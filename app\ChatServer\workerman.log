2025-04-17 11:46:41 pid:1 Error: Call to a member function connection() on null in D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1820
Stack trace:
#0 D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1786): Illuminate\Database\Eloquent\Model::resolveConnection(NULL)
#1 D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1577): Illuminate\Database\Eloquent\Model->getConnection()
#2 D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1496): Illuminate\Database\Eloquent\Model->newBaseQueryBuilder()
#3 D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1532): Illuminate\Database\Eloquent\Model->newModelQuery()
#4 D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1485): Illuminate\Database\Eloquent\Model->newQueryWithoutScopes()
#5 D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2335): Illuminate\Database\Eloquent\Model->newQuery()
#6 D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2347): Illuminate\Database\Eloquent\Model->__call('where', Array)
#7 D:\phpEnv\www\ai_interview\app\Http\Controllers\Api\InterviewChatController.php(15): Illuminate\Database\Eloquent\Model::__callStatic('where', Array)
#8 D:\phpEnv\www\ai_interview\app\ChatServer\Events.php(53): App\Http\Controllers\Api\InterviewChatController::handleConnect('abc123')
#9 [internal function]: Events::onMessage('7f0000010af0000...', '{\r\n  "type": "c...')
#10 D:\phpEnv\www\ai_interview\vendor\workerman\gateway-worker\src\BusinessWorker.php(388): call_user_func('Events::onMessa...', '7f0000010af0000...', '{\r\n  "type": "c...')
#11 D:\phpEnv\www\ai_interview\vendor\workerman\workerman\src\Connection\TcpConnection.php(741): GatewayWorker\BusinessWorker->onGatewayMessage(Object(Workerman\Connection\AsyncTcpConnection), Array)
#12 D:\phpEnv\www\ai_interview\vendor\workerman\workerman\src\Events\Select.php(408): Workerman\Connection\TcpConnection->baseRead(Resource id #66)
#13 D:\phpEnv\www\ai_interview\vendor\workerman\workerman\src\Worker.php(1602): Workerman\Events\Select->run()
#14 D:\phpEnv\www\ai_interview\vendor\workerman\workerman\src\Worker.php(1527): Workerman\Worker::forkWorkersForWindows()
#15 D:\phpEnv\www\ai_interview\vendor\workerman\workerman\src\Worker.php(593): Workerman\Worker::forkWorkers()
#16 D:\phpEnv\www\ai_interview\app\ChatServer\start_businessworker.php(34): Workerman\Worker::runAll()
#17 {main}
2025-04-17 11:56:03 pid:1 Exception: $message_data['room_id'] not set. client_ip:127.0.0.1 $message:{
  "type": "pong",
  "token": "abc123"
} in D:\phpEnv\www\ai_interview\app\ChatServer\Events.php:66
Stack trace:
#0 [internal function]: Events::onMessage('7f0000010af0000...', '{\r\n  "type": "p...')
#1 D:\phpEnv\www\ai_interview\vendor\workerman\gateway-worker\src\BusinessWorker.php(388): call_user_func('Events::onMessa...', '7f0000010af0000...', '{\r\n  "type": "p...')
#2 D:\phpEnv\www\ai_interview\vendor\workerman\workerman\src\Connection\TcpConnection.php(741): GatewayWorker\BusinessWorker->onGatewayMessage(Object(Workerman\Connection\AsyncTcpConnection), Array)
#3 D:\phpEnv\www\ai_interview\vendor\workerman\workerman\src\Events\Select.php(408): Workerman\Connection\TcpConnection->baseRead(Resource id #66)
#4 D:\phpEnv\www\ai_interview\vendor\workerman\workerman\src\Worker.php(1602): Workerman\Events\Select->run()
#5 D:\phpEnv\www\ai_interview\vendor\workerman\workerman\src\Worker.php(1527): Workerman\Worker::forkWorkersForWindows()
#6 D:\phpEnv\www\ai_interview\vendor\workerman\workerman\src\Worker.php(593): Workerman\Worker::forkWorkers()
#7 D:\phpEnv\www\ai_interview\app\ChatServer\start_businessworker.php(34): Workerman\Worker::runAll()
#8 {main}
2025-04-17 12:26:04 pid:1 Error: Call to a member function connection() on null in D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1820
Stack trace:
#0 D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1786): Illuminate\Database\Eloquent\Model::resolveConnection(NULL)
#1 D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1577): Illuminate\Database\Eloquent\Model->getConnection()
#2 D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1496): Illuminate\Database\Eloquent\Model->newBaseQueryBuilder()
#3 D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1532): Illuminate\Database\Eloquent\Model->newModelQuery()
#4 D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1485): Illuminate\Database\Eloquent\Model->newQueryWithoutScopes()
#5 D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2335): Illuminate\Database\Eloquent\Model->newQuery()
#6 D:\phpEnv\www\ai_interview\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2347): Illuminate\Database\Eloquent\Model->__call('where', Array)
#7 D:\phpEnv\www\ai_interview\app\Http\Controllers\Api\InterviewChatController.php(15): Illuminate\Database\Eloquent\Model::__callStatic('where', Array)
#8 D:\phpEnv\www\ai_interview\app\ChatServer\Events.php(59): App\Http\Controllers\Api\InterviewChatController::handleConnect('abc123')
#9 [internal function]: Events::onMessage('7f0000010af0000...', '{\r\n  "type": "c...')
#10 D:\phpEnv\www\ai_interview\vendor\workerman\gateway-worker\src\BusinessWorker.php(388): call_user_func('Events::onMessa...', '7f0000010af0000...', '{\r\n  "type": "c...')
#11 D:\phpEnv\www\ai_interview\vendor\workerman\workerman\src\Connection\TcpConnection.php(741): GatewayWorker\BusinessWorker->onGatewayMessage(Object(Workerman\Connection\AsyncTcpConnection), Array)
#12 D:\phpEnv\www\ai_interview\vendor\workerman\workerman\src\Events\Select.php(408): Workerman\Connection\TcpConnection->baseRead(Resource id #66)
#13 D:\phpEnv\www\ai_interview\vendor\workerman\workerman\src\Worker.php(1602): Workerman\Events\Select->run()
#14 D:\phpEnv\www\ai_interview\vendor\workerman\workerman\src\Worker.php(1527): Workerman\Worker::forkWorkersForWindows()
#15 D:\phpEnv\www\ai_interview\vendor\workerman\workerman\src\Worker.php(593): Workerman\Worker::forkWorkers()
#16 D:\phpEnv\www\ai_interview\app\ChatServer\start_businessworker.php(34): Workerman\Worker::runAll()
#17 {main}
