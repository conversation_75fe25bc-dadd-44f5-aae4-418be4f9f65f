<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VisitorApply extends Model
{
    protected $table = 'cna_visitor_apply';
    protected $guarded = [];

    // 状态
    const STATUS_AGREE = 1; //同意
    const STATUS_REJECT = 2; //拒绝

    const STATUS_ARR = [
        self::STATUS_AGREE,
        self::STATUS_REJECT,
    ];

    // 到访目的
    const PLACE_OFFICIAL = 1; //官方会议
    const PLACE_VISITS = 2; //会所参观
    const PLACE_BUSINESS  = 3; //商务沟通
    const PLACE_OTHER = 4; //其他


    const PLACE_ARR = [
        self::PLACE_OFFICIAL,
        self::PLACE_VISITS,
        self::PLACE_BUSINESS,
        self::PLACE_OTHER,
    ];

    const PLACE = [
        1 => '官方会议',
        2 => '会所参观',
        3 => '商务沟通',
        4 => '其他',
    ];

    // 地区
    const LOCATION = [
        'cn' => '国内',
        'my' => '马来西亚',
    ];
}
