<?php
/**
 * This file is part of workerman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link http://www.workerman.net/
 * @license http://www.opensource.org/licenses/mit-license.php MIT License
 */
use \Workerman\Worker;
use \Workerman\WebServer;
use \GatewayWorker\BusinessWorker;
use App\ChatServer\Events;

require_once __DIR__ . '/../../vendor/autoload.php';
// require_once __DIR__ . '/Events.php';

// $app = require_once __DIR__ . '/../../bootstrap/app.php';

// $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
// $kernel->bootstrap();

// bussinessWorker 进程
$worker = new BusinessWorker();
// worker名称
$worker->name = 'OfficeBusinessWorker';
// bussinessWorker进程数量
$worker->count = 4;
// 服务注册地址
$worker->registerAddress = '127.0.0.1:1286';
$worker->eventHandler = 'App\ChatServer\Events';

// 如果不是在根目录启动，则运行runAll方法
if(!defined('GLOBAL_START'))
{
    Worker::runAll();
}

