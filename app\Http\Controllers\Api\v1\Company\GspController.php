<?php

namespace App\Http\Controllers\Api\v1\Company;

use App\Events\GspRegisterEvent;
use App\Http\Controllers\Controller;
use App\Models\CompanyGspReport;
use App\Models\CountryModel;
use App\Models\GspRefundModel;
use App\Models\IcbcOrderModel;
use App\Models\InvoiceDetailModel;
use App\Models\InvoiceModel;
use App\Models\ProfileInfoModel;
use App\Models\ProjectServiceModel;
use App\Services\ICBC\ICBCService;
use App\Services\OssService;
use App\Services\PdfTableService;
use App\Services\SmsService;
use Firebase\JWT\JWT;
use Fpdi\PdfRichText;
use Illuminate\Http\Request;
use App\Models\CompanyGsp;
use App\Models\GSP\GspInvestigationConfig;
use App\Models\GspOption;
use App\Rules\Mobile\CheckMobileRule;
use App\Services\DocService;
use App\Services\GspService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;


class GspController extends Controller
{
    const ACTION = 'gsp_form'; //场景
    const ACTION_COMPANY_LOGIN = 'company_login'; // 企业用户登录场景


    private $smsService;

    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
    }


    public function formOption(Request $request)
    {
        $type = $request->get('type');
        if (!in_array($type, GspOption::TYPE_ARR)) {
            return responseFail();
        }
        $data = GspOption::where('type', $type)->get();

        return responseSuccess($data);
    }

    // 当前正在进行中流程
    public function getCurrentGsp($user, $gsp_id = '')
    {
        $record = [];
        if ($user) {
            $record = CompanyGsp::where('profile_id', $user['profileID'])->where('status', '!=',CompanyGsp::STATUS_ACTIVE)->first();
        } else if ($gsp_id) {
            $record = CompanyGsp::where('id', $gsp_id)->first();
        }

        return $record;
    }

    public function progress(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id =  $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);

        $progress = $this->checkStep($record, $gsp_id);
        return responseSuccess($progress);
    }

    // 判断当前进行中的步骤
    public function checkStep($record, $gsp_id=0)
    {
        $progress = [
            1 => [  // 第一步判断
                'createForm' => 0,  // 生成预审表格
                'uploadForm' => 0,  // 提交预审表格
                'payOne'     => 0,  // 已付款(申请费和预审保证金)
                'commitCheck'=> 0,  // 提交审核
            ],
            2 => [ // 第二步判断
                'formCheck' => 0,  // 预审表审核中
            ],
            3 => [ // 第三步判断
                'printContract'  => 0,  // 打印合同
                'uploadContract' => 0,  // 上传签署合同
                'payTwo'         => 0, // 已付款(企业开户费)
                'nextStep'       => 0 // 下一步
            ],
            4 => [ // 第四步判断
                'contractCheck' => 0,  // 合同审核中
            ],
            5 => [ // 第五步判断
                'postReport' => 0,  // 提交材料
            ],
            6 => [ // 第六步企业审核
                'reportCompanyCheck' => 0, // 企业审核中
            ],
            7 => [ // 第七步判断
                'reportCheck'  => 0, // 材料审核
            ],
            8 => [ // 第八步判断 报告审核
                'reportPartnerPOST' => 0, // 提交审核意见
                'reportCACheck'  => 0, // 尽调报告审核中
            ],
            9 => [ // 第九步判断
                'payThree' => 0, // 已付款(AI账号开通费)
                'success'  => 0
            ],
        ];

        if ($record) {
            // 第一步判断
            if ($record['form']) { // 已生成预审表格
                $progress[1]['createForm'] = 1;
            }

            if ($record['form_complete']) { // 已上传预审表格
                $progress[1]['uploadForm'] = 1;
            }

            if ($record['pay_one'] == 1) { // 已付款第一步
                $progress[1]['payOne'] = 1;
            }

            if ($record['status'] == CompanyGsp::STATUS_UPLOAD_FORM) { // 第一步提交审核
                $progress[1]['commitCheck'] = 1;
            }

            // 第二步判断
            if ($record['status'] == CompanyGsp::STATUS_FORM_FAIL) { // 预审不通过
                $progress[2]['formFail'] = 0;
                $progress[2]['formCheck'] = 1;
                // 是否退款
                if ($record['pay_one_return'] == 1) {
                    $progress[2]['payOneReturn'] = 0;
                }
            } else if ($record['status'] == CompanyGsp::STATUS_FORM_SUCCESS) { // 审核通过
                //$progress[2]['formSuccess'] = 0;
                $progress[2]['formCheck'] = 1;
                $progress[1]['commitCheck'] = 1;

            }
            if ($record['status'] > CompanyGsp::STATUS_FORM_FAIL)  {
                $progress[2]['formCheck'] = 1;
                $progress[1]['commitCheck'] = 1;
            }

            // 第三步判断
            if ($record['contract_url']) {
                $progress[3]['uploadContract'] = 1; // 上传签署合同
            }

            if ($record['is_print_contract'] == 1) { // 打印合同
                $progress[3]['printContract'] = 1;
            }

            if ($record['pay_two'] == 1) { // 已付款(企业开户费)
                $progress[3]['payTwo'] = 1;
            }

            if ($record['status'] == CompanyGsp::STATUS_UPLOAD_CONTRACT) { // 下一步审核
                $progress[3]['nextStep'] = 1;
            }

            // 第四步判断
            if ($record['status'] == CompanyGsp::STATUS_CONTRACT_SUCCESS) { // 审核通过
                $progress[3]['nextStep'] = 1;
                $progress[4]['contractCheck'] = 1;
                //$progress[4]['contractSuccess'] = 0;
            } else if ($record['status'] == CompanyGsp::STATUS_CONTRACT_FAIL) { // 审核失败
                $progress[4]['contractFail'] = 0;
                $progress[4]['contractCheck'] = 1;
            }
            if ($record['status'] > CompanyGsp::STATUS_CONTRACT_FAIL)  {
                $progress[4]['contractCheck'] = 1;
                $progress[3]['nextStep'] = 1;
            }

            // 第五步判断
            if ($record['status'] == CompanyGsp::STATUS_POST_REPORT) {
                $progress[5]['postReport'] = 1;
            }

            // 第六步判断
            if ($record['status'] == CompanyGsp::STATUS_COMPANY_REPORT_SUCCESS) { // 企业审核通过
                $progress[6]['reportCompanyCheck'] = 1;
                $progress[5]['postReport'] = 1;
            } else if ($record['status'] == CompanyGsp::STATUS_COMPANY_REPORT_FAIL) { // 企业审核不通过

                if ($gsp_id > 0) {// 企业还是第六步
                    $progress[5]['postReport'] = 1;
                    $progress[6]['reportCompanyCheck'] = 1;
                    $progress[6]['reportCompanyFail'] = 0;
                } else { // 合伙人是回到第五步
                    $progress[6]['reportCompanyCheck'] = 1;
                    $progress[6]['reportCompanyFail'] = 0;
                }

            }


            // 第七步判断
            if ($record['status'] == CompanyGsp::STATUS_REPORT_SUCCESS) { // 材料审核通过
                $progress[7]['reportCheck'] = 1;
                $progress[6]['reportCompanyCheck'] = 1;
                $progress[5]['postReport'] = 1;
            } else if ($record['status'] == CompanyGsp::STATUS_REPORT_FAIL) { // 材料审核不通过
                $progress[7]['reportCheck'] = 1;
                $progress[7]['reportFail'] = 0;
            }


            // 第八步判断
            if (in_array($record['is_report_partner_post'], [1,2])) { // 提交审核意见
                $progress[8]['reportPartnerPOST'] = 1;
            } else {
                $progress[8]['reportCACheck'] = 1;
            }

            if ($record['status'] == CompanyGsp::STATUS_REPORT_CA_FAIL) { // 尽调审核不通过
                $progress[8]['reportCACheck'] = 1;
            } else if ($record['status'] == CompanyGsp::STATUS_REPORT_CA_SUCCESS) {
                $progress[8]['reportCACheck'] = 1;
                $progress[5]['postReport'] = 1;
                $progress[6]['reportCompanyCheck'] = 1;
                $progress[7]['reportCheck'] = 1;
            }

            // 第九步
            if ($record['pay_three'] == 1) {
                $progress[9]['payThree'] = 1;
            }
        }


        // 过滤掉已设置步骤
        foreach ($progress as $key=>$val) {
            $progress[$key] = array_keys(array_filter($val, function($value){
                return $value==0;
            }));
            if(empty($progress[$key])) unset($progress[$key]);
        }

        return $progress;
    }

    /**
     * 预审表格(保存草稿)
     * @param Request $request
     * @return null
     */
    public function apply(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'register_capital'  => 'numeric',
            'paid_capital'      => 'numeric',
            'annual_revenue'    => 'numeric',
            'annual_cost'       => 'numeric',
            'total_assets'      => 'numeric',
            'total_liability'   => 'numeric',
            'annual_trade'      => 'numeric',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $user = $request->attributes->get('user');
        $body = $request->all();
        $body = array_map(function ($value) {
            return empty($value) ? '' : $value;
        }, $body);

        // 判断手机号是否重复
        if (isset($body['phone']) && $body['phone']) {
            $checkPhone = CompanyGsp::where('phone', $body['phone'])->first();
            if ($checkPhone) {
                return responseFail(__('team phone exist'));
            }
        }

        // 判断邮箱唯一性用于创建账号使用
        if (isset($body['email']) && $body['email']) {
            $emailExist = ProfileInfoModel::where('profileEmail', $body['email'])->first();
            if ($emailExist) {
                return responseFail(__('team email exist'));
            }
        }

        // 当前正在进行的入驻申请
        $record = $this->getCurrentGsp($user, '');
        if ($record) {
            $body['status'] = 0;
            $body['form'] = '';
            $body['form_complete'] = '';
            CompanyGsp::where('profile_id', $user['profileID'])->update($body);
        } else {

            $body['profile_id'] = $user['profileID'];
            CompanyGsp::create($body);
        }

        return responseSuccess();
    }

    /**
     * 查看预审表格数据
     * @param Request $request
     * @return void
     */
    public function applyDetail(Request $request)
    {
        $user = $request->attributes->get('user');
        $record = $this->getCurrentGsp($user);

        if (empty($record)) {
            return responseSuccess();
        }
        $data = [
            'name'              => $record['name'],
            'credit_code'       => $record['credit_code'],
            'register_capital'  => $record['register_capital'],
            'paid_capital'      => $record['paid_capital'],
            'desc'              => $record['desc'],
            'main_business'     => $record['main_business'],
            'related_business'  => $record['related_business'],
            'customer'          => $record['customer'],
            'annual_revenue'    => $record['annual_revenue'],
            'annual_cost'       => $record['annual_cost'],
            'total_assets'      => $record['total_assets'],
            'total_liability'   => $record['total_liability'],
            'contact_name'      => $record['contact_name'],
            'contact_position'  => $record['contact_position'],
            'phone'             => $record['phone'],
            'email'             => $record['email'],
            'economic_behavior' => $record['economic_behavior'],
            'industry_group'    => $record['industry_group'],
            'annual_trade'      => $record['annual_trade'],
            'target_country'    => $record['target_country'],
        ];

        if ($record['industry_group']) {
            $data['industry_group'] = explode(',', $record['industry_group']);
        }
        if ($record['target_country']) {
            $data['target_country'] = explode(',', $record['target_country']);
        }

        return responseSuccess($data);

    }

    /**
     * 生成预审表格PDF
     * @return void
     */
    public function createForm(Request $request)
    {
        $body = $request->all();
        $user = $request->attributes->get('user');
        $validator = Validator::make($request->all(), [
            'name'              => 'required',
            'credit_code'       => 'required',
            'register_capital'  => ['required', 'numeric'],
            'paid_capital'      => ['required', 'numeric'],
            'desc'              => 'required',
            'main_business'     => 'required',
            'related_business'  => 'required',
            'customer'          => 'required',
            'annual_revenue'    => ['required', 'numeric'],
            'annual_cost'       => ['required', 'numeric'],
            'total_assets'      => ['required', 'numeric'],
            'total_liability'   => ['required', 'numeric'],
            'contact_name'      => 'required',
            'contact_position'  => 'required',
            'phone'             => ['required',new CheckMobileRule],
            'email'             => ['required', 'email'],
            'economic_behavior' => 'required',
            'industry_group'    => 'required',
            'annual_trade'      =>  ['required', 'numeric'],
            'target_country'    => 'required',
            'link'              => 'required'
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        // 判断手机号是否重复
        if (isset($body['phone']) && $body['phone']) {
            $checkPhone = CompanyGsp::where('phone', $body['phone'])->first();
            if ($checkPhone) {
                return responseFail(__('team phone exist'));
            }
        }

        // 判断邮箱唯一性用于创建账号使用
        if (isset($body['email']) && $body['email']) {
            $emailExist = ProfileInfoModel::where('profileEmail', $body['email'])->first();
            if ($emailExist) {
                return responseFail(__('team email exist'));
            }
        }


        // 二维码链接
        //$body['link'] = $body['link'];

        // step1. 生成预审表格
        $form = (new GspService)->getForm($body);

        // step2. 更新信息
        unset($body['link']);
        $body['form'] = $form;
        $body['status'] = 0;
        $body['form_complete'] = '';

        $record = $this->getCurrentGsp($user);
        if ($record) {
            CompanyGsp::where('profile_id', $user['profileID'])->update($body);
        } else {

            $body['profile_id'] = $user['profileID'];
            CompanyGsp::create($body);
        }

        $token = DocService::generateToken($form, self::ACTION, 3600);
        return responseSuccess($token);

    }


    // 上传预审表格
    public function uploadForm(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $body = $request->all();
        $rules = [
            'file' => ['file', 'mimes:pdf,jpg,jpeg,png,webp', 'max:'.(env('ALLOW_FILE_SIZE')*1024)],
        ];
        $messages = [
            'file.mimes' => __('incorrect format img'),
            'file.max' => __('exceed size img', ['limit' => env('ALLOW_FILE_SIZE').'M']),
        ];
        $validator = Validator::make($body, $rules, $messages);
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            return responseFail($errors);
        }

        $file = $request->file('file');
        if (empty($file)) {
            return responseFail();
        }

        $resource = OssService::upload($file);
       // $resource = $file->store('gsp', 'public');
        if ($resource) {
            $form_complete = $resource;
        } else {
            return responseFail();
        }

        // 正在进行中申请
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail(__('info no exist'));
        }

       $result = CompanyGsp::where('id', $record['id'])->update([
           'form_complete' => $form_complete
       ]);
        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail();
        }

    }

    /**
     * 重新上传预审表格
     * @return void
     */
    public function reloadForm(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        // 正在进行中申请
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail(__('info no exist'));
        }

        $result = CompanyGsp::where('id', $record['id'])->update([
            'form_complete' => ''
        ]);
        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail();
        }
    }

    /**
     * 第一步提交审核
     * @return void
     */
    public function formCheck(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        // 正在进行中申请
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail(__('info no exist'));
        }
        $result = CompanyGsp::where('id', $record['id'])->update([
            'status' => CompanyGsp::STATUS_UPLOAD_FORM,
            'form_post_date' => date('Y-m-d H:i:s')
        ]);
        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail();
        }
    }

    // 打印合同
    public function printContract(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail(__('info no exist'));
        }
        // 判断前面步骤是否走完
/*        $ret = $this->checkStep($record);
        if (isset($ret[1]) || isset($ret[2])) {
            return responseFail();
        }*/


        $result = CompanyGsp::where('id', $record['id'])->update([
            'is_print_contract' => 1
        ]);


        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail();
        }

    }

    // 上传合同
    public function uploadContract(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $body = $request->all();
        $rules = [
            'file' => ['required','file', 'mimes:pdf,jpg,jpeg,png,webp', 'max:'.(env('ALLOW_FILE_SIZE')*1024)],
        ];
        $messages = [
            'file.mimes' => __('incorrect format img'),
            'file.max' => __('exceed size img', ['limit' => env('ALLOW_FILE_SIZE').'M']),
        ];
        $validator = Validator::make($body, $rules, $messages);
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            return responseFail($errors);
        }

        $file = $request->file('file');
        if (empty($file)) {
            return responseFail();
        }

        //$resource = $file->store('gsp', 'public');
        $resource = OssService::upload($file);
        if ($resource) {
            $contract_url = $resource;
        } else {
            return responseFail();
        }

        // 正在进行中申请
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail(__('info no exist'));
        }
        $result = CompanyGsp::where('id', $record['id'])->update([
            'contract_url'  => $contract_url
        ]);
        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail();
        }
    }

    // 重新上传合同
    public function reloadContract(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        // 正在进行中申请
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail(__('info no exist'));
        }
        $result = CompanyGsp::where('id', $record['id'])->update([
            'contract_url'  => ''
        ]);
        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail();
        }
    }


    /**
     * 合同提交审核
     * @return void
     */
    public function contractCheck(Request $request)
    {
        $user = $request->attributes->get('user');
        // 企业信息
        $info = $this->getCurrentGsp($user);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }
        // 正在进行中申请
        $result = CompanyGsp::where('id', $info['id'])->update([
            'status' => CompanyGsp::STATUS_UPLOAD_CONTRACT,
            'contract_post_date' => date('Y-m-d H:i:s')
        ]);
        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail();
        }
    }

    /**
     * 审核结果
     * @param Request $request
     * @return void
     */
    public function checkResult(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $type = $request->input('type');

        // 企业信息
        $info = $this->getCurrentGsp($user, $gsp_id);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        $data = [];
        if ($type == 1) {
            if ($info['status'] == 1) { // 审核中
                $data = [
                    'check_date'    => $info['form_post_date']??'',
                    'reject_reason' => '',
                ];
            } else if ($info['status'] == 3) {
                $data = [
                    'check_date'    => $info['form_check_date']??'',
                    'reject_reason' => $info['form_reject_reason'],
                ];
            }

        } else if ($type == 2) {
            if ($info['status'] == 4) {
                $data = [
                    'check_date'    => $info['contract_post_date']??'',
                    'reject_reason' => '',
                ];
            } else if ($info['status'] == 6) {
                $data = [
                    'check_date'    => $info['contract_check_date']??'',
                    'reject_reason' => $info['contract_reject_reason'],
                ];
            }

        } else if ($type == 3) {
            if ($info['status'] == 10) {
                $data = [
                    'check_date'    => $info['report_post_date']??'',
                    'reject_reason' => '',
                ];
            } else if ($info['status'] == 11) {
                $data = [
                    'check_date'    => $info['report_check_date']??'',
                    'reject_reason' => $info['report_reject_reason'],
                ];
            }

        }

        return responseSuccess($data);

    }

    /**
     * 上传尽调报告
     * @param Request $request
     * @return void
     */
    public function uploadReport(Request $request)
    {
        //$gsp_id = $request->attributes->get('gsp_id');
        $user = $request->attributes->get('user');
        $files = $request->file('file');
        $filename = $request->input('filename');


        // 检查是否有上传文件
        if ($request->hasFile('file')) {
            $rule       = ['required'];
            $mimes      = [];
            $max        = 0;        
            $maxNumber  = 0;        
            $configId   = 0;
            $mimeMap    = [
                'image/png' => 'png',
                'image/jpeg' => 'jpeg',
                'image/jpg' => 'jpg',
                'application/pdf' => 'pdf',
                'application/msword' => 'doc',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx',
            ];
            if (preg_match('/\d+/', $filename, $matches)) {
                $configId = $matches[0];
            }
            $configStr = GspInvestigationConfig::where([
                    ['id', '=', $configId]
            ])->value('allow_upload_file_config');
            if(!empty($configStr)){
                $configArr  = json_decode($configStr, true);
                $max        = $configArr['max_size'];
                $maxNumber  = $configArr['number'];
                foreach($configArr['mime'] as $value){
                    $mimes[] = $mimeMap[$value];
                }
                $mimes      = implode(',', $mimes);
                $rule[]     = "mimes:{$mimes}";
                if($max) $rule[] = "max:{$max}";
                $rule       = implode('|', $rule);    
                if($maxNumber && count($files) > $maxNumber) return responseFail('ERROR');
                // 如果是多文件上传，则循环每个文件
                foreach ($files as $file) {
                    $validator = Validator::make(
                        ['file' => $file],
                        ['file' => $rule], // 2048 KB = 2 MB
                        [
                            'mimes' => 'The :attribute must be a file of type: :values.',
                            'max' => 'The :attribute may not be greater than :max kilobytes.'
                        ]
                    );
                    if ($validator->fails()) {
                        return responseFail($validator->errors()->getmessages()['file'][0]);
                    }
                }
            }
            // 企业信息
            $info = $this->getCurrentGsp($user);
            if (empty($info)) {
                return responseFail(__('info no exist'));
            }

            if ( empty($filename)) {
                return responseFail(__('missing parameter', ['param' => 'filename']));
            }

            $file_id = str_replace('file', '', $filename);
            $gsp_id = $info['id'];

            if ($user) {
                $file_owner = 1; // 合伙人
            } else {
                $file_owner = 2; // 企业
            }

            $data = [];
            if ($files) {
                foreach ($files as $file) {
                    $resource = OssService::upload($file);
                    //$resource = $file->store('gsp/report', 'public');
                    // 命名格式：企业id+文件名+上传时间的年月日
                   // $filename = $file->getClientOriginalName();
                    $filename = GspInvestigationConfig::where('id', $file_id)->value('category_zh_cn');
                    $filename = $gsp_id.'-'.$filename.'-'.date('Ymd');
                    $resource && $data[] = [
                        'gsp_id'     => $info['id'],
                        'profile_id' => $info['profile_id'],
                        'file_path'  => $resource,
                        'file_id'    => $file_id,
                        'file_owner' => $file_owner,
                        'file_name'  => $filename,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                }
            }
            try {
                // 开启事务
                DB::beginTransaction();

                // step1 删除文件
            /* $oldFile = CompanyGspReport::where('gsp_id', $gsp_id)->where('file_id', $file_id)->where('file_owner', $file_owner)->first();
                if ($oldFile) {
                    CompanyGspReport::where('gsp_id', $gsp_id)->where('file_id', $file_id)->where('file_owner', $file_owner)->delete();
                }*/


                // step2 重新上传
                if ($data) {
                    CompanyGspReport::insert($data);

                }

                // 提交
                DB::commit();

                // 返回前端路径和ID
                $arrFilePath = collect($data)->transform(function ($item) {
                    return collect($item)->only(['file_path'])->toArray();
                });

                $insertFile = CompanyGspReport::select('id', 'file_path', 'file_name')->whereIn('file_path', $arrFilePath)->get();

                return responseSuccess($insertFile);

            } catch (\Exception $e) {
                DB::rollBack();

                return responseFail($e->getMessage());
            }
        }
    }

    /**
     * 删除尽调文件
     * @param Request $request
     * @return void
     */
    public function delReport(Request $request)
    {
        $file_id = $request->input('file_id');
        $user = $request->attributes->get('user');

        if ( empty($file_id)) {
            return responseFail(__('missing parameter', ['param' => 'file_id']));
        }


        // 企业信息
        $info = $this->getCurrentGsp($user);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        $gsp_id = $info['id'];

        $fileInfo = CompanyGspReport::where('gsp_id', $gsp_id)->where([
            ['id', '=',$file_id],
            ['status', '=',CompanyGspReport::STATUS_FILE_NO],
        ])->first();
        if (empty($fileInfo)) {
            return responseFail();
        }

        $fileInfo->delete();

        return responseSuccess();

    }

    /**
     * 尽调报告(保存草稿)
     * @param Request $request
     * @return void
     */
    public function applyDraft(Request $request)
    {
        //$gsp_id = $request->attributes->get('gsp_id');
        $user = $request->attributes->get('user');

        /*$body = $request->all();
        Log::info(json_encode($body));*/
        // 企业信息
        $info = $this->getCurrentGsp($user);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        if ($user) {
            $file_owner = 1; // 合伙人
        } else {
            $file_owner = 2; // 企业
        }
        $data = [];
        for ($i = 1; $i <= 23; $i++) {
            $files = $request->file('file'.$i);
            if ($files) {
                foreach ($files as $file) {
                   // $resource = OssService::upload($file);
                    $resource = $file->store('gsp/report', 'public');
                    $resource && $data[$i][] = $resource;
                }

            }
        }


        $gsp_id = $info['id'];
        $updateData = [];
        if ($data) {
            foreach ($data as $key => $val) {
                foreach ($val as $v) {
                    $updateData[] = [
                        'gsp_id'     => $gsp_id,
                        'profile_id' => $info['profile_id'],
                        'file_path'  => $v,
                        'file_id'    => $key,
                        'file_owner' => $file_owner,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }

            }
        }


        try {
            // 开启事务
            DB::beginTransaction();

            // step1 删除文件
            CompanyGspReport::where('gsp_id', $gsp_id)->where('file_owner', $file_owner)->delete();

            // step2 重新上传
            if ($updateData) {
                CompanyGspReport::insert($updateData);

            }

            // 提交
            DB::commit();

            return responseSuccess();

        } catch (\Exception $e) {
            DB::rollBack();

            return responseFail($e->getMessage());
        }


    }

    /**
     * 提交尽调报告
     * @param Request $request
     * @return void
     */
    public function applyReport(Request $request)
    {
        //$gsp_id = $request->attributes->get('gsp_id');
        $user = $request->attributes->get('user');

        // 企业信息
        $info = $this->getCurrentGsp($user);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        $gsp_id = $info['id'];

       /* $validator = Validator::make($request->all(), [
            'file1' => 'required',
            'file2' => 'required',
            'file3' => 'required',
            'file4' => 'required',
            'file5' => 'required',
            'file6' => 'nullable',
            'file7' => 'required',
            'file8' => 'required',
            'file9' => 'required',
            'file10' => 'required',
            'file11' => 'required',
            'file12' => 'required',
            'file13' => 'required',
            'file14' => 'required',
            'file15' => 'required',
            'file16' => 'required',
            'file17' => 'required',
            'file18' => 'required',
            'file19' => 'required',
            'file20' => 'required',
            'file21' => 'required',
            'file22' => 'required',
            'file23' => 'nullable'
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        if ($user) {
            $file_owner = 1; // 合伙人
        } else {
            $file_owner = 2; // 企业
        }

        $data = [];
        for ($i = 1; $i <= 23; $i++) {
            $files = $request->file('file'.$i);
            if ($files) {
                foreach ($files as $file) {
                   //$resource = OssService::upload($file);
                    $resource = $file->store('gsp/report', 'public');
                    $resource && $data[$i][] = $resource;
                }

            }
        }
        

        if ($data) {
            foreach ($data as $key => $val) {
                foreach ($val as $v) {
                    $updateData[] = [
                        'gsp_id'     => $gsp_id,
                        'profile_id' => $info['profile_id'],
                        'file_path'  => $v,
                        'file_id'    => $key,
                        'file_owner' => $file_owner
                    ];
                }

            }
        }*/

        // 判断是否必填都填了
        $reportData = CompanyGspReport::where('gsp_id', $gsp_id)->pluck('file_id')->toArray();
        $explode = [6, 23];
        for ($i = 1; $i <= 23; $i++) {
           if (!in_array($i, $reportData) && !in_array($i, $explode)) {
                return responseFail(__('please upload complete file'));
           }
        }

        try {
            // 开启事务
            DB::beginTransaction();

            // step1 删除文件
          //  CompanyGspReport::where('gsp_id', $gsp_id)->where('file_owner', $file_owner)->delete();

            // step2 重新上传
          //  CompanyGspReport::insert($updateData);

            // step3 更新提交状态
            CompanyGsp::where('id', $gsp_id)->update([
                'status' => CompanyGsp::STATUS_POST_REPORT,
                'report_post_date' => date('Y-m-d H:i:s')
            ]);

            // 提交
            DB::commit();

            return responseSuccess();

        } catch (\Exception $e) {
            DB::rollBack();

            return responseFail($e->getMessage());
        }

    }

    /**
     * 查看尽调报告
     * @param Request $request
     * @return void
     */
    public function reportDetail(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        // 企业信息
        $info = $this->getCurrentGsp($user, $gsp_id);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        $gsp_id = $info['id'];

        // 查看尽调报告文件
        $data = CompanyGspReport::where('gsp_id', $gsp_id)->get();
        $result = [];
        if ($data) {
            foreach ($data as $val) {
                $result['file'.$val['file_id']]['file'][] = [
                    'file_name' => $val['file_name'],
                    'file_path' => $val['file_path'],
                    'file_owner'=> $val['file_owner'],
                    'created_at'=> $val['created_at']
                ];
                $result['file'.$val['file_id']]['status'] = $val['status'];
            }
        }

        return responseSuccess($result);
    }

    /**
     * 生成付款第一步二维码
     * @return void
     */
    public function createPayOneQRcode(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        // 企业信息
        $info = $this->getCurrentGsp($user, $gsp_id);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        if ($info['pay_one'] == 1) {
            return responseFail(__('gsp register paid one'));
        }

        // 生成订单

        // 申请费+预审保证金
        $total = ProjectServiceModel::whereIn('id', [ ProjectServiceModel::PAY_PRE_BOND, ProjectServiceModel::PAY_COMPANY_APPLY])->sum('price');
        if (!$total) return responseFail();

        if (env('APP_ENV') == 'product') {
            $total = $total * 100;
        } else {
            $total = 0.01 * 100;
        }

        $outTradeNo = ICBCService::generateOutTradeNo($info['profile_id']);

        // 调用ICBC接口
        $domail =   $request->getSchemeAndHttpHost();
        $notify =  $domail.'/api/v1/gsp/notifyPayOne';
        $result = ICBCService::generatePayQRcode($outTradeNo, $total, $notify);
        if ($result) {

            //创建订单
            $ret = IcbcOrderModel::query()->create([
                'mer_id'        => config('icbc.mer_id'),
                'app_id'        => config('icbc.app_id'),
                'user_id'       => $gsp_id > 0 ? 0: $user['profileID'], // 企业的话为0; 合伙人则合伙人ID
                'obj_id'        => $info['id'],
                'qrcode_msg_id' => $result['msg_id'],
                'out_trade_no'  => $outTradeNo,
                'qrcode'        => $result['qrcode'],
                'total_amt'     => $total,
                'attach'        => $result['attach'],
                'type'          => IcbcOrderModel::TYPE_GSP_REGISTER,
                'remark'        => '入驻平台申请费用和资格预审保证金',
                'project'       => ProjectServiceModel::PAY_PRE_BOND.','.ProjectServiceModel::PAY_COMPANY_APPLY,  // 预备保证金
            ]);

            if (!$ret) {
                return responseFail('GENERATE_ORDER_ERROR');
            }

            $img = \SimpleSoftwareIO\QrCode\Facades\QrCode::format('png')->size(150)->generate($result['qrcode']);
            $img = 'data:image/png;base64,' . base64_encode($img);
            return responseSuccess(['qrcode'=>$img]);
        }

    }

    /**
     * 付款第一步成功回调
     * @return bool
     */
    public function notifyPayOne(Request $request)
    {
        $queryString = file_get_contents("php://input");

        // 解码查询字符串
        $decodedString = urldecode($queryString);

        // 将查询字符串解析为数组
        parse_str($decodedString, $data);

        $response_biz_content = isset($data['biz_content'])?json_decode($data['biz_content'], true):'';

        if ($response_biz_content && $response_biz_content['return_code'] == 0) {
            // 回调处理
            $order = IcbcOrderModel::query()->where('out_trade_no', $response_biz_content['out_trade_no'])->first();
            if ($order) {

                if ($order->pay_status == 0) {
                    try {
                        // 开启事务
                        DB::beginTransaction();

                        $gspId = $order->obj_id;

                        // step1 更新支付订单信息
                        $order->pay_msg_id = $response_biz_content['msg_id'];
                        $order->order_id = $response_biz_content['order_id'];
                        $order->pay_time = $response_biz_content['pay_time'];
                        $order->pay_status = 1;
                        $order->cust_id = $response_biz_content['cust_id'];
                        $order->card_no = $response_biz_content['card_no'];
                        $order->bank_name = $response_biz_content['bank_name'] ?? '';
                        $order->channel = $response_biz_content['channel'];
                        $order->attach = $response_biz_content['attach'];
                        $order->tp_cust_id = $response_biz_content['tp_cust_id'] ?? '';
                        $order->trx_ser_no = $response_biz_content['trx_ser_no'] ?? '';
                        $order->tp_order_id = $response_biz_content['tp_order_id'] ?? '';
                        $order->sub_open_id = $response_biz_content['sub_open_id'] ?? '';
                        $order->bank_type = $response_biz_content['bank_type'] ?? '';
                        $order->tp_user_id = $response_biz_content['tp_user_id'] ?? '';
                        $order->save();


                        // step2 更新付款成功状态
                        CompanyGsp::where('id', $gspId)->update(['pay_one' => 1]);

                        // 提交
                        DB::commit();

                        return true;

                    } catch (\Exception $e) {
                        DB::rollBack();

                        Log::info($e->getMessage());
                        return false;
                    }
                } else if ($order->pay_status == 1) {
                    return true;
                }

            }

        }

        return false;
    }

    /**
     * 第一步付款要求退款
     * @param Request $request
     * @return void
     */
    public function payOneReturn(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        // 企业信息
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail(__('info no exist'));
        }

        if ($record['status'] > 3 ) {
            return responseFail(__('param error', ['param'=>'status']));
        }


        try {
            // 开启事务
            DB::beginTransaction();

            // step1 更改为退款状态
            CompanyGsp::where('id', $record['id'])->update(['pay_one_return' => 1]);

            // step2 退款记录

            // 退款金额
            $amount = IcbcOrderModel::query()
                            ->where('project',  ProjectServiceModel::PAY_PRE_BOND.','.ProjectServiceModel::PAY_COMPANY_APPLY)
                            ->where('obj_id', $record['id'])
                            ->where('type', IcbcOrderModel::TYPE_GSP_REGISTER)
                            ->where('pay_status', 1)
                            ->value('total_amt');

            $amount = bcdiv($amount, 100, 2);

            GspRefundModel::insert([
                'gsp_id'     => $record['id'],
                'profile_id' => $record['profile_id'],
                'amount'     => $amount,
                'status'     => 0,
            ]);

            // 提交
            DB::commit();

            return responseSuccess();

        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail();
        }

    }

    /**
     * 生成付款第二步二维码
     * @return void
     */
    public function createPayTwoQRcode(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        // 企业信息
        $info = $this->getCurrentGsp($user, $gsp_id);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        if ($info['pay_two'] == 1) {
            return responseFail(__('gsp register paid two'));
        }

        // 生成订单

        // 企业开户费
        $total = ProjectServiceModel::where('id', ProjectServiceModel::PAY_COMPANY_OPEN)->value('price');
        if (!$total) return responseFail();

        if (env('APP_ENV') == 'product') {
            $total = $total * 100;
        } else {
            $total = 0.01 * 100;
        }

        $outTradeNo = ICBCService::generateOutTradeNo($info['profile_id']);

        // 调用ICBC接口
        $domail =   $request->getSchemeAndHttpHost();
        $notify =  $domail.'/api/v1/gsp/notifyPayTwo';
        $result = ICBCService::generatePayQRcode($outTradeNo, $total, $notify);
        if ($result) {

            //创建订单
            $ret = IcbcOrderModel::query()->create([
                'mer_id'        => config('icbc.mer_id'),
                'app_id'        => config('icbc.app_id'),
                'user_id'       => $gsp_id > 0 ? 0: $user['profileID'], // 企业的话为0; 合伙人则合伙人ID
                'obj_id'        => $info['id'],
                'qrcode_msg_id' => $result['msg_id'],
                'out_trade_no'  => $outTradeNo,
                'qrcode'        => $result['qrcode'],
                'total_amt'     => $total,
                'attach'        => $result['attach'],
                'type'          => IcbcOrderModel::TYPE_GSP_REGISTER,
                'remark'        => '企业入驻平台开户费(一次性)',
                'project'       => ProjectServiceModel::PAY_COMPANY_OPEN,  // 企业开户费
            ]);

            if (!$ret) {
                return responseFail('GENERATE_ORDER_ERROR');
            }

            $img = \SimpleSoftwareIO\QrCode\Facades\QrCode::format('png')->size(150)->generate($result['qrcode']);
            $img = 'data:image/png;base64,' . base64_encode($img);
            return responseSuccess(['qrcode'=>$img]);
        }

    }

    /**
     * 付款第二步成功回调
     * @return bool
     */
    public function notifyPayTwo(Request $request)
    {
        $queryString = file_get_contents("php://input");

        // 解码查询字符串
        $decodedString = urldecode($queryString);

        // 将查询字符串解析为数组
        parse_str($decodedString, $data);

        $response_biz_content = isset($data['biz_content'])?json_decode($data['biz_content'], true):'';

        if ($response_biz_content && $response_biz_content['return_code'] == 0) {
            // 回调处理
            $order = IcbcOrderModel::query()->where('out_trade_no', $response_biz_content['out_trade_no'])->first();
            if ($order) {

                if ($order->pay_status == 0) {
                    try {
                        // 开启事务
                        DB::beginTransaction();

                        $gspId = $order->obj_id;
                        $gspCompany = CompanyGsp::where('id', $gspId)->first();

                        // step1 更新支付订单信息
                        $order->pay_msg_id = $response_biz_content['msg_id'];
                        $order->order_id = $response_biz_content['order_id'];
                        $order->pay_time = $response_biz_content['pay_time'];
                        $order->pay_status = 1;
                        $order->cust_id = $response_biz_content['cust_id'];
                        $order->card_no = $response_biz_content['card_no'];
                        $order->bank_name = $response_biz_content['bank_name'] ?? '';
                        $order->channel = $response_biz_content['channel'];
                        $order->attach = $response_biz_content['attach'];
                        $order->tp_cust_id = $response_biz_content['tp_cust_id'] ?? '';
                        $order->trx_ser_no = $response_biz_content['trx_ser_no'] ?? '';
                        $order->tp_order_id = $response_biz_content['tp_order_id'] ?? '';
                        $order->sub_open_id = $response_biz_content['sub_open_id'] ?? '';
                        $order->bank_type = $response_biz_content['bank_type'] ?? '';
                        $order->tp_user_id = $response_biz_content['tp_user_id'] ?? '';
                        $order->save();


                        // step2 更新付款成功状态
                        CompanyGsp::where('id', $gspId)->update(['pay_two' => 1]);

                        // 提交
                        DB::commit();

                        return true;

                    } catch (\Exception $e) {
                        DB::rollBack();

                        Log::info($e->getMessage());
                        return false;
                    }
                } else if ($order->pay_status == 1) {
                    return true;
                }

            }

        }

        return false;
    }


    /**
     * 生成付款第三步二维码
     * @return void
     */
    public function createPayThreeQRcode(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        // 企业信息
        $info = $this->getCurrentGsp($user, $gsp_id);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        if ($info['pay_three'] == 1) {
            return responseFail(__('gsp register paid three'));
        }

        // 生成订单

        // AI开通费
        $total = ProjectServiceModel::where('id', ProjectServiceModel::PAY_COMPANY_AIOPEN)->value('price');
        if (!$total) return responseFail();

        if (env('APP_ENV') == 'product') {
            $total = $total * 100;
        } else {
            $total = 0.01 * 100;
        }

        $outTradeNo = ICBCService::generateOutTradeNo($info['profile_id']);

        // 调用ICBC接口
        $domail =   $request->getSchemeAndHttpHost();
        $notify =  $domail.'/api/v1/gsp/notifyPayThree';
        $result = ICBCService::generatePayQRcode($outTradeNo, $total, $notify);
        if ($result) {

            //创建订单
            $ret = IcbcOrderModel::query()->create([
                'mer_id'        => config('icbc.mer_id'),
                'app_id'        => config('icbc.app_id'),
                'user_id'       => $gsp_id > 0 ? 0: $user['profileID'], // 企业的话为0; 合伙人则合伙人ID
                'obj_id'        => $info['id'],
                'qrcode_msg_id' => $result['msg_id'],
                'out_trade_no'  => $outTradeNo,
                'qrcode'        => $result['qrcode'],
                'total_amt'     => $total,
                'attach'        => $result['attach'],
                'type'          => IcbcOrderModel::TYPE_GSP_REGISTER,
                'remark'        => 'AI账号开通费',
                'project'       => ProjectServiceModel::PAY_COMPANY_AIOPEN,  // 企业开户费
            ]);

            if (!$ret) {
                return responseFail('GENERATE_ORDER_ERROR');
            }

            $img = \SimpleSoftwareIO\QrCode\Facades\QrCode::format('png')->size(150)->generate($result['qrcode']);
            $img = 'data:image/png;base64,' . base64_encode($img);
            return responseSuccess(['qrcode'=>$img]);
        }

    }

    /**
     * 付款第三步成功回调
     * @return bool
     */
    public function notifyPayThree(Request $request)
    {
        $queryString = file_get_contents("php://input");

        // 解码查询字符串
        $decodedString = urldecode($queryString);

        // 将查询字符串解析为数组
        parse_str($decodedString, $data);

        $response_biz_content = isset($data['biz_content'])?json_decode($data['biz_content'], true):'';

        if ($response_biz_content && $response_biz_content['return_code'] == 0) {
            // 回调处理
            $order = IcbcOrderModel::query()->where('out_trade_no', $response_biz_content['out_trade_no'])->first();
            if ($order) {

                if ($order->pay_status == 0) {
                    try {
                        // 开启事务
                        DB::beginTransaction();

                        $gspId = $order->obj_id;
                        $gspCompany = CompanyGsp::where('id', $gspId)->first();

                        // step1 更新支付订单信息
                        $order->pay_msg_id = $response_biz_content['msg_id'];
                        $order->order_id = $response_biz_content['order_id'];
                        $order->pay_time = $response_biz_content['pay_time'];
                        $order->pay_status = 1;
                        $order->cust_id = $response_biz_content['cust_id'];
                        $order->card_no = $response_biz_content['card_no'];
                        $order->bank_name = $response_biz_content['bank_name'] ?? '';
                        $order->channel = $response_biz_content['channel'];
                        $order->attach = $response_biz_content['attach'];
                        $order->tp_cust_id = $response_biz_content['tp_cust_id'] ??'';
                        $order->trx_ser_no = $response_biz_content['trx_ser_no'] ?? '';
                        $order->tp_order_id = $response_biz_content['tp_order_id'] ?? '';
                        $order->sub_open_id = $response_biz_content['sub_open_id'] ?? '';
                        $order->bank_type = $response_biz_content['bank_type'] ?? '';
                        $order->tp_user_id = $response_biz_content['tp_user_id'] ?? '';
                        $order->save();


                        // step2 更新付款成功状态
                        CompanyGsp::where('id', $gspId)->update(['pay_three' => 1]);

                        // 提交
                        DB::commit();

                        // 绿智地球入驻成功处理
                        event(new GspRegisterEvent($gspId));


                        return true;

                    } catch (\Exception $e) {
                        DB::rollBack();

                        Log::info($e->getMessage());
                        return false;
                    }
                } else if ($order->pay_status == 1) {
                    return true;
                }

            }

        }

        return false;
    }

    /**
     * 查询是否支付完成
     * @return void
     */
    public function checkPayStatus(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $type = $request->input('type');

        if (empty($type)) {
            return responseFail(__('missing parameter', ['param' => 'type']));
        }

        // 企业信息
        $info = $this->getCurrentGsp($user, $gsp_id);
        if (empty($info)) {
            return responseFail(___('info no exist'));
        }

        $data = 0;
        $field = '';
        if ($type == 1) {
            $data = $info['pay_one'];
            $field = 'pay_one';
        } else if ($type == 2) {
            $data = $info['pay_two'];
            $field = 'pay_two';
        } else if ($type == 3) {
            $data = $info['pay_three'];
            $field = 'pay_three';
        }

        if ($data == 0) {
            // 从工行接口查询
            $ret = IcbcOrderModel::checkPayStatus($info['profile_id']);
            if ($ret) {
                $data = 1;
                // 更新支付状态
                CompanyGsp::where('id', $info['id'])->update([$field=>1]);
            }
        }

        return responseSuccess(['pay_status'=>$data]);
    }


    /**
     * 再次发邮件通知
     * @param Request $request
     * @return void
     */
    public function sendEmail(Request $request)
    {

    }

    /**
     * 企业用户登录
     */
    public function companyLogin(Request $request)
    {
        $phone = $request->input('phone');
        $code = $request->input('code');

        if (empty($phone)) {
            return responseFail(__('missing parameter', ['param' => 'phone']));
        }

        if (empty($code)) {
            return responseFail(__('missing parameter', ['param' => 'code']));
        }

        try {
            //短信验证码登录
            $this->smsService->verifySms($phone, $code, config('sms.verification_code_scene.company_login'));

            // 查找手机号是否存在
            $record = CompanyGsp::where('phone', $phone)->first();

            $payload = [
                'exp' => time() + (24 * 3600),
                'action' => self::ACTION_COMPANY_LOGIN,
                'gsp_id' => $record['id'],
            ];
            $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');
            return responseSuccess(['token' => 'Bearer '.$token]);

        } catch (\Exception $e) {
            return responseFail(__('sms verification code error'));
        }

    }

    //发送验证码
    public function sendCode(Request $request)
    {
        $request->validate([
            'phone' => ['required', new CheckMobileRule],
        ]);
        $body = $request->all();
        $record = CompanyGsp::where('phone', $body['phone'])->first();
        if (!$record) {
            return responseFail(__('account error'));
        }
        //整合区号+手机号
        $resetPhoneData = (new CountryModel())->resetPhone(env('COUNTRY'), $body['phone']);
        $resetPhone = $resetPhoneData['phone'];
        $resetCountryCode = $resetPhoneData['countryCode'];

        return $this->smsService->sendVerificationCodeSms(
            $resetPhone, config('sms.verification_code_scene.company_login'), $resetCountryCode);
    }

    /**
     * 生成文件签名
     * @param Request $request
     * @return null
     */
    public function generateGspFileToken(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $type = $request->input('type');
        $file_path = $request->input('file_path', '');
        if (empty($type)) {
            return responseFail(__('missing parameter', ['param' => 'type']));
        }

        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail($gsp_id);
        }

        $filename = '';
        if ($type == 1) { // 预审表格文件
            $file = $record['form'];
        } else if ($type == 2) { // 用户盖章上传预审表格
            $file = $record['form_complete'];
        } else if ($type == 3) { // 签约合同
            $contractFile = storage_path('pdf/gsp_contract.pdf');
            $newFile = storage_path('app/public/gsp/gsp_contract.pdf');
            copy($contractFile, $newFile);
            $file =  'gsp/gsp_contract.pdf';
        } else if ($type == 4) { // 尽调文件
            if (empty($file_path)) {
                return responseFail(__('missing parameter', ['param' => 'file_path']));
            }
            $file = $file_path;
            $filename = CompanyGspReport::where('file_path', $file)->value('file_name');
        } else if ($type == 5) { // 尽调签收模板
            if (empty($file_path)) {
                return responseFail(__('missing parameter', ['param' => 'file_path']));
            }
            $contractFile = storage_path('pdf/gsp_template/'.$file_path);
            $newFile = storage_path('app/public/gsp/').$file_path;
            copy($contractFile, $newFile);
            $file =  'gsp/'.$file_path;
        } else if ($type == 6) { // 尽调合成文件
            $file = $record['report_url'];
        } else if ($type == 7) { // 尽调合成英文版
            $path = $record['report_url'];

            // 获取去掉扩展名后的文件名
            $filenameWithoutExtension = pathinfo(basename($path), PATHINFO_FILENAME);
            // 获取目录路径
            $directory = dirname($path);
            // 组合完整路径
            $file = "$directory/$filenameWithoutExtension".'_en.pdf';
        }

        if (empty($file)) {
            return responseFail();
        }

        $token = DocService::generateToken($file, self::ACTION, 3600, $filename);

        return responseSuccess($token);
    }

    public function previewFile(Request $request)
    {
        $token = $request->route('token');

        return DocService::preview($token, self::ACTION);
    }

    public function downloadFile(Request $request)
    {
        $token = $request->route('token');

        return DocService::download($token, self::ACTION);
    }

    public function generatePdf(Request $request)
    {

      /*  $title = '管理层介绍';
        $column = ['姓名', '职位', '国籍','加入时间'];
        $unit = '单位：人民币';//表格单位
        $desc = '目标公司及其控股子公司近三年涉诉金额占目标公司总资产 20%以上的诉讼或仲裁案件。（目标公司股东涉诉案件不含在调查范围内）'; // 表格描述
        $content = [
            ['王小明', '总经理', '中国', '2025年10月1日'],
            ['王小明', '总经理', '中国', '2025年10月1日'],
            ['王小明', '总经理', '中国', '2025年10月1日'],
            ['王小明', '总经理', '中国', '2025年10月1日'],
            ['王小明', '总经理', '中国', '2025年10月1日'],
            ['王小明', '总经理', '中国', '2025年10月1日'],
            ['王小明', '总经理', '中国', '2025年10月1日'],
            ['王小明', '总经理', '中国', '2025年10月1日'],
            ['王小明', '总经理', '中国', '2025年10月1日'],
            ['王小明', '总经理', '中国', '2025年10月1日'],
        ];
        (new PdfTableService($title, $column,  $content, '', $unit, $desc))->generatePdf();*/

       /* $title = '生产及办公面积';
        $column = ['房屋坐落', '用途', '面积','产权归属'];
        $content = [
            ['佛山市顺德区乐从镇乐', '办公场所', '200平方', '保利房产'],
            ['佛山市顺德区乐从镇乐从社区东平新城文华南路 8 号保利商务中心 1 座 912 单元佛山市顺德区乐从镇乐从社区东平新城文华南路号保利商务中心座 912 单元', '办公场所', '200平方', '保利房产'],
            ['佛山市顺德区乐从镇乐从社区东平新城文华南路号保利商务中心座 912 单元', '办公场所', '200平方', '保利房产'],
            ['佛山市顺德区乐从镇乐从社区东平', '办公场所', '200平方', '保利房产'],
            ['佛山市顺德区乐从镇乐从社东平新城文华南路号保利', '办公场所', '200平方', '保利房产'],
            ['佛山市顺德区乐从镇乐从社区东平新城文华南路号保利商务中心座 912 单元', '办公场所', '200平方', '保利房产'],
            ['佛山市顺德区乐从镇乐从社区东平新城文华南路 8 号保利商务中心 1 座 912 单元佛山市顺德区乐从镇乐从社区东平新城文华南路号保利商务中心座 912 单元', '办公场所', '200平方', '保利房产'],
        ];
        (new PdfTableService($title, $column,  $content, 'gsp_investigation_office_spaces','', '', '',''))->generatePdf();*/

       /* $title = '产品应用案例';
        $column = ['项目名称', '涉及国家/地区', '供应产品情况','供货金额'];
        $content = [
            ['保利中建合资', '中国、马来西亚、泰国', '200平方', '100000000'],
            ['保利中建合资', '中国、马来西亚、泰国', '200平方', '100000000'],
            ['保利中建合资', '中国、马来西亚、泰国', '200平方', '100000000'],
            ['保利中建合资', '中国、马来西亚、泰国', '200平方', '100000000'],
            ['保利中建合资', '中国、马来西亚、泰国', '200平方', '100000000'],
        ];
        (new PdfTableService($title, $column,  $content, 'gsp_investigation_product_application_cases'))->generatePdf();*/

        /*$title = '所获荣誉';
        $column = ['荣誉名称', '获得时间', '颁发单位'];
        $content = [
            ['杰出优秀设计', '2025-10-1', '保利中建合资'],
            ['杰出优秀设计', '2025-10-1', '保利中建合资'],
            ['杰出优秀设计', '2025-10-1', '保利中建合资'],
            ['杰出优秀设计', '2025-10-1', '保利中建合资'],
            ['杰出优秀设计', '2025-10-1', '保利中建合资'],
        ];
        (new PdfTableService($title, $column,  $content, 'gsp_investigation_honors'))->generatePdf();*/

        /*$title = '所获表扬信';
        $column = ['出具单位', '涉及项目', '出具日期'];
        $content = [
            ['保利中建合资', '城投项目', '2025-10-1'],
            ['保利中建合资', '城投项目', '2025-10-1'],
            ['保利中建合资', '城投项目', '2025-10-1'],
            ['保利中建合资', '城投项目', '2025-10-1'],
            ['保利中建合资', '城投项目', '2025-10-1'],
        ];
        (new PdfTableService($title, $column,  $content, 'gsp_investigation_commendatory_letters'))->generatePdf();*/

       /* $title = '资质及认证';
        $column = ['证书名称', '出具机构', '出具日期', '证书有效期', '认证范围/产品'];
        $content = [
            ['会计高级资格证书', '财务局', '2025-10-1', '2040-10-1', '无'],
            ['会计高级资格证书', '财务局', '2025-10-1', '2040-10-1', '无'],
            ['会计高级资格证书', '财务局', '2025-10-1', '2040-10-1', '无'],
            ['会计高级资格证书', '财务局', '2025-10-1', '2040-10-1', '无'],
            ['会计高级资格证书', '财务局', '2025-10-1', '2040-10-1', '无'],
        ];
        (new PdfTableService($title, $column,  $content, 'gsp_investigation_qualifications'))->generatePdf();*/

       /* $title = '知识产权';
        $column = ['知识产权名称/图片', '知识产权类型', '备注', '注册国家/地区', '状态'];
        $content = [
            ['AI知能设备', '商标', 'AI知能设备', '中国广东', '正常'],

        ];
        (new PdfTableService($title, $column,  $content, 'gsp_investigation_intellectual_properties'))->generatePdf();*/


       /* $title = '失信记录';
        $column = ['失信事件', '状态', '备注', '涉及金额', '受理机构', '日期'];
        $content = [
            ['拖欠货款', '已执行', '拖欠货款', '10000000万元', '广东人民法院', '2025-4-15'],

        ];
        (new PdfTableService($title, $column,  $content, 'gsp_investigation_product_application_cases'))->generatePdf();*/

       /* $title = '重大合同';
        $column = ['合同类型', '主要内容', '涉及金额', '签订日期', '履行状态'];
        $content = [
            ['装修合同', '装修后期维护', '100000万元', '2025-04-15', '未执行'],
        ];
        (new PdfTableService($title, $column,  $content, 'gsp_investigation_significant_contracts'))->generatePdf();*/

       /* $title = '重大诉讼及仲裁信息';
        $column = ['案件名称', '案件类型', '立案日期', '受理机构', '涉诉金额'];
        $content = [
            ['拖欠货款', '拖欠货款', '2025-04-15', '广东人民法院', '100000万元'],
        ];
        (new PdfTableService($title, $column,  $content, 'gsp_investigation_litigation_arbitration_cases', '公司声明近三年无重大诉讼及仲裁案件。', 1))->generatePdf();*/




        /*$title = '公司工商情况';
        $column = '';
        $unit = '';//表格单位
        $desc = ''; // 表格描述
        $content = [
            ['企业名称', '得力集团有限公司'],
            ['曾用名', '浙江新时代文具有限公司'],
            ['统一社会信用代码', '4324324324324'],
            ['注册国家或地区', '中国浙江'],
            ['成立日期', '2000-10-1'],
            ['公司类型', '私营企业'],
            ['注册资本', '1000万'],
            ['实收资本', '500万'],
            ['注册地址', '中国杭州钱塘江'],
            ['经营地址', '中国杭州钱塘江'],
            ['经营范围', '成立于1988年，前身为宁海县文教用品厂，产品名为——环球。1992年改名为浙江新时代文具有限公司。1995年设立得力品牌。主要生产考勤机，门禁机，摄像机，打印机，访客机和其它办公品类。学生用品目前有成为国内最强趋势，另顺带着做了胶粘用品。公司面向全国45,000家门店发货。[1]得力文具同时也是电商领域，如天猫商城和京东的领头产品。'],
            ['年检情况', '合格'],
            ['营业期限', '10年']
        ];
        $bottom = "";
        (new PdfTableService($title, $column,  $content, '',$unit, $desc,  $bottom, '公司声名', 0))->generatePdf();*/

        /*$title = '售后服务及保证';
        $column = '';
        $unit = '';//表格单位
        $desc = ''; // 表格描述
        $content = [
            ['售后服务<br>fdsfds', '7天无理由退换货'],
            ['服务标准', 'ISO4324324324324'],
            ['服务方式', '前身为宁海县文教用品厂，产品名为——环球。1992年改名为浙江新时代文具有限公司。1995年设立得力品牌前身为宁海县文教用品厂，产品名为——环球。1992年改名为浙江新时代文具有限公司。1995年设立得力品牌前身为宁海县文教用品厂，产品名为——环球。1992年改名为浙江新时代文具有限公司。1995年设立得力品牌前身为宁海县文教用品厂，产品名为——环球。1992年改名为浙江新时代文具有限公司。1995年设立得力品牌'],
            ['响应时效', '24小时'],
            ['覆盖地区', '中国浙江']
        ];
        $bottom = "";
        $token = (new PdfTableService($title, $column,  $content, '',$unit, $desc,  $bottom, '', 0, '售后服务及保证.pdf'))->generatePdf();

        return responseSuccess(['token'=>$token]);*/

        /*$title = '概况摘要';
        $column = '';
        $unit = '';//表格单位
        $desc = ''; // 表格描述
        $content = [
            [
                'title' => '公司概述',
                'text' => '公司成立于【 】年，为【 】集团成员，总部位于【国家-省-市】。截至报告基准日，公司注册资本为人民币/美元（下拉式选择）【 】元，实收资本为人民币/美元【 】元。主要股东包括【前五大股东名称】（持股比例共计【 】%）。
【企业希望自主增加说明的与公司概述相关的内容】'
            ],
            [
                'title' => '业务概述',
                'text' => '目标公司的核心业务集中在【产品名称】，生产及办公面积共计【 】平方米，配备了【生产设备名称】等关键设施。原材料采购来自【原材料来源的国家/地区名称】的【供应商名称】、生产过程及质量控制为【将“生产过程及质量控制”过程进行归纳】。售后服务承诺【将售后服务中的“响应时间/覆盖[国家/地区]”进行归纳】，满足客户需求。
【企业希望自主增加说明的与业务概述相关的内容】'
            ],
            [
                'title' => '客户及业绩',
                'text'  => '客户及终端用户涵盖【客户所在行业类型】的企业，包括【主要客户名称】（年交易金额人民币【 】元）。产品应用案例包括【项目名称】,涉及【项目涉及的国家/地区】，供货金额人民币【 】元。
【企业希望自主增加说明的与业绩概述相关的内容】'
            ],
            [
                'title' => '法务与合规',
                'text'  => '目标公司已获得【资质/证书/标准认证】和知识产权【专利/商标/著作权（含软件著作权）】。涉及【合同类型（如买卖、租赁、借款、担保等）】的重大合同（金额超过人民币【 】百万元）。目前共有【 】起诉讼/仲裁案件，涉及金额共计人民币【 】元；未处理的行政处罚共【 】件，主要是由于【未处理的行政处罚的原因。有则填写具体内容，无则打“/”】。有【 】条失信记录。'
            ],
            [
                'title' => '财务报告',
                'text'  => '目标公司审计报告显示，目标公司【 】年至【 】年分别实现收入人民币【 】百万元和人民币【 】百万元，净利润率保持在【 】%-【 】%。资产负债率为【 】%。'
            ],
            [
                'title' => '未来规划概括',
                'text'  => '【“未来规划”部分概括归纳】'
            ],

        ];
        $bottom = "";
        $token = (new PdfTableService($title, $column,  $content, '',$unit, $desc,  $bottom, '', 0))->generateStaticPdf();*/

       /* $title = '公司简介';
        $column = '';
        $unit = '';//表格单位
        $desc = ''; // 表格描述
        $content = [
            [
                'title' => '',
                'text' => '公司成立于【 】年，为【 】集团成员，主要生产地位于【国家-省-市】，是一家从事【大类行业名称】的企业，一直深耕于【细分行业名称】，主要产品包括【 】。
【企业希望自主增加说明的与公司简介相关的内容】'
            ],

        ];
        $bottom = "";
        $token = (new PdfTableService($title, $column,  $content, '',$unit, $desc,  $bottom, '', 0))->generateStaticPdf();

        return responseSuccess(['token'=>$token]);*/

        /*$title = '员工人数';
        $column = '';
        $unit = '';//表格单位
        $desc = ''; // 表格描述
        $content = [
            [
                'title' => '',
                'text' => '目标公司员工总数【 】人，社会保险实际缴费人数【 】人。'
            ],

        ];
        $bottom = "";
        $token = (new PdfTableService($title, $column,  $content, '',$unit, $desc,  $bottom, '', 0, '员工人数.pdf'))->generateStaticPdf();

        return responseSuccess(['token'=>$token]);*/
      /*  $title = '前五大主要生产设备';
       $column = ['设备名称', '采购/首次租赁时间', '描述/涉及主要技术/应用场景', '数量', '产权归属'];
       $unit = "";//表格单位
       $desc = ""; // 表格描述
       $content = [
           ['打磨机器',  '2026-1-1','制衣打磨','100', '佛山制衣有限公司'],
           ['打磨机器',  '2026-1-1','制衣打磨','100', '佛山制衣有限公司'],
           ['打磨机器',  '2026-1-1','制衣打磨','100', '佛山制衣有限公司'],
           ['打磨机器',  '2026-1-1','制衣打磨','100', '佛山制衣有限公司'],
           ['打磨机器',  '2026-1-1','制衣打磨','100', '佛山制衣有限公司'],
           ['打磨机器',  '2026-1-1','制衣打磨','100', '佛山制衣有限公司'],
       ];
       $bottom = "";
       (new PdfTableService($title, $column,  $content, '',$unit, $desc,  $bottom, '', 0, '前五大主要生产设备.pdf'))->generatePdf();*/

      /*  $title = '生产与交付<br>fdsafdsaf';
        $column = ['生产线/设施<br>fdsfds', '平均生产周期<br>fdsfds', '常规交货期<br>fdsfds', '备注<br>fdsfds'];
        $unit = "";//表格单位
        $desc = "结合目标公司自身业务特点，确定重大合同的标准为：占目标公司年交易额 10%的合同或年度框架协议。<br>fdsfdsfdsfdsafd"; // 表格描述
        $content = [
            ['<Img src="/gsp/report/1.jpg"/>', '12周', '2026-1-1', '质量检测同步进行结合目标公司自身业务特点，确定重大合同的标准为：占目标公司年交易额 10%的合同或年度框架协议。'],
            ['<Img src="/gsp/report/1.jpg"/>', '12周', '2026-1-1', '质量检测同步进行'],
            ['<Img src="/gsp/report/1.jpg"/>', '12周', '2026-1-1', '质量检测同步进行'],
            ['<Img src="/gsp/report/1.jpg"/>', '12周', '2026-1-1', '质量检测同步进行结合目标公司自身业务特点，确定重大合同的标准为：占目标公司年交易额 10%的合同或年度框架协议。'],

        ];
        $bottom = "目标公司年产能计算公式：【平均生产周期 x 常规交货期 / 12 】。依据此计算公式，目标公司【 佛山制衣厂实业有限公司】年度年产能为【 1000万 】。目标公司【佛山制衣厂实业有限公司 】年度库存金额为人民币【10000万 】元。";
        (new PdfTableService($title, $column,  $content, 'gsp_investigation_production_deliveries',$unit, $desc,  $bottom, '', 0, '生产与交付.pdf','', '2025-4-24 10:12:89'))->generatePdf();*/

       /* $title = '公司简介';
        $column = '';
        $unit = '';//表格单位
        $desc = ''; // 表格描述
        $content = [
            [
                'title' => '',
                'text' => '公司成立于【 】年，为【 】集团成员，主要生产地位于【国家-省-市】，是一家从事【大类行业名称】的企业，一直深耕于【细分行业名称】，主要产品包括【 】。
【企业希望自主增加说明的与公司简介相关的内容】'
            ],

        ];
        $bottom = "";
        (new PdfTableService($title, $column,  $content, '',$unit, $desc,  $bottom, '', 0, '公司简介.pdf', '','2025-04-25 11:10:12'))->generateStaticPdf();*/

        /*$title = '全体董事、监事、高级管理人员声明';
        $column = [
            ['全体董事','名称', '签名'],
            ['全体监事','名称', '签名'],
            ['全体高级管理人员','名称', '签名'],
        ];
        $unit = '';//表格单位
        $desc = '本公司全体董事、监事、高级管理人员承诺本尽职调查报告内涉及公司的全部内容均真实、准确、完整，不存在虚假记载、误导性陈述或重大遗漏，按照诚信原则履行承诺，并承担相应的法律责任。'; // 表格描述
        $content = [
            0 => [ // 全体董事
               ['黄小明', ''],
               ['李小红', ''],
            ],
            1 => [ // 全体监事
                ['Tom', ''],
                ['Sun', ''],
            ],
            2 => [ // 全体高级管理人员
                ['Eric', ''],
                ['Shan', ''],
            ]

        ];
        $bottom = "";
        $token = (new PdfTableService($title, $column,  $content, '',$unit, $desc,  $bottom, '', 0, '全体董事.pdf'))->generateLeadPdf();

        return responseSuccess(['token'=>$token]);*/

        // 利润表
        /*$title = 'STATEMENT OF COMPREHENSIVE INCOME 综合损益表/利润表';
        $column = '';
        $content = [
                0 => [
                    'year'  => 2024,
                    'revenue' => 1000000,
                    'cogs' => 8000000,
                    'gross_profit' => 200000,
                    'research_and_development_expense' => 10000,
                    'general_expenses'  => 200000,
                    'administrative_expenses' => 200000,
                    'general_and_administrative_expense' => 200000,
                    'net_exposure_hedging_loss'   => 200000,
                    'fair_value_change_loss'   => 200000,
                    'credit_impairment_loss'   => 200000,
                    'asset_impairment_loss'   => 200000,
                    'asset_disposal_loss'   => 200000,
                    'other_non_operating_expenses' => 200000,
                    'other_operating_expenses' => 200000,
                    'financial_asset_income'  => 200000,
                    'net_exposure_hedging_gain'  => 200000,
                    'fair_value_change_gain'  => 200000,
                    'asset_disposal_gain'  => 200000,
                    'other_non_operating_income'  => 200000,
                    'other_operating_income'  => 200000,
                    'oprating_profit' => 200000,
                    'interest_income' => 200000,
                    'investment_income' => 200000,
                    'finance_income' => 200000,
                    'interest_expense' => 200000,
                    'investment_loss'  => 200000,
                    'finance_expenses' => 200000,
                    'share_of_net_profit' => 200000,
                    'profit_before_tax' => 200000,
                    'taxation'  => 200000,
                    'profit_for_the_period' => 200000,
                    'items_that_will_not_be_reclassified_to_profit_and_loss' => 200000,
                    'other_comprehensive_income' => 200000,
                    'other_comprehensive_income_all' => 2000,
                    'gains_losses_on_cash_flow_hedges' => 20000,
                    'currency_retranslation_gains' => 20000,
                    'other_comprehensive_expense' => 20000,
                    'total_comprehensive_income_for_the_period' => 20000,
                    'accounting_name' => '事所',
                    'accounting_name_en'=>'en'
                ],
                1 => [
                    'year'  => 2023,
                    'revenue' => 1000000,
                    'cogs' => 8000000,
                    'gross_profit' => 200000,
                    'research_and_development_expense' => 10000,
                    'general_expenses'  => 200000,
                    'administrative_expenses' => 200000,
                    'general_and_administrative_expense' => 200000,
                    'net_exposure_hedging_loss'   => 200000,
                    'fair_value_change_loss'   => 200000,
                    'credit_impairment_loss'   => 200000,
                    'asset_impairment_loss'   => 200000,
                    'asset_disposal_loss'   => 200000,
                    'other_non_operating_expenses' => 200000,
                    'other_operating_expenses' => 200000,
                    'financial_asset_income'  => 200000,
                    'net_exposure_hedging_gain'  => 200000,
                    'fair_value_change_gain'  => 200000,
                    'asset_disposal_gain'  => 200000,
                    'other_non_operating_income'  => 200000,
                    'other_operating_income'  => 200000,
                    'oprating_profit' => 200000,
                    'interest_income' => 200000,
                    'investment_income' => 200000,
                    'finance_income' => 200000,
                    'interest_expense' => 200000,
                    'investment_loss'  => 200000,
                    'finance_expenses' => 200000,
                    'share_of_net_profit' => 200000,
                    'profit_before_tax' => 200000,
                    'taxation'  => 200000,
                    'profit_for_the_period' => 200000,
                    'items_that_will_not_be_reclassified_to_profit_and_loss' => 200000,
                    'other_comprehensive_income' => 200000,
                    'other_comprehensive_income_all' => 2000,
                    'gains_losses_on_cash_flow_hedges' => 20000,
                    'currency_retranslation_gains' => 20000,
                    'other_comprehensive_expense' => 20000,
                    'total_comprehensive_income_for_the_period' => 20000,
                    'accounting_name' => '事所',
                    'accounting_name_en'=>'en'
                ]

        ];
        $token = (new PdfTableService($title, $column,  $content, '','', '',  '', '', 0, '利润表.pdf','', '2025-05-08'))->incomeStatement();*/
        // 现金流量表
      /*  $title = '现金流量表（直接式）';
        $column = '';
        $content = [
            0 => [
                'year'  => 2024,
                'cash_received_from_the_sale' => 200000,
                'tax_refunds_received'  => 200000,
                'other_cash_received_in_connection_with_operating_activities' => 200000,
                'subtotal_cash_inflows_from_operating_activities' => 200000,
                'cash_for_the_purchase' => 200000,
                'cash_paid_to_and_on_behalf' => 200000,
                'taxes_and_fees_paid' => 200000,
                'payment_of_other_cash_related_to_operating_activities' => 200000,
                'subtotal_cash_otflows_from_operating_activities' => 200000,
                'proceeds_from_sale_of_plant' => 200000,
                'proceeds_from_sale_of_investments' => 200000,
                'proceeds_from_sale_of_goodwill' => 200000,
                'net_cash_received_from_sales_of_subsidiaries' => 200000,
                'other_proceeds_from_investments' => 200000,
                'operating_incomes_from_investments' => 200000,
                'dividends_received' => 200000,
                'interest_received' => 200000,
                'rent_on_property_received' => 200000,
                'purchase_of_plant' => 200000,
                'purchase_of_investments' => 200000,
                'purchase_of_joint_ventures_and_associates' => 200000,
                'purchase_of_intangible_assets' => 200000,
                'other_payments_for_investing_activities' => 200000,
                'net_cash_from_investing_activities' => 200000,
                'proceeds_from_issue_of_share_capital_and_borrowings' => 200000,
                'proceeds_from_issue_of_ordinary_shares_capital' => 20000,
                'proceeds_from_issue_of_preference_shares_capital' => 200000,
                'proceeds_from_loans_and_borrowings_etc' => 200000,
                'buy_back_of_equity_shares' => 200000,
                'redemption_of_preference_shares' => 200000,
                'repayment_of_loans_and_borrowings' => 200000,
                'redemption_of_debentures' => 200000,
                'dividends_paid_on_equity_shares' => 200000,
                'dividend_on_preference_shares' => 200000,
                'repayment_of_obligations_under_leases' => 200000,
                'other_payments_for_financing_activities' => 200000,
                'from_financing_activities' => 200000,
                'net_increase' => 200000,
                'cash_equivalents_at_the_beginning_of_the_period' => 200000,
                'effect_of_foreign_exchange_rate_changes' => 200000,
                'cash_equivalents_at_the_end_of_the_year' => 200000,
            ],
            1 => [
                'year'  => 2023,
                'cash_received_from_the_sale' => 200000,
                'tax_refunds_received'  => 200000,
                'other_cash_received_in_connection_with_operating_activities' => 200000,
                'subtotal_cash_inflows_from_operating_activities' => 200000,
                'cash_for_the_purchase' => 200000,
                'cash_paid_to_and_on_behalf' => 200000,
                'taxes_and_fees_paid' => 200000,
                'payment_of_other_cash_related_to_operating_activities' => 200000,
                'subtotal_cash_otflows_from_operating_activities' => 200000,
                'proceeds_from_sale_of_plant' => 200000,
                'proceeds_from_sale_of_investments' => 200000,
                'proceeds_from_sale_of_goodwill' => 200000,
                'net_cash_received_from_sales_of_subsidiaries' => 200000,
                'other_proceeds_from_investments' => 200000,
                'operating_incomes_from_investments' => 200000,
                'dividends_received' => 200000,
                'interest_received' => 200000,
                'rent_on_property_received' => 200000,
                'purchase_of_plant' => 200000,
                'purchase_of_investments' => 200000,
                'purchase_of_joint_ventures_and_associates' => 200000,
                'purchase_of_intangible_assets' => 200000,
                'other_payments_for_investing_activities' => 200000,
                'net_cash_from_investing_activities' => 200000,
                'proceeds_from_issue_of_share_capital_and_borrowings' => 200000,
                'proceeds_from_issue_of_ordinary_shares_capital' => 20000,
                'proceeds_from_issue_of_preference_shares_capital' => 200000,
                'proceeds_from_loans_and_borrowings_etc' => 200000,
                'buy_back_of_equity_shares' => 200000,
                'redemption_of_preference_shares' => 200000,
                'repayment_of_loans_and_borrowings' => 200000,
                'redemption_of_debentures' => 200000,
                'dividends_paid_on_equity_shares' => 200000,
                'dividend_on_preference_shares' => 200000,
                'repayment_of_obligations_under_leases' => 200000,
                'other_payments_for_financing_activities' => 200000,
                'from_financing_activities' => 200000,
                'net_increase' => 200000,
                'cash_equivalents_at_the_beginning_of_the_period' => 200000,
                'effect_of_foreign_exchange_rate_changes' => 200000,
                'cash_equivalents_at_the_end_of_the_year' => 200000,
            ]

        ];
        $token = (new PdfTableService($title, $column,  $content, '','', '',  '', '', 0, '现金流量表.pdf', '', '2025-07-7'))->cashFlow();*/


        // 资产负债表
        $title = '资产负债表 (工作版本）';
        $column = '';
        $content = [
            0 => [
                'year'  => 2024,
                'fixed_assets' => -20000,
                'construction_in_progress' => -20000,
                'property_plant_and_equipment' => 20000,
                'goodwill' => 20000,
                'intangible_assets' => 20000,
                'right_of_use_assets' => 20000,
                'investments_properties' => 20000,
                'debt_investments' => 20000,
                'available_for_sale_financial_assets' => 20000,
                'other_debt_investments' => 20000,
                'held_to_maturity_investments' => 20000,
                'long_term_equity_investments' => 20000,
                'other_equity_instrument_investments' => 20000,
                'other_non_current_financial_assets' => 20000,
                'other_investments_all'  => 20000,
                'long_term_receivables' => 20000,
                'deferred_tax_assets' => 20000,
                'productive_biological_assets' => 20000,
                'oil_and_gas_assets' => 20000,
                'development_expenditure' =>  20000,
                'long_term_prepaid_expenses' => 20000,
                'other_non_current_assets' => 20000,
                'other_non_current_assets_all' => 20000,
                'total_non_current_assets' => 20000,
                'inventories' => 20000,
                'notes_receivable' => 20000,
                'accounts_receivable' => 20000,
                'prepayments' => 20000,
                'other_receivables' => 20000,
                'trade_and_other_receivables' => 20000,
                'trading_financial_assets' => 20000,
                'financial_assets_measured' => 20000,
                'derivative_financial_assets' => 20000,
                'other_investments' => 20000,
                'contract_assets' => 20000,
                'held_for_sale_assets' => 20000,
                'non_current_assets_due_within_one_year' => 20000,
                'other_current_assets' => 20000,
                'receivables_financing' => 20000,
                'other_current_assets_all' => 20000,
                'income_tax_receivables' => 20000,
                'cash_and_cash_equivalents' => 20000,
                'total_current_assets' => 20000,
                'total_assets' => 20000,
                'short_term_borrowings' => 20000,
                'trading_financial_liabilities' => 20000,
                'derivative_financial_liabilities' => 20000,
                'interest_bearing_loans_and_borrowings_short' => 20000,
                'lease_liabilities_short' => 20000,
                'accounts_payable' => 20000,
                'notes_payable' => 20000,
                'advance_receipts' => 20000,
                'contract_liabilities' => 20000,
                'employee_benefits_payable' => 20000,
                'other_payables' => 20000,
                'trade_and_other_payables' => 20000,
                'provisions_short' => 20000,
                'income_tax_payables_short' => 20000,
                'held_for_sale_liabilities' => 20000,
                'non_current_liabilities_due_within_one_year' => 20000,
                'other_current_liabilities' => 20000,
                'other_current_liabilities_all' => 20000,
                'total_current_liabilities' => 20000,
                'long_term_borrowings' => 20000,
                'bonds_payable' => 20000,
                'interest_bearing_loans_and_borrowings_long' => 20000,
                'lease_liabilities_long' => 20000,
                'deferred_tax_liabilities' => 20000,
                'provisions_long' => 20000,
                'income_tax_payables_long' => 20000,
                'long_term_payables' => 20000,
                'long_term_employee_benefits_payable' => 20000,
                'provision_liabilities' => 20000,
                'other_paybales' => 20000,
                'preferred_stock' => 20000,
                'perpetual_bonds' => 20000,
                'deferred_revenue' => 20000,
                'other_non_current_liabilities' => 20000,
                'other_non_current_liabilities_all' => 20000,
                'total_non_current_liabilities' => 20000,
                'total_liabilities' => 20000,
                'ordinary_share_capital' => 20000,
                'less_treasury_stocks' => 20000,
                'preferred_share_holders' => 20000,
                'share_premium' => 20000,
                'other_comprehensive_income' => 20000,
                'special_reserves' => 20000,
                'surplus_reserves' => 20000,
                'other_reserves' => 20000,
                'retained_eranings' => 20000,
                'total_equity' => 20000,
                'total_equity_and_liabilities' => 20000,
                'check' => 20000,
                'non_controlling_interest' => 20000,
                'share_capital'=>3213
            ],
            1 => [
                'year'  => 2023,
                'fixed_assets' => 20000,
                'construction_in_progress' => 20000,
                'property_plant_and_equipment' => 20000,
                'goodwill' => 20000,
                'intangible_assets' => 20000,
                'right_of_use_assets' => 20000,
                'investments_properties' => 20000,
                'debt_investments' => 20000,
                'available_for_sale_financial_assets' => 20000,
                'other_debt_investments' => 20000,
                'held_to_maturity_investments' => 20000,
                'long_term_equity_investments' => 20000,
                'other_equity_instrument_investments' => 20000,
                'other_non_current_financial_assets' => 20000,
                'other_investments_all'  => 20000,
                'long_term_receivables' => 20000,
                'deferred_tax_assets' => 20000,
                'productive_biological_assets' => 20000,
                'oil_and_gas_assets' => 20000,
                'development_expenditure' =>  20000,
                'long_term_prepaid_expenses' => 20000,
                'other_non_current_assets' => 20000,
                'other_non_current_assets_all' => 20000,
                'total_non_current_assets' => 20000,
                'inventories' => 20000,
                'notes_receivable' => 20000,
                'accounts_receivable' => 20000,
                'prepayments' => 20000,
                'other_receivables' => 20000,
                'trade_and_other_receivables' => 20000,
                'trading_financial_assets' => 20000,
                'financial_assets_measured' => 20000,
                'derivative_financial_assets' => 20000,
                'other_investments' => 20000,
                'contract_assets' => 20000,
                'held_for_sale_assets' => 20000,
                'non_current_assets_due_within_one_year' => 20000,
                'other_current_assets' => 20000,
                'receivables_financing' => 20000,
                'other_current_assets_all' => 20000,
                'income_tax_receivables' => 20000,
                'cash_and_cash_equivalents' => 20000,
                'total_current_assets' => 20000,
                'total_assets' => 20000,
                'short_term_borrowings' => 20000,
                'trading_financial_liabilities' => 20000,
                'derivative_financial_liabilities' => 20000,
                'interest_bearing_loans_and_borrowings_short' => 20000,
                'lease_liabilities_short' => 20000,
                'accounts_payable' => 20000,
                'notes_payable' => 20000,
                'advance_receipts' => 20000,
                'contract_liabilities' => 20000,
                'employee_benefits_payable' => 20000,
                'other_payables' => 20000,
                'trade_and_other_payables' => 20000,
                'provisions_short' => 20000,
                'income_tax_payables_short' => 20000,
                'held_for_sale_liabilities' => 20000,
                'non_current_liabilities_due_within_one_year' => 20000,
                'other_current_liabilities' => 20000,
                'other_current_liabilities_all' => 20000,
                'total_current_liabilities' => 20000,
                'long_term_borrowings' => 20000,
                'bonds_payable' => 20000,
                'interest_bearing_loans_and_borrowings_long' => 20000,
                'lease_liabilities_long' => 20000,
                'deferred_tax_liabilities' => 20000,
                'provisions_long' => 20000,
                'income_tax_payables_long' => 20000,
                'long_term_payables' => 20000,
                'long_term_employee_benefits_payable' => 20000,
                'provision_liabilities' => 20000,
                'other_paybales' => 20000,
                'preferred_stock' => 20000,
                'perpetual_bonds' => 20000,
                'deferred_revenue' => 20000,
                'other_non_current_liabilities' => 20000,
                'other_non_current_liabilities_all' => 20000,
                'total_non_current_liabilities' => 20000,
                'total_liabilities' => 20000,
                'ordinary_share_capital' => 20000,
                'less_treasury_stocks' => 20000,
                'preferred_share_holders' => 20000,
                'share_premium' => 20000,
                'other_comprehensive_income' => 20000,
                'special_reserves' => 20000,
                'surplus_reserves' => 20000,
                'other_reserves' => 20000,
                'retained_eranings' => 20000,
                'total_equity' => 20000,
                'total_equity_and_liabilities' => 20000,
                'check' => 20000,
                'non_controlling_interest' => 20000,
                'share_capital'=>3213
            ]

        ];
        $token = (new PdfTableService($title, $column,  $content, '','', '',  '', '', 0, '资产负债表.pdf', '', '2025-07-7'))->balanceSheet();
        return responseSuccess(['token'=>$token]);

    }

    // 发票列表
    public function invoiceList(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail($gsp_id);
        }

        // 还没开发票的主流程
        $id = $this->invoiceNone($record['profile_id']);

        // 查看主表信息
        $record = CompanyGsp::where('profile_id', $record['profile_id'])->where('id', $id)->first();
        if (empty($record)) {
            return responseSuccess(['list'=>[], 'invoice'=>null]);
        }

        $items = [];

        // 预审申请费
        if ($record['pay_one'] == 1) { // 已付款
            $invoicePayOne = InvoiceDetailModel::checkInvoice(InvoiceModel::TYPE_GSP_REGISTER, ProjectServiceModel::PAY_PRE_BOND.','.ProjectServiceModel::PAY_COMPANY_APPLY, $id);
            $payOneAmount = IcbcOrderModel::projectAmount(InvoiceModel::TYPE_GSP_REGISTER, ProjectServiceModel::PAY_PRE_BOND.','.ProjectServiceModel::PAY_COMPANY_APPLY, $id);
            if (empty($invoicePayOne)) {
                $invoicePayOne['status'] = InvoiceDetailModel::STATUS_NO;
                $invoicePayOne['created_at'] = '';
            }

            $items[] = [
                'project_name' => '预审申请费',
                'type'         => 1,
                'amount'       => number_format($payOneAmount,2),
                'status'       => $invoicePayOne['status'],
                'created_at'   => $invoicePayOne['created_at'],
            ];
        }

        // 企业开户费
        if ($record['pay_two'] == 1) { // 已付款
            $invoicePayTwo = InvoiceDetailModel::checkInvoice(InvoiceModel::TYPE_GSP_REGISTER, ProjectServiceModel::PAY_COMPANY_OPEN, $id);
            $payTwoAmount = IcbcOrderModel::projectAmount(InvoiceModel::TYPE_GSP_REGISTER, ProjectServiceModel::PAY_COMPANY_OPEN, $id);
            if (empty($invoicePayTwo)) {
                $invoicePayTwo['status'] = InvoiceDetailModel::STATUS_NO;
                $invoicePayTwo['created_at'] = '';
            }

            $items[] = [
                'project_name' => '企业开户费',
                'type'         => 2,
                'amount'       => number_format($payTwoAmount, 2),
                'status'       => $invoicePayTwo['status'],
                'created_at'   => $invoicePayTwo['created_at'],
            ];
        }

        // AI账号开通费
        if ($record['pay_three'] == 1) { // 已付款
            $invoicePayThree = InvoiceDetailModel::checkInvoice(InvoiceModel::TYPE_GSP_REGISTER, ProjectServiceModel::PAY_COMPANY_AIOPEN, $id);
            $payThreeAmount = IcbcOrderModel::projectAmount(InvoiceModel::TYPE_GSP_REGISTER, ProjectServiceModel::PAY_COMPANY_AIOPEN, $id);
            if (empty($invoicePayThree)) {
                $invoicePayThree['status'] = InvoiceDetailModel::STATUS_NO;
                $invoicePayThree['created_at'] = '';
            }

            $items[] = [
                'project_name' => 'AI账号开通费',
                'type'         =>  3,
                'amount'       => number_format($payThreeAmount, 2),
                'status'       => $invoicePayThree['status'],
                'created_at'   => $invoicePayThree['created_at'],
            ];
        }

        // 发票信息
        $invoice = InvoiceModel::getInvoice(InvoiceModel::TYPE_GSP_REGISTER, $id);

        return responseSuccess(['list'=>$items, 'invoice'=>$invoice]);


    }

    // 保存发票信息
    public function invoiceAdd(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail($gsp_id);
        }

        // 还没开发票的主流程
        $id = $this->invoiceNone($record['profile_id']);

        try {
            $body = $request->validate([
                'header_name'      => 'required',
                'tax_number'       => 'required',
                'email'            => 'required|email',
                'phone'            => ['required', new CheckMobileRule],
            ]);

            // 查看主表
            $record = CompanyGsp::where('profile_id', $record['profile_id'])->where('id', $id)->first();
            if (empty($record)) {
                return responseFail();
            }


            InvoiceModel::updateOrCreate(
                [
                    'obj_id'  => $id,
                    'type'    => InvoiceModel::TYPE_GSP_REGISTER
                ],
                [
                'profile_id' => $record['profile_id'],
                'header_name'=> $body['header_name'],
                'tax_number' => $body['tax_number'],
                'email'      => $body['email'],
                'phone'      => $body['phone'],
                'created_at' => date('Y-m-d H:i:s')
            ]);

            return responseSuccess();

        }  catch (ValidationException $e) {
            // 验证失败，自定义错误信息返回
            return responseFail($e->errors());
        }
    }

    /**
     * 申请开票
     * @return void
     */
    public function openInvoice(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail($gsp_id);
        }

        $type = $request->input('type');
        // 还没开发票的主流程
        $id = $this->invoiceNone($record['profile_id']);


        if (empty($type)) {
            return responseFail(__('missing parameter', ['param' => 'type']));
        }


        // 查看主表
        $record = CompanyGsp::where('profile_id', $record['profile_id'])->where('id', $id)->first();
        if (empty($record)) {
            return responseFail();
        }

        // 申请开票项目
        if ($type == 1)  {
            $project = ProjectServiceModel::PAY_PRE_BOND.','.ProjectServiceModel::PAY_COMPANY_APPLY;
            $payType = 'pay_one';
            // 开票金额
            $amount = IcbcOrderModel::projectAmount(InvoiceModel::TYPE_GSP_REGISTER, ProjectServiceModel::PAY_PRE_BOND.','.ProjectServiceModel::PAY_COMPANY_APPLY, $id);
        } else if ($type == 2) {
            $project = ProjectServiceModel::PAY_COMPANY_OPEN;
            $payType = 'pay_two';
            // 开票金额
            $amount = IcbcOrderModel::projectAmount(InvoiceModel::TYPE_GSP_REGISTER, ProjectServiceModel::PAY_COMPANY_OPEN, $id);
        } else if ($type == 3) {
            $project = ProjectServiceModel::PAY_COMPANY_AIOPEN;
            $payType = 'pay_three';
            // 开票金额
            $amount = IcbcOrderModel::projectAmount(InvoiceModel::TYPE_GSP_REGISTER, ProjectServiceModel::PAY_COMPANY_AIOPEN, $id);

        }

        // 未付款不能申请开发票
        if ($record[$payType] != 1) {
            return responseFail(__('login not pay'));
        }

        if ($amount <= 0 ) {
            return responseFail(__('param error', ['param' => 'amount']));
        }

        // 查看是否开过票
        $invoiceData = InvoiceDetailModel::checkInvoice(InvoiceModel::TYPE_GSP_REGISTER, $project, $id);
        if ($invoiceData) {
            if ($invoiceData['status'] == InvoiceDetailModel::STATUS_APPLY) {
                return responseFail(__('Already applied'));
            }

            if ($invoiceData['status'] == InvoiceDetailModel::STATUS_SUCCESS) {
                return responseFail(__('Invoiced'));
            }
        }

        // 开票记录
        InvoiceDetailModel::insert([
            'profile_id'    => $record['profile_id'],
            'obj_id'        => $id,
            'type'          => InvoiceModel::TYPE_GSP_REGISTER,
            'project'       => $project,
            'amount'        =>  $amount,
            'status'        => InvoiceDetailModel::STATUS_APPLY,
            'created_at'    => date('Y-m-d H:i:s')
        ]);

        return responseSuccess();
    }

    // 查询还没开发票的主流程id
    public function invoiceNone($profileID)
    {
        // 查看主表信息
        $id = CompanyGsp::where('profile_id', $profileID)->value('id');

        return $id;
    }

    // 合伙人提交审核意见
    public function partnerReport(Request $request)
    {
        $user = $request->attributes->get('user');
        $report_partner_suggest = $request->input('report_partner_suggest');
        $is_report_partner_post = $request->input('is_report_partner_post');

        if (!in_array($is_report_partner_post, [1,2])) {
            return responseFail(__('param error', ['param' => 'is_report_partner_post']));
        }

        if ($is_report_partner_post == 2 && empty($report_partner_suggest)) {
            return responseFail(__('missing parameter', ['param' => 'report_partner_suggest']));
        }

        // 正在进行中申请
        $record = $this->getCurrentGsp($user);
        if (empty($record)) {
            return responseFail(__('info no exist'));
        }

        $gsp_id = $record['id'];

        CompanyGsp::where('id', $gsp_id)->update([
            'report_partner_suggest' => $report_partner_suggest,
            'is_report_partner_post' => $is_report_partner_post
        ]);

        return responseSuccess();

    }

    /**
     * 企业审核
     * @return void
     */
    public function checkList(Request $request)
    {
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp('', $gsp_id);
        if (empty($record)) {
            return responseFail();
        }

        // 列表
        $data = GspInvestigationConfig::with([
            'upload' => function ($query) use ($gsp_id) {
                    $query->select('id', 'file_path', 'file_name', 'company_status', 'file_id')
                    ->where('gsp_id', $gsp_id);
            }])->select('id', 'category_zh_cn', 'description_zh_cn')
            ->whereIn('id', [41, 42, 43])->get();

        // 是否审核
        $count = 0; // 不通过
        if ($data) {
            foreach ($data as &$item) {
                if ($item['upload']) {
                    foreach ($item['upload'] as $file) {
                        if (in_array($file['company_status'], CompanyGspReport::COMPANY_STATUS_ARR)) {
                            $count++; // 有一个通过则通过
                            break;
                        }
                    }
                }

            }
        }

        $status = $count == 3?1:0;

        return responseSuccess(['list'=>$data , 'status'=>$status]);
    }

    /**
     * 保存企业审核
     * @param Request $request
     * @return void
     */
    public function checkSave(Request $request) {
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp('', $gsp_id);
        if (empty($record)) {
            return responseFail();
        }

        $body = $request->validate([
            'data' => ['required', 'array'],
            'data.*.id' => ['required', 'integer', 'min:1'],
            'data.*.company_status' => ['required', 'integer', Rule::in(CompanyGspReport::COMPANY_STATUS_ARR)],
        ]);

        DB::transaction(function () use ($body, $gsp_id) {
            foreach ($body['data'] as &$item) {

                CompanyGspReport::where('id', $item['id'])->where('gsp_id', $gsp_id)
                    ->update([
                    'company_status' => $item['company_status'],
                    'status'     => $item['company_status']
                ]);
            }

        });

        return responseSuccess();

    }

    /**
     * 企业审核下一步提交
     * @param Request $request
     * @return void
     */
    public function nextStep(Request $request){
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp('', $gsp_id);
        if (empty($record)) {
            return responseFail();
        }

        // 有一项通过可进下一步
        $data = GspInvestigationConfig::with([
            'upload' => function ($query) use ($gsp_id) {
                $query->where('gsp_id', $gsp_id);
            }])->select('id', 'category_zh_cn', 'description_zh_cn')
            ->whereIn('id', [41, 42, 43])->get();

        $count = 0; // 审核通过的项
        if ($data) {
            foreach ($data as &$item) {
                if ($item['upload']) {
                    foreach ($item['upload'] as $file) {
                        if ($file['company_status'] == CompanyGspReport::COMPANY_STATUS_SUCCESS) {
                            $count++; // 有一个通过此项通过
                            break;
                        }
                    }
                }

            }
        }

        if ($count == 3) {
            CompanyGsp::where('id', $gsp_id)->update(['status' => CompanyGsp::STATUS_COMPANY_REPORT_SUCCESS]);
        } else {
            CompanyGsp::where('id', $gsp_id)->update(['status' => CompanyGsp::STATUS_COMPANY_REPORT_FAIL]);
        }

        return responseSuccess();


    }
}
