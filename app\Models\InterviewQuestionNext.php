<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InterviewQuestionNext extends Model
{
    use HasFactory;

    protected $connection = 'mysql_ai_desk';

    protected $table = 'cna_interview_question_base_next';
    protected $guarded = [];

    protected $fillable = ['base_id', 'next'];

    public function question()
    {
        return $this->belongsTo(InterviewQuestion::class, 'base_id');
    }
}
