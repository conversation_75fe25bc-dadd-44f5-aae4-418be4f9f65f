<?php

namespace App\Providers;

use App\Events\GspRegisterEvent;
use App\Events\UpdateUserAvatar;
use App\Events\UpdateUserEmail;
use App\Events\UserPaid;
use App\Listeners\DelAiTokenCache;
use App\Listeners\GspRegisterListener;
use App\Listeners\UserPaidTodo;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        \App\Events\CheckPartnerEvent::class => [
            \App\Listeners\CheckPartnerListener::class,
        ],
        \App\Events\CheckCompanyEvent::class => [
            \App\Listeners\CheckCompanyListener::class,
        ],
        UpdateUserAvatar::class => [
            DelAiTokenCache::class.'@handleUpdateUserAvatar',
        ],
        UpdateUserEmail::class => [
            DelAiTokenCache::class.'@handleUpdateUserEmail',
        ],
        UserPaid::class => [
            UserPaidTodo::class,
        ],
        GspRegisterEvent::class => [
            GspRegisterListener::class
        ]
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
