<?php
namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Storage;
use Imagick;
use ImagickException;
use TCPDF_FONTS;

class GspReportOutputService
{
    private $data; // 报告内容
    private $filename; // 报告名称

    private $type; // 是否后台工作人员看( 1 合伙人看; 2 工作人员看)

    private $pdf;

    protected $toc = array(); // 目录信息


    public function __construct($filename, $data)
    {

        $this->filename = $filename;
        $this->data = $data;
    }


    public function addContent() {

        // 第一页公司图片
        $this->page1();

        // 第二页声明
        $this->page2();

        // 第三页目录
        $this->page3();

        // 开始新的页面组（用于正文）
        $this->pdf->startPageGroup();

        // 第四页概况摘要
        $this->page4();

        // 第五页公司概述
        $this->page5();

        // 第六页第三部分业务概述
        $this->page6();

        // 第七页第四部分客户及业绩
        $this->page7();

        // 第八页第五部分法务与合规
        $this->page8();

        // 第九页财务部分
        $this->page9();

        // 第十页未来规划
        $this->page10();

        // 第十一页附件
        $this->page11();
    }

    /**
     * 生成尽调报告处理
     * @type  1 合伙人看; 2 工作人员看
     * @return string
     */
    public function output($type = 1)
    {
        $this->type = $type;

        if ($this->type  == 1) {
            // 创建TCPDF实例
            define('K_PATH_FONTS', storage_path('app/public/fonts/'));
        }



        // 创建一个新的 TCPDF 实例
        $this->pdf = new MYPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

        // 禁用页眉
        $this->pdf->setPrintHeader(false);

        // 启用页脚
        $this->pdf->setPrintFooter(true);
        $this->pdf->setFooterData(
            array(0, 0, 0),  // 页脚文本颜色
            array(255, 255, 255)  // 边框颜色设为白色（与背景同色）
        );
        $this->pdf->setFooterMargin(20); // 设置页脚边距

        // 需要相关字体移到可访问目录
        $fontFile = ['fonts/simfang.ttf', 'fonts/simhei.ttf'];
        $newFile = ['app/public/fonts/simfang.ttf', 'app/public/fonts/simhei.ttf'];

        // 检查目标目录是否存在，如果不存在则创建
        if (!is_dir(dirname(storage_path('app/public/fonts')))) {
            mkdir(dirname(storage_path('app/public/fonts')), 0755, true);
        }

        if ($fontFile) {
            foreach ($fontFile as $key => $val) {
                if (!file_exists(storage_path($newFile[$key]))) {
                    copy(storage_path($val), storage_path($newFile[$key]));
                }
            }
        }

        $fontPath = realpath(storage_path($newFile[0])); // 替换为你的字体文件路径
        $blodFontPath = realpath(storage_path($newFile[1])); // 替换为你的字体文件路径

        // 注册支持粗体的中文字体（如 SimHei.ttf 和 SimHeiBold.ttf）
        $blodFont = TCPDF_FONTS::addTTFfont($blodFontPath, 'TrueTypeUnicode', '', 32);
        $regularFont = TCPDF_FONTS::addTTFfont($fontPath, 'TrueTypeUnicode', '', 32);

        // 设置字体
        $this->pdf->SetFont($blodFont, '', 12);
        $this->pdf->SetFont($regularFont, '', 12);

        // 设置内容
       $this->addContent();


        // 保存文档
        $directory = storage_path('app/public/gsp/report/');
        if (!is_dir($directory)) {
            mkdir($directory, 0777, true); // 递归创建目录
        }
        $file = $directory.$this->filename;

        $this->pdf->Output($file, 'F');

        return 'gsp/report/'.$this->filename;
    }


    /**
     * 第一页公司图片
     * @return void
     */
    public function page1()
    {
        // 添加一个页面
        $this->pdf->AddPage();


        // 提取数组中的键值对为变量
        extract($this->data['page1']);

        $report_img = storage_path('pdf/gsp_template/report_img.png');

        // 获取页面尺寸（考虑边距）
        $width = $this->pdf->getPageWidth();
        $height = $this->pdf->getPageHeight();

        // 临时禁用边距和自动分页
        $this->pdf->SetMargins(0, 0, 0);
        $this->pdf->SetAutoPageBreak(false);

        // 插入图片（强制填充）
        $this->pdf->Image(
            $report_img,
            0, 0,
            $width, $height,
            '', '', '',
            false, 300, '',
            false, false, 0, 'T', false
        );


        if ($this->type == 2) {
            $this->pdf->SetTextColor(255, 0, 0); // 红色文本
        }

        $this->pdf->setXY(1, 202);
        $this->pdf->Cell(0, 20, $field1, 0, 1, 'C'); // 目标公司名称

        $this->pdf->setXY(0, 215);
        $this->pdf->Cell(0, 20, $field2, 0, 1, 'C'); // 报告基准日

        $this->pdf->setXY(0, 225);
        $this->pdf->Cell(0, 20, $field3, 0, 1, 'C'); // 报告出具日

        $this->pdf->setXY(10, 236);
        $this->pdf->Cell(0, 20, $field4, 0, 1, 'C'); // 报告编号

        $this->pdf->setXY(0, 247);
        $this->pdf->Cell(0, 20, $field5, 0, 1, 'C'); // 报告有效期

        $this->pdf->setXY(50, 259);
        $this->pdf->Cell(0, 20, 'Clement & Associates International Limited', 0, 1, 'C'); // 出具方


        // 恢复默认设置
        $this->pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
        $this->pdf->SetAutoPageBreak(true, PDF_MARGIN_BOTTOM);
        $this->pdf->SetTextColor(0, 0, 0);

    }

    // 公司声名
    public function page2()
    {
        // 工作人员查看生成增加颜色
        if ($this->data['page2'] && $this->type==2) {
            foreach ($this->data['page2'] as $key => $val) {
                $this->data['page2'][$key] = '<span style="color:red">'.$val.'</span>';
            }
        }

        // 提取数组中的键值对为变量
        extract($this->data['page2']);

       // $this->pdf->AddPage();

        $htmlTable = <<<EOD
        <style>
            .text-page2{
                line-height: 24px;
                font-size: 12px;
                font-family:simfang;
            }
            .bold {
                font-weight: bold;
            }
        </style>
        <div>
        <div style="font-size: 16px; text-align: center; font-family:SimHei;font-weight:bold;"><b>尽调报告出具方声明</b></div>
        <div><br>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;"><b>报告背景</b></span><br>
            本尽职调查报告（以下简称“尽调报告”）系绿智地球为践行透明度和业务可靠性承诺而编制。绿智地球是一个专注于可持续和技术型城市发展的产业生态系统在线平台，连接产业利益相关者的商业需求和机遇（以下简称“绿智地球平台”）。 <br>
            为支持绿智地球平台的透明度和业务可靠性，Clement & Associates International Limited（以下简称“C&A”）作为绿智地球平台的指定尽职调查服务商对目标公司进行尽职调查。此调查从公司基本信息、核心业务、主要客户及业绩、法务与合规、财务报告及未来规划方面反映目标公司现状。
        </div>
        <div class="text-page2">
        <span  style="font-family:SimHei;font-weight:bold;">报告用途及受众</span><br>
            本尽调报告仅通过绿智地球平台及相关本地域名向绿智地球平台的注册用户发布和提供，并仅供内部参考，未经绿智地球平台或其关联公司书面同意，不得分享、复制或向外分发。
           <br><br>
        <span  style="font-family:SimHei;font-weight:bold;">报告准确性及完整性声明</span><br>
           本尽调报告基于目标公司提供的信息、公开渠道以及适用情况下的第三方验证。尽管已尽力核实关键方面，但关于目标公司提供的公司简介、管理层介绍、生产与交付、质量保证与控制、前十大原材料及来源占比、售后服务及保证、主要客户、产品应用案例、所获荣誉及表扬信和未来规划，限于目标公司提供资料信息状况以及资料信息本身属性，对该等信息的真实性及有效性C&A概不负责。C&A亦不对本尽调报告所含任何信息的准确性或完整性进行独立审计或保证。
           <br><br>
        <span  style="font-family:SimHei;font-weight:bold;">目标公司陈述与保证</span>
           <br>
           目标公司已保证并声明，为编制本报告提供的所有文件、陈述和数据截至提供之日真实、准确、完整且最新。如果日后发现目标公司提供虚假或误导性信息，C&A保留撤销本报告并通知相关利益方的权利。目标公司对其提供的任何不实陈述、遗漏或虚假信息承担全部责任。
           <br><br>
        <span  style="font-family:SimHei;font-weight:bold;">目标公司未来业绩声明</span>
           <br>
           本尽调报告可能包含目标公司提供的预测和未来计划。此类陈述具有前瞻性，基于目标公司在报告时的自身预期、假设和战略计划。这些预测不构成此类结果将实现的保证、承诺或保障。实际结果可能因市场条件、运营风险、财务表现、监管变更或不可预见因素而有所不同。
        <br><br>
        <span  style="font-family:SimHei;font-weight:bold;">C&A责任声明</span>
         <br>
           本尽调报告按资料原样提供，仅供参考。C&A不承担因使用、依赖或无法使用本尽调报告而产生的或与之相关的任何及所有责任。在任何情况下，C&A及其关联公司、员工或代表均不对因使用本报告而产生的任何直接、间接、偶然、后续、特殊或惩罚性损害（包括但不限于利润损失、投资损失、商业机会损失或商誉损失）承担责任。
        <br><br>
        <span  style="font-family:SimHei;font-weight:bold;">阅读提示</span>
            本尽调报告的每个部分都涉及C&A工作的不同方面，请完整阅读整份报告以全面了解C&A的发现。
        <br><br>
        <span  style="font-family:SimHei;font-weight:bold;">报告有效性及更新</span>
        <br>
           本尽调报告涉及的尽职调查工作于目标公司按要求提交全部资料之日开始，于  $field1  年 $field2 月 $field3 日完成。本尽调报告不考虑  $field4  年 $field5 月 $field6 日之后发生的事件或情况，因此，C&A无义务依据前述时间节点后发生的事件或情况更新或修订本尽调报告。本尽调报告自其出具之日起十二（12）个月内有效。如果目标公司在此期限后仍为绿智地球平台用户，其尽职调查报告应每年更新。
        <br><br>
        <span  style="font-family:SimHei;font-weight:bold;">报告适用法律及管辖地</span>
        <br>
           本尽调报告应受英格兰和威尔士法律的管辖，并据其解释。因本尽调报告引起的或与本尽调报告有关的任何争议应由英格兰和威尔士主管法院专属管辖。
        <br><br>
        <span  style="font-family:SimHei;font-weight:bold;">注册用户须知</span>
        <br>
            通过查看本尽调报告，平台注册用户确认已阅读、理解并同意本声明所述条款，包括尽调报告性质、尽调报告用途要求和责任限制。使用本尽调报告即表示注册用户接受这些条款。 
        </div>
         <div style="text-align: right">报告出具方：Clement & Associates International Limited</div>
         <div style="text-align: center">终审人：  </div>
        </div>
        EOD;


       $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

    }

    // 目录
    public function page3()
    {
        $this->pdf->AddPage();

        $htmlTable = <<<EOD
            <style>
             table{
              width: 100%;
              border-collapse: collapse;
              margin: 0 auto; /* 居中显示 */
            }
            .text-page2{
                text-align: left;
                font-size: 11px;
                margin:0 auto;
            }
            .bold {
                font-weight: bold;
            }

        </style>
        <div style="font-size: 16px; text-align: center">目录</div>
        <div class="text-page2">
         <span style="font-family:SimHei;font-weight:bold;">第一部分 概况摘要......................................................................1</span><Br>
            1. 公司概述............................................................................1<Br>
            2. 业务概述............................................................................1<Br>
            3. 客户及业绩..........................................................................1<Br>
            4. 法务与合规..........................................................................1<Br>
            5. 财务报告............................................................................2<Br>
            6. 未来规划概况........................................................................2<br>
         <span style="font-family:SimHei;font-weight:bold;">第二部分 公司概述......................................................................3</span><Br>
            1. 公司简介............................................................................3<Br>
            2. 公司工商情况........................................................................3<Br>
            3. 管理层介绍..........................................................................4<Br>
            4. 股东情况（前五大股东）..............................................................4<br>
         <span style="font-family:SimHei;font-weight:bold;">第三部分 业务概述......................................................................5</span><Br>
            1. 主要产品类别........................................................................5<Br>
            2. 前五大主要生产设备..................................................................5<Br>
            3. 生产及办公面积......................................................................5<Br>
            4. 员工人数............................................................................6<Br>
            5. 生产与交付..........................................................................6<Br>
            6. 质量保证与控制......................................................................6<Br>
            7. 前十主要原材料及来源占比............................................................7<Br>
            8. 售后服务及保证......................................................................7<br>
         <span style="font-family:SimHei;font-weight:bold;">第四部分 客户及业绩....................................................................8</span><Br>
            1. 前五大主要客户情况..................................................................8<Br>
            2. 产品应用案例........................................................................8<Br>
            3. 所获荣誉及表扬信....................................................................9<br>
         <span style="font-family:SimHei;font-weight:bold;">第五部分 法务与合规...................................................................10</span><Br>
            1. 资质及认证.........................................................................10<Br>
            2. 知识产权...........................................................................10<Br>
            3. 重大合同...........................................................................10<Br>
            4. 重大诉讼及仲裁信息.................................................................11<Br>
            5. 行政处罚记录.......................................................................11<Br>
            6. 失信记录...........................................................................11<br>
         <span style="font-family:SimHei;font-weight:bold;">第六部分 财务报告.....................................................................12</span><Br>
            1. 综合损益表.........................................................................12<Br>
            2. 资产负债表.........................................................................13<Br>
            3. 现金流量表.........................................................................15<Br>
        <span style="font-family:SimHei;font-weight:bold;">第七部分 未来规划.....................................................................18</span><Br>
        <span style="font-family:SimHei;font-weight:bold;">第八部分 附件.........................................................................19</span><br>
             1. 公司信息...........................................................................19<br>
             2. 业务资质及荣誉.....................................................................19<br>
             3. 权利证书...........................................................................19<br>
             4. 公司相关现场照片...................................................................19<br>
             5. 审计报告...........................................................................19<br>
             6. 公司声明...........................................................................19<br>
        </div>
        EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');
    }

    // 概要摘要
    public function page4()
    {

        // 工作人员查看生成增加颜色
        if ($this->data['page4'] && $this->type==2) {
            foreach ($this->data['page4'] as $key => $val) {
                $this->data['page4'][$key] = '<span style="color:red">'.$val.'</span>';
            }
        }

        // 提取数组中的键值对为变量
        extract($this->data['page4']);

        $accounting_name = $this->data['page9']['field4'];

        $this->pdf->AddPage();


        $htmlTable = <<<EOD
        <style>
            .text-page2{
                line-height: 24px;
                 font-size: 12px;
            }
            .bold {
                font-weight: bold;
            }
        </style>
         <div style="font-size: 16px; text-align: center;font-family:SimHei;font-weight:bold;">第一部分 概况摘要</div>
        <div class="text-page2">
        <div><span style="font-family:SimHei;font-weight:bold;">1.公司概述</span><br>
            公司成立于  $field1 年，为  $field2    集团成员，总部位于    $field3     。截至报告基准日，公司注册资本为   $field4     ，实收资本为  $field5  。主要股东包括 $field6 （持股比例共计  $field7  %）。
        <br>
         $field7a 
        </div>
        <div>
        <span style="font-family:SimHei;font-weight:bold;">2.业务概述</span><br>
             目标公司的核心业务集中在 $filed7b ，生产及办公面积共计 $field8 平方米，配备了 $field8a 等关键设施。原材料采购来自 $field8b 如 $field8c 、生产过程及质量控制为  $field9  。售后服务承诺 $field9a ，满足客户需求。 <br>
         $field9b 
        </div>
        <div>
        <span style="font-family:SimHei;font-weight:bold;">3.客户及业绩</span><br>
            客户及终端用户涵盖 $field9c 的企业，包括 $field9d （年交易金额合计人民币   $field10   元）。产品应用案例包括 $field10a ,涉及 $field10b ，供货金额合计人民币   $field11      元。
        <br>
         $field11a 
        </div>
        <div>
        <span style="font-family:SimHei;font-weight:bold;">4.法务与合规</span><br>
            目标公司已获得 $field11b 和知识产权 $field11c 。涉及 $field11d 的重大合同（金额超过人民币  $field12  元）。近三年共有$field13  起重大诉讼/仲裁案件，涉及金额共计人民币  $field14   元；未处理的行政处罚共 $field15 件，主要是由于 $field16 。有  $field17 条失信记录。<br>
        </div>
        <div>
         <span style="font-family:SimHei;font-weight:bold;">5.财务报告</span><br>
             目标公司委托 $accounting_name  出具的审计报告显示，目标公司 $field18 年至 $field19 年分别实现收入人民币 $field20 元和人民币 $field21 元，净利润率保持在 $field22  %- $field23 %。资产负债率为 $field24a % -  $field24c %。<br>
        </div>
        <div>
         <span style="font-family:SimHei;font-weight:bold;">6.未来规划概况</span><br>
         $field25
        </div>
        EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');


    }

    // 公司概述
    public function page5()
    {

        // 工作人员查看生成增加颜色
        if ($this->data['page5'] && $this->type==2) {
            foreach ($this->data['page5'] as $key => $val) {
                if (!is_array($val)) {
                    $this->data['page5'][$key] = '<span style="color:red">'.$val.'</span>';
                } else {
                    $redTable = [];
                    foreach ($val as  $row) {
                        foreach ($row as $index => $field ) {
                            $row[$index] = '<span style="color:red">'.$field.'</span>';
                        }
                        $redTable[] = $row;
                    }
                    $this->data['page5'][$key] = $redTable;
                }

            }
        }

        // 提取数组中的键值对为变量
        extract($this->data['page5']);

        $this->pdf->AddPage();

        // 管理层内容
        $table1 = '
         <table  border="0">';
        $table1 .= '<tr class="th"><td class="td" style="width: 25%">姓名</td><td class="td" style="width: 25%">职位</td><td class="td" style="width: 25%">国籍</td><td class="td" style="width: 25%">加入时间</td></tr>';
        foreach ($this->data['page5']['field19'] as $row) {
            $table1 .= '<tr class="th">';
            $table1 .= '<td class="td" style="width: 25%">' . $row[0] . '</td>';
            $table1 .= '<td class="td" style="width: 25%">' . $row[1] . '</td>';
            $table1 .= '<td class="td" style="width: 25%">' . $row[2] . '</td>';
            $table1 .= '<td class="td" style="width: 25%">' . $row[3] . '</td>';
            $table1 .= '</tr>';
        }
        $table1 .= '</table>';

        // 股东情况
        $table2 = '<table>';
        $table2 .= '<tr class="th"><td class="td" style="width: 33%">名称</td><td class="td" style="width: 33%">国家</td><td class="td" style="width: 33%">持股比例</td></tr>';
        foreach ($this->data['page5']['field20'] as $row) {
            $table2 .= '<tr class="th">';
            $table2 .= '<td class="td" style="width: 33%">' . $row[0] . '</td>';
            $table2 .= '<td class="td" style="width: 33%">' . $row[1] . '</td>';
            $table2 .= '<td class="td" style="width: 33%">' . $row[2] . '</td>';
            $table2 .= '</tr>';
        }
        $table2 .= '</table>';

        $htmlTable = <<<EOD
         <style>
              tr { page-break-inside:avoid; page-break-after:auto }
             td { page-break-inside:avoid; page-break-after:auto }
             table{
              width: 100%;
              border-collapse: collapse;
              margin: 0 auto; /* 居中显示 */
              page-break-inside:auto
            }
            .th{
                height: 40px;
            }
            .td{
                
                font-size: 14px;
                line-height: 30px;
                text-align: center;
                width: 40%;
                border:1px solid #999;
            }
             .td-left{
                
                font-size: 14px;
                line-height: 30px;
                width: 56%;
                margin-left: 10px;
                border: 1px solid black;
            }
            .td-center{
                text-align: center;
                line-height: 30px;
                width:4%;
                border: 1px solid black;
            }
            .text-page2{
                line-height: 24px;
                text-align: left;
                 font-size: 12px;
            }
            .bold {
                font-weight: bold;
            }

        </style>
         <div style="font-size: 16px; text-align: center;font-family:SimHei;font-weight:bold;">第二部分 公司概述</div>
        <div class="text-page2">
          公司概述部分概述了目标公司的简介、工商情况、管理层以及股东持股比例。本节中的信息基于目标公司提供的官方文件、公共记录和第三方验证（如适用）。
        </div>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">1.公司简介</span><br>
            公司成立于 $field1  年，为 $field2 集团成员，主要生产地位于 $field3 ，是一家从事 $field4 的企业，一直深耕于 $field4a ，主要产品包括 $field5 。 
        <br>
         $field5a 
        </div>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">2.公司工商情况</span><br>
           截至报告基准日，目标公司已提供以下注册详情，并/或通过公开记录进行验证。
        </div>
         <table align="center"  border="0">
            <tr class="th">
                <td class="td">企业名称</td>
                <td class="td-left"> $field6</td>
            </tr>
            <tr  class="th">
                <td class="td">曾用名</td>
                <td class="td-left"> $field7</td>
            </tr>
            <tr  class="th">
                <td class="td">统一社会信用代码</td>
                <td class="td-left"> $field8</td>
            </tr>
            <tr  class="th">
                <td class="td">注册国家</td>
                <td class="td-left"> $field9</td>
            </tr>
             <tr  class="th">
                <td class="td">成立日期</td>
                <td class="td-left"> $field10</td>
            </tr>
             <tr  class="th">
                <td class="td">公司类型</td>
                <td class="td-left"> $field11</td>
            </tr>
            <tr  class="th">
                <td class="td">注册资本</td>
                <td class="td-left"> $field12</td>
            </tr>
             <tr  class="th">
                <td class="td">实收资本</td>
                <td class="td-left"> $field13</td>
            </tr>
            <tr  class="th">
                <td class="td">注册地址</td>
                <td class="td-left"> $field14</td>
            </tr>
            <tr  class="th">
                <td class="td">经营地址</td>
                <td class="td-left"> $field15</td>
            </tr>
             <tr  class="th">
                <td class="td">经营范围</td>
                <td class="td-left"> $field16</td>
            </tr>
             <tr  class="th">
                <td class="td">年检情况</td>
                <td class="td-left"> 最新为 $field17 年度报告</td>
            </tr>
            <tr  class="th">
                <td class="td">营业期限</td>
                <td class="td-left"> $field18</td>
            </tr>
        </table><br></br>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">3.管理层介绍</span><br>
             截至报告基准日，以下个人在目标公司担任高级管理职务。
         </div>
         $table1
        <br>
         <div class="text-page2">
         <span style="font-family:SimHei;font-weight:bold;">4.股东情况（前五大股东）</span><br>
           截至报告基准日，以下个人在目标公司担任高级管理职务。
        </div>
        $table2
        <br>
        
        EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');


    }

    public function page6()
    {

        // 工作人员查看生成增加颜色
        if ($this->data['page6'] && $this->type==2) {
            foreach ($this->data['page6'] as $key => $val) {
                if (!is_array($val)) {
                    $this->data['page6'][$key] = '<span style="color:red">'.$val.'</span>';
                } else {
                    $redTable = [];
                    foreach ($val as  $row) {
                        foreach ($row as $index => $field ) {
                            $row[$index] = '<span style="color:red">'.$field.'</span>';
                        }
                        $redTable[] = $row;
                    }
                    $this->data['page6'][$key] = $redTable;
                }

            }
        }

        // 提取数组中的键值对为变量
        extract($this->data['page6']);

        $this->pdf->AddPage();

        // 主要产品类别
        $table1 = '
         <table  border="0">';
        $table1 .= '<tr class="th"><td class="td" style="width: 20%">产品类别</td><td class="td" style="width:60%">描述/涉及主要技术/应用场景</td><td class="td" style="width: 20%">适用标准</td></tr>';
        foreach ($this->data['page6']['field1'] as $row) {
            $table1 .= '<tr class="th">';
            $table1 .= '<td class="td" style="width: 20%">' . $row[0] . '</td>';
            $table1 .= '<td class="td" style="width: 60%">' . $row[1] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[2] . '</td>';
            $table1 .= '</tr>';
        }
        $table1 .= '</table>';

        // 2.前五大主要生产设备
        $table2 = '
         <table  border="0">';
        $table2 .= '<tr class="th"><td class="td" style="width:20%">设备名称</td><td class="td" style="width:20%">采购/首次租赁时间</td><td class="td" style="width: 30%">描述/涉及主要技术/应用场景</td><td class="td" style="width: 10%">数量</td><td class="td" style="width: 20%">产权归属</td></tr>';
        foreach ($this->data['page6']['field2'] as $row) {
            $table2 .= '<tr class="th">';
            $table2 .= '<td class="td" style="width: 20%">' . $row[0] . '</td>';
            $table2 .= '<td class="td" style="width: 20%">' . $row[1] . '</td>';
            $table2 .= '<td class="td" style="width: 30%">' . $row[2] . '</td>';
            $table2 .= '<td class="td" style="width: 10%">' . $row[3] . '</td>';
            $table2 .= '<td class="td" style="width: 20%">' . $row[4] . '</td>';
            $table2 .= '</tr>';
        }
        $table2 .= '</table>';

        // 3.生产及办公面积
        $table3 = '
         <table  border="0">';
        $table3 .= '<tr class="th"><td class="td" style="width:25%">房屋坐落</td><td class="td" style="width:25%">用途</td><td class="td" style="width: 25%">面积</td><td class="td" style="width: 25%">产权归属</td></tr>';
        foreach ($this->data['page6']['field3'] as $row) {
            $table3 .= '<tr class="th">';
            $table3 .= '<td class="td" style="width: 25%">' . $row[0] . '</td>';
            $table3 .= '<td class="td" style="width: 25%">' . $row[1] . '</td>';
            $table3 .= '<td class="td" style="width: 25%">' . $row[2] . '</td>';
            $table3 .= '<td class="td" style="width: 25%">' . $row[3] . '</td>';
            $table3 .= '</tr>';
        }
        $table3 .= '</table>';

        // 5.生产与交付
        $table4 = '
         <table  border="0">';
        $table4 .= '<tr class="th"><td class="td" style="width:25%">生产线/设施</td><td class="td" style="width:25%">平均生产周期</td><td class="td" style="width: 25%">常规交货期</td><td class="td" style="width: 25%">备注</td></tr>';
        foreach ($this->data['page6']['field9'] as $row) {
            $table4 .= '<tr class="th">';
            $table4 .= '<td class="td" style="width: 25%">' . $row[0] . '</td>';
            $table4 .= '<td class="td" style="width: 25%">' . $row[1] . '</td>';
            $table4 .= '<td class="td" style="width: 25%">' . $row[2] . '</td>';
            $table4 .= '<td class="td" style="width: 25%">' . $row[3] . '</td>';
            $table4 .= '</tr>';
        }
        $table4 .= '</table>';

        // 6.质量保证与控制
        $table5 = '
         <table  border="0">';
        $table5 .= '<tr class="th"><td class="td" style="width:50%">质量控制流程</td><td class="td" style="width:50%">描述/技术细节</td></tr>';
        foreach ($this->data['page6']['field15'] as $row) {
            $table5 .= '<tr class="th">';
            $table5 .= '<td class="td" style="width: 50%">' . $row[0] . '</td>';
            $table5 .= '<td class="td" style="width: 50%">' . $row[1] . '</td>';
            $table5 .= '</tr>';
        }
        $table5 .= '</table>';


        // 7.前十主要原材料及来源占比
        $table6 = '
         <table  border="0">';
        $table6 .= '<tr class="th"><td class="td" style="width:25%">主要原材料</td><td class="td" style="width:25%">来源国家/地区</td><td class="td" style="width: 25%">在特定产品中占比</td><td class="td" style="width: 25%">主要供应商名称</td></tr>';
        foreach ($this->data['page6']['field16'] as $row) {
            $table6 .= '<tr class="th">';
            $table6 .= '<td class="td" style="width: 25%">' . $row[0] . '</td>';
            $table6 .= '<td class="td" style="width: 25%">' . $row[1] . '</td>';
            $table6 .= '<td class="td" style="width: 25%">' . $row[2] . '</td>';
            $table6 .= '<td class="td" style="width: 25%">' . $row[3] . '</td>';
            $table6 .= '</tr>';
        }
        $table6 .= '</table>';

        // 8.售后服务及保证
        $table7 = '
         <table  border="0">';
        $table7 .= '<tr class="th">';
        $table7 .= '<td class="td" style="width: 50%">售后服务</td>';
        $table7 .= '<td class="td" style="width: 50%">概述</td>';
        $table7 .= '</tr>';
        $table7 .= '<tr class="th">';
        $table7 .= '<td class="td" style="width: 50%">服务标准</td>';
        $table7 .= '<td class="td" style="width: 50%">' . $field17 . '</td>';
        $table7 .= '</tr>';
        $table7 .= '<tr class="th">';
        $table7 .= '<td class="td" style="width: 50%">服务方式</td>';
        $table7 .= '<td class="td" style="width: 50%">' . $field18 . '</td>';
        $table7 .= '</tr>';
        $table7 .= '<tr class="th">';
        $table7 .= '<td class="td" style="width: 50%">响应时效</td>';
        $table7 .= '<td class="td" style="width: 50%">' . $field19 . '</td>';
        $table7 .= '</tr>';
        $table7 .= '<tr class="th">';
        $table7 .= '<td class="td" style="width: 50%">覆盖地区</td>';
        $table7 .= '<td class="td" style="width: 50%">' . $field20 . '</td>';
        $table7 .= '</tr>';
        $table7 .= '</table>';

        $htmlTable = <<<EOD
         <style>
             tr { page-break-inside:avoid; page-break-after:auto }
             td { page-break-inside:avoid; page-break-after:auto }
             table{
              width: 100%;
              border-collapse: collapse;
              margin: 0 auto; /* 居中显示 */
              page-break-inside:auto
            }
            .th{
                height: 40px;
            }
            .td{
                
                font-size: 14px;
                line-height: 30px;
                text-align: center;
                width: 40%;
                border:1px solid #999;
            }
             .td-left{
                
                font-size: 14px;
                line-height: 30px;
                width: 56%;
                margin-left: 10px;
                border: 1px solid black;
            }
            .td-center{
                text-align: center;
                line-height: 30px;
                width:4%;
                border: 1px solid black;
            }
            .text-page2{
                line-height: 24px;
                text-align: left;
                font-size: 12px;
            }
            .bold {
                font-weight: bold;
            }

        </style>
         <div style="font-size: 16px; text-align: center;font-family:SimHei;font-weight:bold;">第三部分业务概述</div>
        <div class="text-page2">
            业务概述部分概述了目标公司的主要产品类别、主要生产设备、生产及办公面积、雇员人数、生产及交付情况、质量控制措施、主要原材料和售后服务。本节基于目标公司提供和/或通过可用第三方来源验证（如适用）的数据。
        </div>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">1.主要产品类别</span><br>
            截至报告基准日，目标公司已报告以下主要产品类别和技术规格。
        </div>
        $table1
        <br>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">2.前五大主要生产设备</span><br>
            截至报告基准日，目标公司的生产设施中报告有以下主要机械和设备。
        </div>
        $table2
        <br>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">3.生产及办公面积</span><br>
            截至报告基准日，目标公司在以下地址进行生产和办公。
        </div>
        <div style="text-align: right">单位：平方米</div>
        $table3
        <br>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">4.员工人数</span><br>
            截至 $field4  年 $field5 月 $field6 日，目标公司员工总数 $field7  人，社会保险实际缴费人数 $field8  人。
        </div>
        <br>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">5.生产与交付</span><br>
            (1)截至报告基准日，下表概述目标公司的交付周期。<br>
        </div>
        $table4
        <br>
        <div class="text-page2">
        (2)目标公司年产能计算公式： $field10 。依据此计算公式，目标公司 $field11  年度年产能为 $field12  。<br>
        (3)目标公司 $field13 年度库存金额为人民币 $field14 元。
        </div>
        <br>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">6.质量保证与控制</span><br>
           截至报告基准日，目标公司已报告以下质量控制流程，以确保产品的一致性和合规性。
        </div>
        $table5
        <br>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">7.前十主要原材料及来源占比</span><br>
           截至报告基准日，以下国家和供应商已被确定为生产原材料的主要来源。
        </div>
        $table6
        <br>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">8.售后服务及保证</span><br>
           截至报告基准日，目标公司已报告以下售后服务承诺。
        </div>
        $table7
        <br>
        EOD;


        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');


    }

    public function page7()
    {

        // 工作人员查看生成增加颜色
        if ($this->data['page7'] && $this->type==2) {
            foreach ($this->data['page7'] as $key => $val) {
                if (!is_array($val)) {
                    $this->data['page7'][$key] = '<span style="color:red">'.$val.'</span>';
                } else {
                    $redTable = [];
                    foreach ($val as  $row) {
                        foreach ($row as $index => $field ) {
                            $row[$index] = '<span style="color:red">'.$field.'</span>';
                        }
                        $redTable[] = $row;
                    }
                    $this->data['page7'][$key] = $redTable;
                }

            }
        }

        // 提取数组中的键值对为变量
        extract($this->data['page7']);

        $this->pdf->AddPage();

        // 主要产品类别
        $table1 = '
         <table  border="0">';
        $table1 .= '<tr class="th"><td class="td" style="width: 20%">客户名称</td><td class="td" style="width:20%">国家/地区</td><td class="td" style="width: 20%">所属行业</td><td class="td" style="width: 20%">年度交易金额</td><td class="td" style="width: 20%">备注</td></tr>';
        foreach ($this->data['page7']['field1'] as $row) {
            $table1 .= '<tr class="th">';
            $table1 .= '<td class="td" style="width: 20%">' . $row[0] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[1] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[2] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[3] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[4] . '</td>';
            $table1 .= '</tr>';
        }
        $table1 .= '</table>';

        // 2.产品应用案例
        $table2 = '
         <table  border="0">';
        $table2 .= '<tr class="th"><td class="td" style="width: 25%">项目名称</td><td class="td" style="width:25%">涉及国家/地区</td><td class="td" style="width: 25%">供应产品情况</td><td class="td" style="width: 25%">供货金额</td></tr>';
        foreach ($this->data['page7']['field2'] as $row) {
            $table2 .= '<tr class="th">';
            $table2 .= '<td class="td" style="width: 25%">' . $row[0] . '</td>';
            $table2 .= '<td class="td" style="width: 25%">' . $row[1] . '</td>';
            $table2 .= '<td class="td" style="width: 25%">' . $row[2] . '</td>';
            $table2 .= '<td class="td" style="width: 25%">' . $row[3] . '</td>';
            $table2 .= '</tr>';
        }
        $table2 .= '</table>';

        //3.所获荣誉及表扬信
        $table3 = '
         <table  border="0">';
        $table3 .= '<tr class="th"><td class="td" style="width: 33%">荣誉名称</td><td class="td" style="width:33%">获得时间</td><td class="td" style="width: 33%">颁发单位</td></tr>';
        foreach ($this->data['page7']['field3'] as $row) {
            $table3 .= '<tr class="th">';
            $table3 .= '<td class="td" style="width: 33%">' . $row[0] . '</td>';
            $table3 .= '<td class="td" style="width: 33%">' . $row[1] . '</td>';
            $table3 .= '<td class="td" style="width: 33%">' . $row[2] . '</td>';
            $table3 .= '</tr>';
        }
        $table3 .= '</table>';

        $table4 = '
         <table  border="0">';
        $table4 .= '<tr class="th"><td class="td" style="width: 33%">出具单位</td><td class="td" style="width:33%">涉及项目</td><td class="td" style="width: 33%">出具日期</td></tr>';
        foreach ($this->data['page7']['field4'] as $row) {
            $table4 .= '<tr class="th">';
            $table4 .= '<td class="td" style="width: 33%">' . $row[0] . '</td>';
            $table4 .= '<td class="td" style="width: 33%">' . $row[1] . '</td>';
            $table4 .= '<td class="td" style="width: 33%">' . $row[2] . '</td>';
            $table4 .= '</tr>';
        }
        $table4 .= '</table>';


        $htmlStyle = '
            <style>
              tr { page-break-inside:avoid; page-break-after:auto }
             td { page-break-inside:avoid; page-break-after:auto }
             table{
              width: 100%;
              border-collapse: collapse;
              margin: 0 auto; /* 居中显示 */
              page-break-inside:auto
            }
            .th{
                height: 40px;
            }
            .td{
                
                font-size: 14px;
                line-height: 30px;
                text-align: center;
                width: 40%;
                border:1px solid #999;
            }
             .td-left{
                
                font-size: 14px;
                line-height: 30px;
                width: 56%;
                margin-left: 10px;
                border: 1px solid black;
            }
            .td-center{
                text-align: center;
                line-height: 30px;
                width:4%;
                border: 1px solid black;
            }
            .text-page2{
                line-height: 24px;
                text-align: left;
                font-size: 12px;
            }
            .bold {
                font-weight: bold;
            }

        </style>';

        $htmlTable = $htmlStyle.'<div style="font-size: 16px; text-align: center;font-family:SimHei;font-weight:bold;">第四部分客户及业绩</div>
        <div class="text-page2">
            客户和业绩部分提供了目标公司主要客户名单、产品应用案例和所获荣誉的摘要。本节中的信息基于目标公司提供的数据、公开来源和第三方验证（如适用）。
        </div>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">1.前五大主要客户情况</span><br>
            截至报告基准日，根据商业交易、供应协议或长期合作关系，以下公司已被确定为目标公司的主要客户。
        </div>
        <div style="text-align: right">人民币：元</div>
        '.$table1.'
        <br>';

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

        $htmlTable = $htmlStyle.'<div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">2.产品应用案例</span><br>
            截至报告基准日，以下产品已为大型项目或客户提供。这些信息表明目标公司过去在履行合同义务方面的表现和可靠性。
        </div>
        <div style="text-align: right">人民币：元</div>
        '.$table2.'
        <br>';

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

        $htmlTable = $htmlStyle.'<div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">3.所获荣誉及表扬信</span><br>
         截至报告基准日，目标公司已报告了以下荣誉及表扬信。<br>
         (1)目标公司所获荣誉情况如下：<br>
         '.$table3.' <br><br>
         (2)目标公司所获表扬信情况如下：<br>
         '.$table4.'
        </div><br>';


        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

    }

    public function page8()
    {

        // 工作人员查看生成增加颜色
        if ($this->data['page8'] && $this->type==2) {
            foreach ($this->data['page8'] as $key => $val) {
                if (!is_array($val)) {
                    $this->data['page8'][$key] = '<span style="color:red">'.$val.'</span>';
                } else {
                    $redTable = [];
                    foreach ($val as  $row) {
                        foreach ($row as $index => $field ) {
                            $row[$index] = '<span style="color:red">'.$field.'</span>';
                        }
                        $redTable[] = $row;
                    }
                    $this->data['page8'][$key] = $redTable;
                }

            }
        }


        // 提取数组中的键值对为变量
        extract($this->data['page8']);

        $this->pdf->AddPage();


        // 1.资质及认证
        $table1 = '
         <table  border="0">';
        $table1 .= '<tr class="th"><td class="td" style="width: 20%">证书名称</td><td class="td" style="width:20%">出具机构</td><td class="td" style="width: 20%">出具日期</td><td class="td" style="width: 20%">证书有效期</td><td class="td" style="width: 20%">认证范围/产品</td></tr>';
        foreach ($this->data['page8']['field1'] as $row) {
            $table1 .= '<tr class="th">';
            $table1 .= '<td class="td" style="width: 20%">' . $row[0] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[1] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[2] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[3] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[4] . '</td>';
            $table1 .= '</tr>';
        }
        $table1 .= '</table>';

        // 知识产权
        $table2 = '
         <table  border="0">';
        $table2 .= '<tr class="th"><td class="td" style="width: 50%">知识产权名称/图片</td><td class="td" style="width:50%">知识产权类型</td></tr>';
        foreach ($this->data['page8']['field2'] as $row) {

            $img = public_path('storage/').$row[0];
            if (is_file($img) && file_exists($img)) {
                $image = '<img src="'.$img.'" height="50">';
            } else {
                $image = $row[0];
            }
            $table2 .= '<tr class="th">';
            $table2 .= '<td class="td" style="width: 50%;">'. $image .'</td>';
            $table2 .= '<td class="td" style="width: 50%">' . $row[1] . '</td>';
            /*$table2 .= '<td class="td" style="width: 20%">' . htmlspecialchars($row[3]) . '</td>';
            $table2 .= '<td class="td" style="width: 20%">' . htmlspecialchars($row[4]) . '</td>';
            $table2 .= '<td class="td" style="width: 20%">' . htmlspecialchars($row[2]) . '</td>';*/
            $table2 .= '</tr>';
        }
        $table2 .= '</table>';

        // 3.重大合同
        $table3 = '
         <table  border="0">';
        $table3 .= '<tr class="th"><td class="td" style="width: 20%">合同类型</td><td class="td" style="width:20%">主要内容</td><td class="td" style="width: 20%">涉及金额</td><td class="td" style="width: 20%">签订日期</td><td class="td" style="width: 20%">履行状态</td></tr>';
        foreach ($this->data['page8']['field3'] as $row) {

            $table3 .= '<tr class="th">';
            $table3 .= '<td class="td" style="width: 20%;">'. $row[0].'</td>';
            $table3 .= '<td class="td" style="width: 20%">' . $row[1] . '</td>';
            $table3 .= '<td class="td" style="width: 20%">' . $row[2] . '</td>';
            $table3 .= '<td class="td" style="width: 20%">' . $row[3] . '</td>';
            $table3 .= '<td class="td" style="width: 20%">' . $row[4] . '</td>';
            $table3 .= '</tr>';
        }
        $table3 .= '</table>';

        //4.重大诉讼及仲裁信息
        $table4 = '
         <table  border="0">';
        $table4 .= '<tr class="th"><td class="td" style="width: 20%">案件名称</td><td class="td" style="width:20%">案件类型</td><td class="td" style="width: 20%">立案日期</td><td class="td" style="width: 20%">受理机构</td><td class="td" style="width: 20%">涉诉金额</td></tr>';
        foreach ($this->data['page8']['field4'] as $row) {

            $table4 .= '<tr class="th">';
            $table4 .= '<td class="td" style="width: 20%;">'. $row[0].'</td>';
            $table4 .= '<td class="td" style="width: 20%">' . $row[1] . '</td>';
            $table4 .= '<td class="td" style="width: 20%">' . $row[2] . '</td>';
            $table4 .= '<td class="td" style="width: 20%">' . $row[3] . '</td>';
            $table4 .= '<td class="td" style="width: 20%">' . $row[4] . '</td>';
            $table4 .= '</tr>';
        }
        $table4 .= '</table>';

        //5.行政处罚记录
        $table5 = '
         <table  border="0">';
        $table5 .= '<tr class="th"><td class="td" style="width: 25%">处罚事由</td><td class="td" style="width:25%">处罚内容</td><td class="td" style="width: 25%">处罚日期</td><td class="td" style="width: 25%">处罚单位</td></tr>';
        foreach ($this->data['page8']['field5'] as $row) {

            $table5 .= '<tr class="th">';
            $table5 .= '<td class="td" style="width: 25%;">'. $row[0].'</td>';
            $table5 .= '<td class="td" style="width: 25%">' . $row[1] . '</td>';
            $table5 .= '<td class="td" style="width: 25%">' . $row[2] . '</td>';
            $table5 .= '<td class="td" style="width: 25%">' . $row[3] . '</td>';
            $table5 .= '</tr>';
        }
        $table5 .= '</table>';

        //6.失信记录
        $table6 = '
         <table  border="0">';
        $table6 .= '<tr class="th"><td class="td" style="width: 50%">失信事件</td><td class="td" style="width:50%">状态</td></tr>';
        foreach ($this->data['page8']['field6'] as $row) {

            $table6 .= '<tr class="th">';
            $table6 .= '<td class="td" style="width: 50%;">'. $row[0].'</td>';
            $table6 .= '<td class="td" style="width: 50%">' . $row[1] . '</td>';
/*            $table6 .= '<td class="td" style="width: 20%">' . htmlspecialchars($row[3]) . '</td>';
            $table6 .= '<td class="td" style="width: 20%">' . htmlspecialchars($row[4]) . '</td>';
            $table6 .= '<td class="td" style="width: 20%">' . htmlspecialchars($row[2]) . '</td>';*/
            $table6 .= '</tr>';
        }
        $table6 .= '</table>';


        $htmlTable = <<<EOD
            <style>
            tr { page-break-inside:avoid; page-break-after:auto }
             td { page-break-inside:avoid; page-break-after:auto }
             table{
              width: 100%;
              border-collapse: collapse;
              margin: 0 auto; /* 居中显示 */
              page-break-inside:auto
            }
            .th{
                height: 40px;
            }
            .td{
                
                font-size: 14px;
                line-height: 30px;
                text-align: center;
                width: 40%;
                border:1px solid #999;
            }
             .td-left{
                
                font-size: 14px;
                line-height: 30px;
                width: 56%;
                margin-left: 10px;
                border: 1px solid black;
            }
            .td-center{
                text-align: center;
                line-height: 30px;
                width:4%;
                border: 1px solid black;
            }
            .text-page2{
                line-height: 24px;
                text-align: left;
                font-size: 12px;
            }
            .bold {
                font-weight: bold;
            }

        </style>
         <div style="font-size: 16px; text-align: center;font-family:SimHei;font-weight:bold;">第五部分法务与合规</div>
        <div class="text-page2">
            法务与合规部分提供了对目标公司资质认证、知识产权、重大合同、重大诉讼及仲裁、行政处罚和信用风险的汇总。本节中提供的信息基于目标公司提供的数据、公开的法律记录和第三方验证（如适用）。
        </div>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">1.资质及认证</span><br>
            截至报告基准日，目标公司已获得以下认证和资质。
        </div>
        $table1
        <br>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">2.知识产权</span><br>
            截至报告基准日，目标公司持有以下知识产权。
        </div>
        $table2
        <br>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">3.重大合同</span><br>
            截至报告基准日，对目标公司经营活动、财务状况或未来发展等具有重要影响的正在履行或将要履行合同。目标公司确定重大合同的标准为：占目标公司年交易额10%的合同或年度框架协议。
        </div>
        <div style="text-align: right">人民币：元</div>
        $table3
        <br>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">4.重大诉讼及仲裁信息</span><br>
            截至报告基准日，目标公司及其控股子公司近三年涉诉金额占目标公司总资产20%以上的诉讼或仲裁案件（目标公司股东涉诉案件不含在调查范围内）。
        </div>
        <div style="text-align: right">人民币：元</div>
        $table4
        <br>
         <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">5.行政处罚记录</span><br>
            截至报告基准日，目标公司存在以下未处理行政处罚。
        </div>
        $table5
        <br>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">6.失信记录</span><br>
           截至报告基准日，目标公司存在以下失信事件。
        </div>
         <div style="text-align: right">人民币：元</div>
         $table6
        <br>
        EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

    }

    public function page9()
    {
        // 提取数组中的键值对为变量

        $this->pdf->AddPage();

        $field4 = $this->data['page9']['field4'];
        if ( $this->type==2) {
            $field4 = '<span style="color:red">'.$this->data['page9']['field4'].'<span/>';
        }

        $table1 = $this->incomeStatement();
        $table2 = $this->cashFlow();
        $table3 = $this->balanceSheet();

        $htmlTable = <<<EOD
         <style>
             tr { page-break-inside:avoid; page-break-after:auto }
             td { page-break-inside:avoid; page-break-after:auto }
             table{
              width: 100%;
              border-collapse: collapse;
              margin: 0 auto; /* 居中显示 */
              page-break-inside:auto
            }
            .th{
                height: 40px;
            }
            .td{
                
                font-size: 14px;
                line-height: 30px;
                text-align: center;
                width: 40%;
                border:1px solid #999;
            }
             .td-left{
                
                font-size: 14px;
                line-height: 30px;
                width: 56%;
                margin-left: 10px;
                border: 1px solid black;
            }
            .td-center{
                text-align: center;
                line-height: 30px;
                width:4%;
                border: 1px solid black;
            }
            .text-page2{
                line-height: 24px;
                text-align: left;
                font-size: 12px;
            }
            .bold {
                font-weight: bold;
            }

        </style>
         <div style="font-size: 16px; text-align: center;font-family:SimHei;font-weight:bold;">第六部分财务报告</div>
        <div class="text-page2">
            财务报告部分提供了对目标公司综合损益表、资产负债表和现金流量表的汇总。本节中提供的信息基于目标公司提供的数据和第三方验证（如适用）。
        </div>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">1.综合损益表</span><br>
            截至报告基准日，以下数据摘自目标公司委托 $field4 出具的审计报告。
        </div>
        <div style="text-align: right">货币：人民币</div>
        $table1
        <br>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">2.资产负债表</span><br>
            截至报告基准日，以下数据摘自目标公司审计报告。
        </div>
        <div style="text-align: right">货币：人民币</div>
        $table3
        <br>
        <div class="text-page2">
        <span style="font-family:SimHei;font-weight:bold;">3.现金流量表</span><br>
            截至报告基准日，以下数据摘自目标公司审计报告。
        </div>
        <div style="text-align: right">货币：人民币</div>
        $table2
        <br>
        EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

    }

    public function page10()
    {
        // 提取数组中的键值对为变量
        extract($this->data['page10']);

        $this->pdf->AddPage();

        if ($this->type == 2) {
            $field1 = '<span style="color:red">'.$field1.'</span>';
        }

        $htmlTable = <<<EOD
        <style>
            .text-page2{
                line-height: 24px;
                text-align: left;
                font-size: 12px;
            }
            .bold {
                font-weight: bold;
            }

        </style>
         <div style="font-size: 16px; text-align: center;font-family:SimHei;font-weight:bold;">第七部分未来规划</div>
        <div class="text-page2">
            未来规划部分提供了目标公司发展战略，为实现战略目标已采取的措施及实施效果，未来具体发展计划及采取的措施。下述资讯由目标公司自行提供，本报告仅摘录展示。
        </div>
        <div class="text-page2">
         $field1
        </div>
        EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

    }

    public function page11()
    {
        $this->pdf->AddPage();

        // 收集临时文件路径
        $tmpFile = [];

        foreach ($this->data['page11'] as $key => $value) {
            $$key = '<div style="text-align: left">';
            if ($value) {
                foreach ($value as $file) {
                    // 获取文件名的后缀
                    $extension = strtolower(substr($file, -4));
                    if ($extension === '.pdf') {
                        if (Storage::disk('public')->exists($file)) {
                            $pdfFile = viewFileUrl($file);
                        } else {
                            $pdfFile = OssService::link($file);
                        }
                       // $pdfFile =  storage_path('app/public/'.$file);
                        $outPutPath = storage_path('app/public/gsp/report/tmp/');
                        $imgArr = $this->convertPdfToImages($pdfFile, $outPutPath);
                        if ($imgArr) {
                            foreach ($imgArr as $img) {
                                $file = public_path('storage/').$img;
                                if (is_file($file) && file_exists($file)) {
                                    $$key .= '<Br><img src="'.$file.'" width="300">';
                                    $tmpFile[] = storage_path('app/public/').$img;;
                                }
                            }
                        }
                    } else if (in_array($extension, ['.jpg','.jpeg', '.png'])){
                        if (Storage::disk('public')->exists($file)) {
                            $file = viewFileUrl($file);
                        } else {
                            $file = OssService::link($file);
                        }
                        //$file = public_path('storage/').$file;
                        // 获取 HTTP 响应头
                        $headers = @get_headers($file);
                        // 检查是否成功获取头信息
                        if ($headers && strpos($headers[0], '200 OK') !== false) {
                            $$key .= '<img src="'.$file.'" width="300">';
                        }
                    }

                }
            }

            $$key .= '</div>';
        }



        $htmlTable = <<<EOD
            <style>
                .text-page2{
                    line-height: 24px;
                    text-align: left;
                    font-size: 12px;
                }
            </style>
            <div style="font-size: 16px; text-align: center;font-family:SimHei;font-weight:bold;">第八部分附件</div>
            <div class="text-page2">
            <b>1.公司信息</b><br>
            （1）营业执照<br>
             $field1<br>
            （2）企业信用信息公示报告<br>
             $field2<br>
            （3）社会保险单位参保证明<br>
             $field3<br>
            （4）征信报告
             $field4<br>
            </div>
            <div class="text-page2">
             <b>2.业务资质及荣誉</b><br>
            （1）资质及认证证书<br>
             $field5<br>
            （2）荣誉证书及表扬信<br>
             $field6<br>
            </div>
             <div class="text-page2">
             <b>3.权利证书</b><br>
            （1）注册商标证书<br>
             $field7<br>
            （2）专利证书<br>
             $field8<br>
            （3）著作权/软件著作权登记证书<br>
             $field9<br>
            （4）办公及生产场地不动产权证书/租赁合同<br>
             $field10<br>
            </div>
            <div class="text-page2">
             <b>4.公司相关现场照片</b><br>
            （1）主要产品照片<br>
             $field11<br>
            （2）主要生产设备照片<br>
             $field12<br>
            （3）生产场地及办公场地照片<br>
             $field13<br>
            </div>
            <div class="text-page2">
             <b>5.审计报告</b><br>
             $field14<br>
            </div>
            <div class="text-page2">
             <b>6.公司声明</b><br>
             （1）公司控股股东、实际控制人声明<br>
              $field15<br>
             （2）公司全体董事、监事、高级管理人员声明<br>
              $field16
            </div>
         EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

        // 清空临时图片
        if ($tmpFile) {
            foreach ($tmpFile as $item) {
                unlink($item);
            }
        }

    }

    /**
     * 利润表
     * @return string
     */
    public function incomeStatement()
    {

        // 利润表
        foreach ($this->data['page9']['field1'][0] as $key => $value) {
            $newKey = $key . '1';
            if (is_numeric($value)) {
                if ($value < 0) {
                    $value = abs($value);

                    $$newKey = '('.number_format($value, 2).')';
                    if ($this->type == 2) {
                        $$newKey = '<span style="color:red">'.$$newKey.'</span>';

                    }
                } else {

                    if ($key != 'year') {
                        $$newKey = number_format($value, 2);
                    } else {
                        $$newKey = $value;
                    }

                    if ($this->type == 2) {
                        $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                    }


                }
            }

        }

        foreach ($this->data['page9']['field1'][1] as $key => $value) {
            $newKey = $key . '2';
            if (is_numeric($value)) {
                if ($value < 0) {
                    $value = abs($value);
                    $$newKey = '('.number_format($value, 2).')';
                    if ($this->type == 2) {
                        $$newKey = '<span style="color:red">'.$$newKey.'</span>';

                    }
                } else {
                    if ($key != 'year') {
                        $$newKey = number_format($value, 2);
                    } else {
                        $$newKey = $value;
                    }

                    if ($this->type == 2) {
                        $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                    }

                }
            }

        }



        // 合并单元格
        $html = <<<EOD
        <table border="1" cellpadding="2">
           <tr>
                <td align="center">项目</td>
                <td align="center">$year1</td>
                <td align="center">$year2</td>
           </tr>
           <tr>
                <td></td>
                <td align="center">元</td>
                <td align="center">元</td>
           </tr>
           <tr>
                <td>营业收入</td>
                <td align="right">$revenue1</td>
                <td align="right">$revenue2</td>
           </tr>
           <tr>
                <td>营业成本</td>
                <td align="right">$cogs1</td>
                <td align="right">$cogs2</td>
           </tr>
           <tr>
                <td>毛利</td>
                <td align="right">$gross_profit1</td>
                <td align="right">$gross_profit2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td>研发费用</td>
                <td align="right">$research_and_development_expense1</td>
                <td align="right">$research_and_development_expense2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>销售及管理费用</td>
                <td align="right">$general_and_administrative_expense1</td>
                <td align="right">$general_and_administrative_expense2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>营业外支出</td>
                <td align="right">$other_operating_expenses1</td>
                <td align="right">$other_operating_expenses2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>营业外收入</td>
                <td align="right">$other_operating_income1</td>
                <td align="right">$other_operating_income2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>营业利润</td>
                <td align="right">$oprating_profit1</td>
                <td align="right">$oprating_profit2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>财务收入</td>
                <td align="right">$finance_income1</td>
                <td align="right">$finance_income2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>财务费用</td>
                <td align="right">$finance_expenses1</td>
                <td align="right">$finance_expenses2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>对联营企业和合营企业的投资收益</td>
                <td align="right">$share_of_net_profit1</td>
                <td align="right">$share_of_net_profit2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>税前利润</td>
                <td align="right">$profit_before_tax1</td>
                <td align="right">$profit_before_tax2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>减：所得税费用</td>
                <td align="right">$taxation1</td>
                <td align="right">$taxation2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>本期利润</td>
                <td align="right">$profit_for_the_period1</td>
                <td align="right">$profit_for_the_period2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>其他综合收益</td>
                <td align="right">$other_comprehensive_income1</td>
                <td align="right">$other_comprehensive_income2</td>
            </tr>
             <tr>
                <td>不能重分类进损益的其他综合收益</td>
                <td align="right">$items_that_will_not_be_reclassified_to_profit_and_loss1</td>
                <td align="right">$items_that_will_not_be_reclassified_to_profit_and_loss2</td>
            </tr>
            <tr>
                <td>其他综合收益</td>
                <td align="right">$other_comprehensive_income_all1</td>
                <td align="right">$other_comprehensive_income_all2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>随后可重新分类至损益的项目：</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>现金流量套期保值收益/（损失）</td>
                <td align="right">$gains_losses_on_cash_flow_hedges1</td>
                <td align="right">$gains_losses_on_cash_flow_hedges2</td>
            </tr>
            <tr>
                <td>货币重估收益 /（损失）</td>
                <td align="right">$currency_retranslation_gains1</td>
                <td align="right">$currency_retranslation_gains2</td>
            </tr>
            <tr>
                <td>本期其他综合（费用）/收入，税后净额</td>
                <td align="right">$other_comprehensive_expense1</td>
                <td align="right">$other_comprehensive_expense2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>综合收益总额</td>
                <td align="right">$total_comprehensive_income_for_the_period1</td>
                <td align="right">$total_comprehensive_income_for_the_period2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
        </table>'
        EOD;

        return $html;

    }

    /**
     * 现金流量表
     * @return void
     */
    public function cashFlow()
    {
        foreach ($this->data['page9']['field2'][0] as $key => $value) {
            $newKey = $key . '1';
            if ($value < 0) {
                $value = abs($value);
                $$newKey = '('.number_format($value, 2).')';
                if ($this->type == 2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            } else {
                if ($key != 'year') {
                    $$newKey = number_format($value, 2);
                } else {
                    $$newKey = $value;
                }
                if ($this->type == 2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            }
        }

        foreach ($this->data['page9']['field2'][1] as $key => $value) {
            $newKey = $key . '2';
            if ($value < 0) {
                $value = abs($value);
                $$newKey = '('.number_format($value, 2).')';
                if ($this->type == 2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            } else {
                if ($key != 'year') {
                    $$newKey = number_format($value, 2);
                } else {
                    $$newKey = $value;
                }
                if ($this->type == 2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            }
        }


        // 合并单元格
        $html = <<<EOD
        <table border="1" cellpadding="2">
           <tr>
                <td align="center">项目</td>
                <td align="center">$year1</td>
                <td align="center">$year2</td>
           </tr>
           <tr>
                <td></td>
                <td  align="center">元</td>
                <td  align="center">元</td>
           </tr>
            <tr>
                <td>经营活动产生的现金流量</td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td>销售商品、提供劳务收到的现金</td>
                <td align="right">$cash_received_from_the_sale1</td>
                <td align="right">$cash_received_from_the_sale2</td>
           </tr>
            <tr>
                <td>收到的税费返还</td>
                <td align="right">$tax_refunds_received1</td>
                <td align="right">$tax_refunds_received2</td>
           </tr>
            <tr>
                <td>收到其他与经营活动有关的现金</td>
                <td align="right">$other_cash_received_in_connection_with_operating_activities1</td>
                <td align="right">$other_cash_received_in_connection_with_operating_activities2</td>
           </tr>
           <tr>
                <td>经营活动现金流入小计</td>
                <td align="right">$subtotal_cash_inflows_from_operating_activities1</td>
                <td align="right">$subtotal_cash_inflows_from_operating_activities2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>购买商品、接受劳务支付的现金</td>
                <td align="right">$cash_for_the_purchase1</td>
                <td align="right">$cash_for_the_purchase2</td>
           </tr>
           <tr>
                <td>支付给职工以及为职工支付的现金</td>
                <td align="right">$cash_paid_to_and_on_behalf1</td>
                <td align="right">$cash_paid_to_and_on_behalf2</td>
           </tr>
           <tr>
                <td>支付的各项税费</td>
                <td align="right">$taxes_and_fees_paid1</td>
                <td align="right">$taxes_and_fees_paid2</td>
           </tr>
           <tr>
                <td>支付其他与经营活动有关的现金</td>
                <td align="right">$payment_of_other_cash_related_to_operating_activities1</td>
                <td align="right">$payment_of_other_cash_related_to_operating_activities2</td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td><b>经营活动现金流出小计</b></td>
                <td align="right">$subtotal_cash_otflows_from_operating_activities1</td>
                <td align="right">$subtotal_cash_otflows_from_operating_activities2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td><b>投资活动产生的现金流量</b></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td><b>处置固定资产的收益：</b></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>处置固定资产、无形资产和其他长期资产收回的现金净额</td>
                <td align="right">$proceeds_from_sale_of_plant1</td>
                <td align="right">$proceeds_from_sale_of_plant2</td>
           </tr>
            <tr>
                <td>收回投资收到的现金</td>
                <td align="right">$proceeds_from_sale_of_investments1</td>
                <td align="right">$proceeds_from_sale_of_investments2</td>
           </tr>
           <tr>
                <td>出售商誉、专利权、商标等的收益。</td>
                <td align="right">$proceeds_from_sale_of_goodwill1</td>
                <td align="right">$proceeds_from_sale_of_goodwill2</td>
           </tr>
           <tr>
                <td>处置子公司及其他营业单位收到的现金净额</td>
                <td align="right">$net_cash_received_from_sales_of_subsidiaries1</td>
                <td align="right">$net_cash_received_from_sales_of_subsidiaries2</td>
           </tr>
           <tr>
                <td>收到其他与投资活动有关的现金</td>
                <td align="right">$other_proceeds_from_investments1</td>
                <td align="right">$other_proceeds_from_investments2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td>加：取得投资收益收到的现金</td>
                <td align="right">$operating_incomes_from_investments1</td>
                <td align="right">$operating_incomes_from_investments2</td>
           </tr>
            <tr>
                <td>收到的股息</td>
                <td align="right">$dividends_received1</td>
                <td align="right">$dividends_received2</td>
           </tr>
           <tr>
                <td>收到的利息</td>
                <td align="right">$interest_received1</td>
                <td align="right">$interest_received2</td>
           </tr>
           <tr>
                <td>收到的物业租金</td>
                <td align="right">$rent_on_property_received1</td>
                <td align="right">$rent_on_property_received2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>减少：非流动资产采购：</td>
                <td align="right"></td>
                <td align="right"></td>
           </tr>
            <tr>
                <td>购建固定资产, 和其他长期资产支付的现金</td>
                <td align="right">$purchase_of_plant1</td>
                <td align="right">$purchase_of_plant2</td>
           </tr>
           <tr>
                <td>购买投资/金融资产</td>
                <td align="right">$purchase_of_investments1</td>
                <td align="right">$purchase_of_investments2</td>
           </tr>
           <tr>
                <td>购买合资企业和联营企业</td>
                <td align="right">$purchase_of_joint_ventures_and_associates1</td>
                <td align="right">$purchase_of_joint_ventures_and_associates2</td>
           </tr>
           <tr>
                <td>无形资产的购买—商誉、专利权、商标等。</td>
                <td align="right">$purchase_of_intangible_assets1</td>
                <td align="right">$purchase_of_intangible_assets2</td>
           </tr>
            <tr>
                <td>支付其他与投资活动有关的现金</td>
                <td align="right">$other_payments_for_investing_activities1</td>
                <td align="right">$other_payments_for_investing_activities2</td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>净现金流入/(流出）（用于/来自投资活动）</td>
                <td align="right">$net_cash_from_investing_activities1</td>
                <td align="right">$net_cash_from_investing_activities2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td><b>融资活动产生的现金流量</b></td>
                <td align="right"></td>
                <td align="right"></td>
           </tr>
           <tr>
                <td><b>发行股本和借款的现金流。</b></td>
                <td align="right">$proceeds_from_issue_of_share_capital_and_borrowings1</td>
                <td align="right">$proceeds_from_issue_of_share_capital_and_borrowings2</td>
           </tr>
            <tr>
                <td>发行普通股所得款项资本</td>
                <td align="right">$proceeds_from_issue_of_ordinary_shares_capital1</td>
                <td align="right">$proceeds_from_issue_of_ordinary_shares_capital2</td>
           </tr>
            <tr>
                <td>发行优先股所得款项资本</td>
                <td align="right">$proceeds_from_issue_of_preference_shares_capital1</td>
                <td align="right">$proceeds_from_issue_of_preference_shares_capital2</td>
           </tr>
           <tr>
                <td>贷款和借款等所得款项</td>
                <td align="right">$proceeds_from_loans_and_borrowings_etc1</td>
                <td align="right">$proceeds_from_loans_and_borrowings_etc2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>（-） 回购普通股</td>
                <td align="right">$buy_back_of_equity_shares1</td>
                <td align="right">$buy_back_of_equity_shares2</td>
           </tr>
            <tr>
                <td>（-） 优先股的赎回</td>
                <td align="right">$redemption_of_preference_shares1</td>
                <td align="right">$redemption_of_preference_shares2</td>
           </tr>
           <tr>
                <td>（-） 偿还贷款和借款</td>
                <td align="right">$repayment_of_loans_and_borrowings1</td>
                <td align="right">$repayment_of_loans_and_borrowings2</td>
           </tr>
            <tr>
                <td>（-） 债券赎回</td>
                <td align="right">$redemption_of_debentures1</td>
                <td align="right">$redemption_of_debentures2</td>
           </tr>
           <tr>
                <td>（-） 支付普通股股息</td>
                <td align="right">$dividends_paid_on_equity_shares1</td>
                <td align="right">$dividends_paid_on_equity_shares2</td>
           </tr>
           <tr>
                <td>（-） 优先股股息</td>
                <td align="right">$dividend_on_preference_shares1</td>
                <td align="right">$dividend_on_preference_shares2</td>
           </tr>
           <tr>
                <td>（-） 租赁债务的偿还</td>
                <td align="right">$repayment_of_obligations_under_leases1</td>
                <td align="right">$repayment_of_obligations_under_leases2</td>
           </tr>
            <tr>
                <td>其他融资活动付款</td>
                <td align="right">$other_payments_for_financing_activities1</td>
                <td align="right">$other_payments_for_financing_activities2</td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td><b>净现金（用于）/来自融资活动</b></td>
                <td align="right">$from_financing_activities1</td>
                <td align="right">$from_financing_activities2</td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td>现金及现金等价物净增加额</td>
                <td align="right">$net_increase1</td>
                <td align="right">$net_increase2</td>
           </tr>
           <tr>
                <td>加：期初现金及现金等价物余额</td>
                <td align="right">$cash_equivalents_at_the_beginning_of_the_period1</td>
                <td align="right">$cash_equivalents_at_the_beginning_of_the_period2</td>
           </tr>
            <tr>
                <td>汇率变动对现金及现金等价物的影响</td>
                <td align="right">$effect_of_foreign_exchange_rate_changes1</td>
                <td align="right">$effect_of_foreign_exchange_rate_changes2</td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>期末现金及现金等价物余额</td>
                <td align="right">$cash_equivalents_at_the_end_of_the_year1</td>
                <td align="right">$cash_equivalents_at_the_end_of_the_year2</td>
           </tr>
        </table>
        EOD;

       return $html;

    }

    /**
     * 资产负债表
     * @return void
     */
    public function balanceSheet()
    {
        foreach ($this->data['page9']['field3'][0] as $key => $value) {
            $newKey = $key . '1';
            if ($value < 0) {
                $value = abs($value);
                $$newKey = '('.number_format($value, 2).')';
                if ($this->type == 2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            } else {
                if ($key != 'year') {
                    $$newKey = number_format($value, 2);
                } else {
                    $$newKey = $value;
                }

                if ($this->type == 2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            }
        }

        foreach ($this->data['page9']['field3'][1] as $key => $value) {
            $newKey = $key . '2';
            if ($value < 0) {
                $value = abs($value);
                $$newKey = '('.number_format($value, 2).')';
                if ($this->type == 2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            } else {
                if ($key != 'year') {
                    $$newKey = number_format($value, 2);
                } else {
                    $$newKey = $value;
                }

                if ($this->type == 2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            }
        }

        // 合并单元格
        $html = <<<EOD
        <table border="1" cellpadding="2">
           <tr>
                <td align="center">项目</td>
                <td align="center">$year1</td>
                <td align="center">$year2</td>
           </tr>
           <tr>
                <td></td>
                <td  align="center">元</td>
                <td  align="center">元</td>
           </tr>
           <tr>
                <td>资产合计</td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td>非流动资产：</td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>固定资产</td>
                <td align="right">$property_plant_and_equipment1</td>
                <td align="right">$property_plant_and_equipment2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>商誉</td>
                <td  align="right">$goodwill1</td>
                <td  align="right">$goodwill2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>无形资产</td>
                <td  align="right">$intangible_assets1</td>
                <td  align="right">$intangible_assets2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>使用权资产</td>
                <td  align="right">$right_of_use_assets1</td>
                <td  align="right">$right_of_use_assets2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>投资性房地产</td>
                <td  align="right">$investments_properties1</td>
                <td  align="right">$investments_properties2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>债权投资</td>
                <td  align="right">$other_investments_all1</td>
                <td  align="right">$other_investments_all2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>长期应收款</td>
                <td align="right">$long_term_receivables1</td>
                <td align="right">$long_term_receivables2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>递延所得税资产</td>
                <td align="right">$deferred_tax_assets1</td>
                <td align="right">$deferred_tax_assets2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>其他非流动资产</td>
                <td align="right">$other_non_current_assets_all1</td>
                <td align="right">$other_non_current_assets_all2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>非流动资产 （共）</td>
                <td  align="right">$total_non_current_assets1</td>
                <td  align="right">$total_non_current_assets2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>流动资产</td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>存货</td>
                <td  align="right">$inventories1</td>
                <td  align="right">$inventories2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>应收账款</td>
                <td  align="right">$trade_and_other_receivables1</td>
                <td  align="right">$trade_and_other_receivables2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>其它投资 （共）</td>
                <td  align="right">$other_investments1</td>
                <td  align="right">$other_investments2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>其它流动资产 （共）</td>
                <td  align="right">$other_current_assets_all1</td>
                <td  align="right">$other_current_assets_all2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>应回收所得税费</td>
                <td  align="right">$income_tax_receivables1</td>
                <td  align="right">$income_tax_receivables2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>现金加同等价值</td>
                <td  align="right">$cash_and_cash_equivalents1</td>
                <td  align="right">$cash_and_cash_equivalents2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>流动资产 (共）</td>
                <td  align="right">$total_current_assets1</td>
                <td  align="right">$total_current_assets2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>总资产</td>
                <td  align="right">$total_assets1</td>
                <td  align="right">$total_assets2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>负债</td>
                <td  align="right"></td>
                <td  align="right"></td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>流动负债</td>
                <td  align="right"></td>
                <td  align="right"></td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>短期借款及金融负债</td>
                <td  align="right">$interest_bearing_loans_and_borrowings_short1</td>
                <td  align="right">$interest_bearing_loans_and_borrowings_short2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>租赁负债</td>
                <td  align="right">$lease_liabilities_short1</td>
                <td  align="right">$lease_liabilities_short2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>应付账款及其他应付款</td>
                <td  align="right">$trade_and_other_payables1</td>
                <td  align="right">$trade_and_other_payables2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>会计拨备或应计</td>
                <td  align="right">$provisions_short1</td>
                <td  align="right">$provisions_short2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>应付所得税/应交税费</td>
                <td  align="right">$income_tax_payables_short1</td>
                <td  align="right">$income_tax_payables_short2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>其他流动负债 (共）</td>
                <td  align="right">$other_current_liabilities_all1</td>
                <td  align="right">$other_current_liabilities_all2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
               <tr>
                <td>流动负债 （共）</td>
                <td  align="right">$total_current_liabilities1</td>
                <td  align="right">$total_current_liabilities2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>非流动负债</td>
                <td  align="right"></td>
                <td  align="right"></td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>长期借款及贷款</td>
                <td  align="right">$interest_bearing_loans_and_borrowings_long1</td>
                <td  align="right">$interest_bearing_loans_and_borrowings_long2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>租赁负债</td>
                <td  align="right">$lease_liabilities_long1</td>
                <td  align="right">$lease_liabilities_long2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>递延所得税负债</td>
                <td  align="right">$deferred_tax_liabilities1</td>
                <td  align="right">$deferred_tax_liabilities2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>会计拨备或应计</td>
                <td  align="right">$provisions_long1</td>
                <td  align="right">$provisions_long2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>应付所得税/应交税费</td>
                <td  align="right">$income_tax_payables_long1</td>
                <td  align="right">$income_tax_payables_long2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>其它应付账款 (共）</td>
                <td  align="right">$other_paybales1</td>
                <td  align="right">$other_paybales2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>其他非流动负债 （共）</td>
                <td  align="right">$other_non_current_liabilities_all1</td>
                <td  align="right">$other_non_current_liabilities_all2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>非流动负债 （共）</td>
                <td  align="right">$total_non_current_liabilities1</td>
                <td  align="right">$total_non_current_liabilities2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>负债 （共）</td>
                <td  align="right">$total_liabilities1</td>
                <td  align="right">$total_liabilities2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>总股本</td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>归属于公司股东的资本及储备金</td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>实收资本（或股本）</td>
                <td  align="right">$ordinary_share_capital1</td>
                <td  align="right">$ordinary_share_capital2</td>
             </tr>
              <tr>
                <td>优先股 （资本部分）</td>
                <td  align="right">$preferred_share_holders1</td>
                <td  align="right">$preferred_share_holders2</td>
             </tr>
             <tr>
                <td>资本公积</td>
                <td  align="right">$share_premium1</td>
                <td  align="right">$share_premium2</td>
             </tr>
             <tr>
                <td>股本</td>
                <td  align="right">$share_capital1</td>
                <td  align="right">$share_capital1</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>其他综合收益</td>
                <td  align="right">$other_comprehensive_income1</td>
                <td  align="right">$other_comprehensive_income2</td>
             </tr>
             <tr>
                <td>专项储备</td>
                <td  align="right">$special_reserves1</td>
                <td  align="right">$special_reserves2</td>
             </tr>
             <tr>
                <td>盈余公积</td>
                <td  align="right">$surplus_reserves1</td>
                <td  align="right">$surplus_reserves2</td>
             </tr>
              <tr>
                <td></td>
                <td  align="right">$other_reserves1</td>
                <td  align="right">$other_reserves2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>未分配利润</td>
                <td  align="right">$retained_eranings1</td>
                <td  align="right">$retained_eranings2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>总股本</td>
                <td  align="right">$total_equity1</td>
                <td  align="right">$total_equity2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>权益和负债总计</td>
                <td  align="right">$total_equity_and_liabilities1</td>
                <td  align="right">$total_equity_and_liabilities2</td>
             </tr>
        </table>
        EOD;

        return $html;

    }

    /**
     * 将 PDF 文件转换为图片
     *
     * @param string $pdfPath PDF 文件路径
     * @param string $outputDir 输出图片的目录
     * @return array 返回包含图片路径的数组
     */
    function convertPdfToImages($pdfFile, $outputDir) {

        // 确保输出目录存在
        if (!file_exists($outputDir)) {
            mkdir($outputDir, 0777, true);
        }

        // 本地临时文件路径
        $tempPdfFile = $outputDir . '/temp_file'.time().'.pdf';
        // 下载 PDF 文件到本地
        if (!copy($pdfFile, $tempPdfFile)) {
            throw new Exception("无法下载 PDF 文件。");
        }


        // 定义输入 PDF 文件路径和输出 JPG 文件路径
        $uniquePrefix = uniqid('output_', true);

        // 构建 Ghostscript 命令
        $outputFile = $outputDir . '/' . $uniquePrefix . '_%03d.jpg'; // 输出文件名格

        // 构建 Ghostscript 命令
        $command = 'gs -sDEVICE=jpeg -r200 -o '.$outputFile.' -dJPEGQ=100 '.$tempPdfFile;
        // 调用系统命令
        exec($command, $output, $returnVar);

        // 检查命令执行是否成功
        if ($returnVar !== 0) {
            throw new Exception("PDF 转换为 JPG 失败，错误信息：\n" . implode("\n", $output) . "\n返回状态码: $returnVar");
        }

        // 获取输出目录中的所有 JPG 文件
        $jpgFiles = glob($outputDir . '/*.jpg');

        // 确保返回的文件路径是本次转换生成的文件
        $convertedFiles = [];
        foreach ($jpgFiles as $jpgFile) {
            if (strpos(basename($jpgFile), $uniquePrefix) === 0) {
                $file = substr($jpgFile, strpos($jpgFile, 'gsp'));
                $convertedFiles[] = $file;
            }
        }

        unlink($tempPdfFile);

        return $convertedFiles;

    }
}