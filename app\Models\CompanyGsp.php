<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CompanyGsp extends Model
{
    protected $table = 'cna_company_gsp';
    protected $guarded = [];

    //账号状态
    const STATUS_UPLOAD_FORM = 1; // 提交预审表格
    const STATUS_FORM_SUCCESS = 2; // 预审通过
    const STATUS_FORM_FAIL = 3; // 预审不通过
    const STATUS_UPLOAD_CONTRACT = 4; // 上传签署合同
    const STATUS_CONTRACT_SUCCESS = 5; // 合同审核通过
    const STATUS_CONTRACT_FAIL = 6; // 合同审核不通过
    const STATUS_POST_REPORT = 7; // 提交尽调报告
    const STATUS_COMPANY_REPORT_SUCCESS = 8; // 企业审核通过
    const STATUS_COMPANY_REPORT_FAIL = 9; // 企业审核不通过
    const STATUS_REPORT_SUCCESS = 10; // 材料审核通过
    const STATUS_REPORT_FAIL = 11; // 材料审核不通过

    const STATUS_REPORT_CA_SUCCESS = 12; // 尽调报告审核通过
    const STATUS_REPORT_CA_FAIL = 13; // 尽调报告审核不通过

    const STATUS_ACTIVE = 14; // 已完成

    public function profile()
    {
        return $this->belongsTo(ProfileInfoModel::class, 'profile_id', 'profileID');
    }

    public static  function reportNumber($id)
    {
        $number = 'cagsp'.date('Ymd').$id;
        return $number;
    }
}
