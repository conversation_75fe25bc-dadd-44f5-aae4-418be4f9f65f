<?php

namespace App\Models;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class AiCheck extends Model
{
    use HasFactory;

    // 公共的请求函数
    /**
     * 发送HTTP POST请求到指定的URL，并返回响应结果。
     *
     * 该函数使用cURL库发送一个带有JSON数据的POST请求，并设置必要的请求头，
     * 包括API Key的授权信息。如果请求过程中发生错误，会输出错误信息。
     *
     * @param string $url 请求的目标URL。
     * @param array $data 需要发送的POST数据，将会被编码为JSON格式。
     * @return string|false 返回请求的响应结果，如果请求失败则返回false。
     */
    private static function sendRequest($url, $data)
    {
        // 从环境变量中获取API Key
        $apiKey = getenv('API_KEY');

        // 设置请求头，包括授权信息和内容类型
        $headers = [
            'Authorization: Bearer ' . $apiKey,
            'Content-Type: application/json'
        ];

        // 初始化cURL会话
        $ch = curl_init();

        // 配置cURL选项，包括URL、POST请求、请求数据、返回结果和请求头
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        // 执行cURL请求并获取响应
        $response = curl_exec($ch);

        // 检查cURL请求是否出错，如果出错则输出错误信息
        if (curl_errno($ch)) {
            echo 'Curl error: ' . curl_error($ch);
        }

        // 关闭cURL会话以释放资源
        curl_close($ch);

        // 返回请求的响应结果
        return $response;
    }


    private static function getNextQuestionFromRedis($interview_id, $attemptsKey)
    {
        $response = [];

        try {
            $nextQuestionData = Redis::rpop("interview_{$interview_id}_questions");
            if ($nextQuestionData) {
                // 解码下一个问题数据
                $nextQuestion = json_decode($nextQuestionData, true);
                if (json_last_error() === JSON_ERROR_NONE && isset($nextQuestion['question_id'], $nextQuestion['question_text'], $nextQuestion['question_url'])) {
                    $response['nextQuestion'] = [
                        'question_id' => $nextQuestion['question_id'],
                        'question_text' => $nextQuestion['question_text'],
                        'question_url' => $nextQuestion['question_url']
                    ];
                    // 重置回答次数
                    Redis::del($attemptsKey);
                } else {
                    $response['nextQuestion'] = null;
                }
            } else {
                // 如果没有更多问题，面试结束
                $response['nextQuestion'] = null;
            }
        } catch (Exception $e) {
            // 处理异常情况
            $response['nextQuestion'] = null;
        }

        return $response;
    }





    // 回答语音转文字
    public static function speechToText($audioUrl)
    {
//        ["audio" => "https://tts.corporate-advisory.cn/sound/368963e0-a505-4f80-a6fd-056b58b9a640.mp3"]
        // 设置请求的URL
        $url = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation';
    
        $data = [
            "model" => "qwen-audio-turbo-latest",
            "input" => [
                "messages" => [
                    [
                        "role" => "system",
                        "content" => [
                            ["text" => "You are a helpful assistant."]
                        ]
                    ],
                    [
                        "role" => "user",
                        "content" => [
                            ["audio" => $audioUrl],
                            ["text" => "这段音频在说什么?只输出他说了什么，不需要带任何描述"]
                        ]
                    ]
                ]
            ]
        ];
    
        try {
            // 调用公共请求函数
            $res = self::sendRequest($url, $data);
            $response = json_decode($res, true);

            // 成功处理
            if (isset($response['output']['choices'][0]['message']['content'][0]['text'])) {
                return [
                    'status' => true,
                    'text' => $response['output']['choices'][0]['message']['content'][0]['text']
                ];
            }

            // 错误码处理
            if (isset($response['code'])) {
                return [
                    'status' => false,
                    'code' => $response['code'],
                    'message' => $response['message'] ?? '未知错误'
                ];
            }

            throw new Exception("返回格式不符合预期");
        } catch (Exception $e) {
            Log::error('speechToText 函数出错: ' . $e->getMessage());
            return [
                'status' => false,
                'code' => 500,
                'message' => $e->getMessage()
            ];
        }
    }



    public static function testToText($audioFile)
    {
        try {
            $response = Http::attach(
                'file',
                fopen($audioFile->getRealPath(), 'r'),
                $audioFile->getClientOriginalName()
            )->post('https://api.testai.corporate-advisory.cn/tool/api/v1/audio/stt');

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['status']) && $data['status'] === true && isset($data['data']['text'])) {
                    return [
                        'status' => true,
                        'text' => $data['data']['text']
                    ];
                }

                return [
                    'status' => false,
                    'code' => $data['code'] ?? null,
                    'message' => $data['message'] ?? '未知错误',
                ];

            }

            return [
                'status' => false,
                'code' => $response->status(),
                'message' => $response->body(),
            ];
        } catch (Exception $e) {
            Log::error('testToText 异常: ' . $e->getMessage());

            return [
                'status' => false,
                'code' => 500,
                'message' => $e->getMessage(),
            ];
        }
    }





    // 文字转语音
    public static function textToSpeech($question)
    {
        $params = [
            'content' => $question,
            'voice' => 'zhishuo',
//            'rate' => 10,
//            'pitch' => 10,
//            'volume' => 40
        ];

        $response = Http::asForm()->post('https://api.testai.corporate-advisory.cn/tool/api/v1/audio/tts', $params);

        if ($response->successful()) {
            return $response->json();
        } else {
            return [
                'status' => false,
                'code' => $response->status(),
                'message' => $response->body(),
            ];
        }
    }

}
