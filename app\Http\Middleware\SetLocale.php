<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\App;

/**
 * 通过请求头设置语言
 */
class SetLocale
{
    public function handle($request, Closure $next)
    {
        $locale = $request->header('Accept-Language');
        $language = [
            'EN' => 'en',
            'MS' => 'ms',
            'ZH' => 'zh_CN',
            'ZT' => 'zh_TW',
        ];
        $setLanguage = 'ZH';
        if ($locale) {
            foreach ($language as $k => $v) {
                if (strpos($locale, $k) !== false) {
                    $setLanguage = $k;
                    break;
                }
            }
        }
        App::setLocale($language[$setLanguage]);
        return $next($request);
    }
}
