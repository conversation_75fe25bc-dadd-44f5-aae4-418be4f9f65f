<?php

namespace App\Services;

use App\Exceptions\DefaultException;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class DocService
{
    //文档类型
    const TYPE_TEAM = 1; //三三制
    const TYPE_CN_SIGN = 2; //业务服务合同
    const TYPE_BANK_ACCOUNT = 3; //银行卡
    const TYPE_NRIC = 4; //身份证

    //生成文档密钥
    public static function generateToken($docPath, $action, $expire = 60, $name = '')
    {
        $payload = [
            'exp' => time() + $expire,
            'action' => $action,
            'file' => $docPath,
            'name' => $name,
        ];
        return JWT::encode($payload, env('JWT_KEY'), 'HS256');
    }

    //预览文档
    public static function preview($token, $action)
    {
        $decodeToken = self::decodeToken($token, $action);
        $file = $decodeToken->file;
        if (Storage::disk('public')->exists($file)) {
            $fileUrl = viewFileUrl($file);
        } else {
            $fileUrl = OssService::link($file);
        }
        $fileContent = file_get_contents($fileUrl);
        if (!$fileContent) {
            return abort(404, 'File not found.');
        }

        // 获取文件MIME类型
        $finfo = new \finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($fileContent);
        $fileSize = strlen($fileContent);
        return response()->stream(function () use ($fileContent) {
            echo $fileContent;
        }, 200, ['Content-Type' => $mimeType, 'Content-Length' => $fileSize]);
    }

    //下载文档
    public static function download($token, $action)
    {
        $decodeToken = self::decodeToken($token, $action);
        $file = $decodeToken->file;
        if (Storage::disk('public')->exists($file)) {
            $fileUrl = viewFileUrl($file);
        } else {
            $fileUrl = OssService::link($file);
        }
        $fileContent = file_get_contents($fileUrl);
        if (!$fileContent) {
            return abort(404, 'File not found.');
        }

        // 获取文件MIME类型
        $finfo = new \finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($fileContent);

        $headers = [];
        if ($decodeToken->name !== '') {
            $headers['Content-Type'] = $mimeType;
            $headers['Content-Disposition'] = 'attachment; filename="' . $decodeToken->name . '"';
        } else {
            $headers['Content-Type'] = $mimeType;
        }
        return response()->stream(function () use ($fileContent, $headers) {
            echo $fileContent;
        }, 200, $headers);

    }

    //解密密钥
    private static function decodeToken($token, $action)
    {
        try {
            $decodeToken = JWT::decode($token, new Key(env('JWT_KEY'), 'HS256'));
            if (!isset($decodeToken->action)) {
                throw new DefaultException('非法操作');
            }
            if ($action != $decodeToken->action) {
                throw new DefaultException('非法操作');
            }
            if (Storage::disk('public')->exists($decodeToken->file)) {
                $fileUrl = viewFileUrl($decodeToken->file);
            } else {
                $fileUrl = OssService::link($decodeToken->file);
            }
            $fileContent = file_get_contents($fileUrl);
            if (!$fileContent) {
                throw new DefaultException('非法操作');
            }
            return $decodeToken;
        } catch (\Exception $e) {
            throw new DefaultException('非法操作');
        }
    }
}