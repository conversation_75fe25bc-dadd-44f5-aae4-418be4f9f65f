<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentModel extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'cna_payment';
    protected $guarded = [];
    protected $primaryKey = 'id';

    protected $appends = [
        'status',
        'profileName',
        'profilePartnerCode',
        'profileNRIC',
        'groupId',
        'teamRankName',
        'sonName',
        'divideTypeName'
    ];


    /**
     * 项目明细
     */
    public function projectContent()
    {
        return $this->belongsTo(ProjectServiceModel::class,'detailId');
    }

    /**
     * 合伙人
     */
    public function profile()
    {
        return $this->belongsTo(ProfileInfoModel::class,'profileID');
    }


    /**
     * 状态
     * @return void
     */
    public function getStatusAttribute()
    {
        $createtime = $this->attributes['createtime']??'';
        $paytime = $this->attributes['paytime']??'';
        if ($createtime && $paytime) {
            return 1;
        }
        return 0;
    }

    /**
     * 合伙人名称
     * @return mixed|null
     */
    public function getProfileNameAttribute()
    {
        return $this->profile()->value('profileName');
    }

    /**
     * 合伙人编码
     * @return mixed|null
     */
    public function getProfilePartnerCodeAttribute()
    {
        return $this->profile()->value('profilePartnerCode');
    }

    /**
     * 身份证号
     * @return mixed|null
     */
    public function getProfileNRICAttribute()
    {
        return $this->profile()->value('profileNRIC');
    }

    /**
     * 合伙人角色
     * @return mixed|null
     */
    public function getGroupIdAttribute()
    {

        return ProfileInfoModel::getGroupById($this->attributes['profileID']);

    }

    /**
     * 三三制等级
     * @return mixed|null
     */
    public function getTeamRankNameAttribute()
    {
        $team_rank = $this->profile()->value('team_rank');
        return $team_rank > 0 ? TeamRank::where('id', $team_rank)->value('job'):'';
    }

    /**
     * 旗下合伙人名称
     * @return mixed|null
     */
    public function getSonNameAttribute()
    {
        $son_id = $this->attributes['son_id']??'';
        return $son_id > 0 ? ProfileInfoModel::where('profileID', $son_id)->value('profileName'): '';
    }

    /**
     * 分类类型
     * @return mixed|null
     */
    public function getDivideTypeNameAttribute()
    {
        $this->attributes['divide_type'] = $this->attributes['divide_type']??'';
        if ($this->attributes['divide_type'] ==  1) {
            return '收益分成';
        }  else if ($this->attributes['divide_type'] ==  2) {
            return '管理津贴';
        } else if ($this->attributes['divide_type'] ==  3) {
            return '运营津贴';
        }
    }

    /**
     * 累计已付
     * @param $end_date
     * @param $profile_id
     * @return void
     */
    public static function getPutTotal( $profile_id)
    {
        $put_total = self::where('profileID', $profile_id)->where('type',1)->sum('put_amount');
        return $put_total;
    }

    /**
     * 期初余额
     * @return void
     */
    public static function startBalance($start_date, $profile_id)
    {
        // 应付
        $totalMustPay = PaymentModel::query()
            ->where('type', 1)
            ->select('profileID',$profile_id)
            ->sum('divide_profit');

        // 本期已付
        $totalPaid = PaymentModel::query()
            ->where('type', 1)
            ->select('profileID', $profile_id)
            ->sum('put_amount');

        return bcsub($totalMustPay, $totalPaid, 2);
    }



    /**
     * 生成单号(SKD+主体公司字母代码+数字||FKD+主体公司字母代码+数字)
     * @param $code 公司主体编号
     * @param $type 应收或应付 (1应收; 2应付)
     * @return string
     */
    public static function createPaymentNumber($code, $type)
    {
        $mainId = PaymentMainModel::query()->where('code', $code)->value('id');

        // 当前最大编码数
        $curMaxCode = self::query()
            ->where('type', $type)
            ->where('main', $mainId)
            ->orderBy('paymentNumber', 'desc')
            ->limit(1)
            ->value('paymentNumber');
        if ($curMaxCode) {
            $number = preg_replace("/[^0-9]/", "", $curMaxCode);
            $number += 1;

        } else {
            $number = 1000000000;
        }

        if ($type ==  1) { // 应付
            $pre = 'FKD';
        } else if ($type == 2) { // 应收
            $pre = 'SKD';
        }

        $paymentNumber = $pre . $code. $number;

        return $paymentNumber;
    }
}
