<?php

namespace App\Http\Controllers\Api\v1\Payment;

use App\Http\Controllers\Controller;
use App\Models\DocumentFoldersModel;
use App\Models\PaymentModel;
use App\Models\ProjectCategoryModel;
use App\Models\ProjectServiceModel;
use App\Services\OssService;
use Illuminate\Http\Request;
use App\Models\DocumentInfoModel;
use Illuminate\Support\Facades\Validator;

class PaymentController extends Controller
{

    /**
     * 账单收入
     * @return void
     */
    public function in(Request $request)
    {
        $user = $request->attributes->get('user');
        $body = $request->all();
        $page_size = $body['page_size'] ?? 10;
        $page = $body['page'] ?? 1;

        $data = PaymentModel::query()->with('projectContent')->where('type', 1)
                        ->where('profileID', $user['profileID'])
                        ->orderBy('createtime', 'desc')
                        ->paginate($page_size);

        $items = $data->items();

        // 合伙人加盟申请项目ID
        $projectId = ProjectCategoryModel::query()->where('type', 1)->value('projectCategoriesID');
        // 合伙人加盟注册(除了加盟申请费)
        $noPayProjectDetail = ProjectServiceModel::query()
            ->where('projectId', $projectId)
            ->where('remark', '<>','register.partner.fees')->pluck('id')->toArray();

        $items = collect($items)->map(function ($item) use($projectId, $noPayProjectDetail) {
            $item->titleZH = $item->projectContent->title_zh;
            $item->titleEN = $item->projectContent->title_en;
            $item->titleZT = $item->projectContent->title_zt;
            $item->titleMS = $item->projectContent->title_ms;
            $item->currency = $item->projectContent->currency;

            if($item->status == 1 && $item->projectId == $projectId
                && in_array($item->detailId, $noPayProjectDetail)) { // 已付款的合伙人加盟申请项目
                $item->remark = __('payment remark exempted');// 已豁免
            } else if ($item->status == 0 && $item->projectId == $projectId
                && in_array($item->detailId, $noPayProjectDetail)) { // 未付款的合伙人加盟申请项目
                $item->remark = __('payment remark exempt');;// 可豁免
            }
            $item->fee = number_format($item->fee,2);

            // 发票地址
            $item->invoice_file = OssService::link($item->invoice_file);
            unset( $item->projectContent);
            return $item;
        });

        $currentPage = $data->currentPage();
        $perPage = $data->perPage();
        $totalRecord = $data->total();
        $totalPage = $data->lastPage();
        $paginate = [
            'currentPage' => $currentPage,
            'perPage' => $perPage,
            'totalRecord' => $totalRecord,
            'totalPage' => $totalPage,
        ];

        $compact = compact('items', 'paginate');

        return responseSuccess($compact);

    }

    /**
     * 账单支出
     * @return void
     */
    public function out(Request $request)
    {
        $user = $request->attributes->get('user');
        $body = $request->all();
        $page_size = $body['page_size'] ?? 10;
        $page = $body['page'] ?? 1;

        $data = PaymentModel::query()->with('projectContent')->where('type', 2)
            ->where('profileID', $user['profileID'])
            ->orderBy('createtime', 'desc')
            ->paginate($page_size);

        $items = $data->items();

        // 合伙人加盟申请项目ID
        $projectId = ProjectCategoryModel::query()->where('type', 1)->value('projectCategoriesID');
        // 合伙人加盟注册(除了加盟申请费)
        $noPayProjectDetail = ProjectServiceModel::query()
            ->where('projectId', $projectId)
            ->where('remark', '<>','register.partner.fees')->pluck('id')->toArray();

        $items = collect($items)->map(function ($item)  use($projectId, $noPayProjectDetail){
            $item->titleZH = $item->projectContent->title_zh;
            $item->titleEN = $item->projectContent->title_en;
            $item->titleZT = $item->projectContent->title_zt;
            $item->titleMS = $item->projectContent->title_ms;
            $item->currency = $item->projectContent->currency;

            if($item->status == 1 && $item->projectId == $projectId
                && in_array($item->detailId, $noPayProjectDetail)) { // 已付款的合伙人加盟申请项目
                $item->remark = __('payment remark exempted');// 已豁免
            } else if ($item->status == 0 && $item->projectId == $projectId
                && in_array($item->detailId, $noPayProjectDetail)) { // 未付款的合伙人加盟申请项目
                $item->remark = __('payment remark exempt');;// 可豁免
            }
            $item->fee = number_format($item->fee,2);
            // 发票地址
            $item->invoice_file = OssService::link($item->invoice_file);
            
            unset( $item->projectContent);
            return $item;
        });

        $currentPage = $data->currentPage();
        $perPage = $data->perPage();
        $totalRecord = $data->total();
        $totalPage = $data->lastPage();
        $paginate = [
            'currentPage' => $currentPage,
            'perPage' => $perPage,
            'totalRecord' => $totalRecord,
            'totalPage' => $totalPage
        ];

        $compact = compact('items', 'paginate');

        return responseSuccess($compact);

    }

    /**
     * 合伙人分成明细
     * @return void
     */
    public function income(Request $request)
    {
        $user = $request->attributes->get('user');
        $body = $request->all();
        $page_size = $body['page_size'] ?? 10;
        $page = $body['page'] ?? 1;

        $data = PaymentModel::query()->select('profileID','paymentNumber','project_name', 'detail_name', 'createtime', 'divide_type',
            'divide_percent', 'divide_amount', 'income_type', 'son_id','proxy_fax', 'divide_profit', 'check', 'check_time','pay_type')->where('type', 1)
            ->where('profileID', $user['profileID'])
            ->orderBy('createtime', 'desc')
            ->paginate($page_size);

        $items = $data->items();


        $paginate = [
            'currentPage' => $data->currentPage(),
            'perPage' => $data->perPage(),
            'totalRecord' =>  $data->total(),
            'totalPage' => $data->lastPage()
        ];

        $compact = compact('items', 'paginate');

        return responseSuccess($compact);
    }

    // 上传发票
    public function uploadInvoice(Request $request)
    {
        $user = $request->attributes->get('user');
        $body = $request->all();
        $rules = [
            'id'   => ['required'],
            'file' => ['required','file', 'mimes:pdf,jpg,jpeg,png,webp', 'max:'.(env('ALLOW_FILE_SIZE')*1024)],
        ];
        $messages = [
            'file.required'   => __('please upload file'),
            'file.mimes' => __('incorrect format img'),
            'file.max' => __('exceed size img', ['limit' => env('ALLOW_FILE_SIZE').'M']),
        ];
        $validator = Validator::make($body, $rules, $messages);
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            return responseFail($errors);
        }

        $file = $request->file('file');
        if (empty($file)) {
            return responseFail();
        }

        $resource = $file->store('invoice', 'public');
        if ($resource) {
            $invoice_file = $resource;
        } else {
            return responseFail();
        }

        $result = PaymentModel::where('id', $body['id'])->where('profileID', $user['profileID'])
            ->where('type', 1)
            ->where('check', '<>', 2)->update([
                    'check' => 1,
                    'invoice_file' => $invoice_file
            ]);
        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail();
        }
    }

}
