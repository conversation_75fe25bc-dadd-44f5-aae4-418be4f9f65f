<?php

namespace App\Models;

use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class PartnerInterviewQuestionsModel extends Model
{
    use HasFactory;

    protected $table = 'cna_interviews_questions';

    protected $fillable = [
        'interview_id',
        'module',
        'question',
        'answer',
        'score',
    ];



    public function interview()
    {
        return $this->belongsTo(PartnerInterviewModel::class);
    }


    public static function saveAnswers($interview_id)
    {
        $key = "interview_{$interview_id}_answers";

        try {
            // 读取 Redis 中所有回答
            $answers = Redis::lrange($key, 0, -1);

            if (empty($answers)) {
                return false;
            }

            $averageScore = null;
            DB::transaction(function () use ($answers, $interview_id, $key) {
                $scoreList = [];

                foreach ($answers as $item) {
                    $answerData = json_decode($item, true);

                    if (!$answerData || !isset($answerData['question_id'])) {
                        continue;
                    }

                    PartnerInterviewQuestionsModel::where('id', $answerData['question_id'])
                        ->update([
                            'answer' => $answerData['answer_text'] ?? null,
                            'answer_url' => $answerData['answer_url'] ?? null,
                            'score' => $answerData['score'] ?? null,
                            'updated_at' => now(),
                        ]);

                    if (isset($answerData['score']) && is_numeric($answerData['score'])) {
                        $scoreList[] = $answerData['score'];
                    }
                }

                // 计算平均分
                $averageScore = ($validScores = array_filter($scoreList, 'is_numeric'))
                    ? round(array_sum($validScores) / count($validScores), 2)
                    : 0;

//                if ($averageScore >= 80) {
//                    $status = 1;
//                } else  {
//                    $status = -1;
//                }
                // 更新面试状态，写入平均分
                PartnerInterviewModel::where('id', $interview_id)->update([
                    'status' => 2,      // 待审核
                    'average_score' => $averageScore,
                    'updated_at' => now(),
                ]);

                // 删除 Redis 临时缓存
                Redis::del($key);
            });

            return $averageScore;
        } catch (Exception $e) {
            Log::error("Failed to save answers for interview $interview_id: " . $e->getMessage());
            return false;
        }
    }

}
