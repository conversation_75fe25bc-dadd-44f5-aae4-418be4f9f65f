<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\AiManFile;
use App\Models\CompanyGsp;
use App\Models\CompanyGspReport;
use App\Models\CountryModel;
use App\Models\GspOption;
use App\Models\InvoiceModel;
use App\Models\VisitorApply;
use App\Models\VisitorAttach;
use App\Models\VisitorLogin;
use App\Services\DocService;
use App\Services\OssService;
use App\Services\ZipService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class InvoiceController extends Controller
{

    /**
     * 开票信息管理
     * @param Request $request
     * @return null
     */
    public function index(Request $request)
    {
        $keyword = $request->get('keyword', '');
        $pageSize = $request->get('page_size', 10);

        $list = InvoiceModel::with(['profileInfo' => function($q) {
                    $q->select('profileID', 'profileName'); // 确保包含外键字段

                }])->whereHas('profileInfo', function ($q) use ($keyword) {
                    $q->where('profileName', 'like', '%' . $keyword . '%');

                })->when($keyword != '', function ($q) use ($keyword) {
                    $q->orWhere('header_name', 'like', '%' . $keyword . '%')
                        ->orWhere('phone', 'like', '%' . $keyword . '%')
                        ->orWhere('email', 'like', '%' . $keyword . '%')
                        ->orWhere('tax_number', 'like', '%' . $keyword . '%');

               })->orderBy('id', 'desc')
                ->paginate($pageSize);

        $items = $list->items();

        $items = collect($items)->map(function ($item) {
            $item->type_name = InvoiceModel::$typeList[$item['type']-1];
            $item->profileName  = $item->profileInfo->profileName;
            unset($item->profileInfo);
            return $item;
        });


        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 到访申请详情
     * @param Request $request
     * @return void
     */
    public function detail(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return responseFail(__('missing parameter', ['param' => 'id']));
        }

        // 查看
        $data = VisitorApply::where('id', $id)->first();
        if ($data && $data['avatar']) {
            $data['avatar'] = OssService::link($data['avatar']);
        }

        // 企业附件
        $arrAttach = [];
        $attachs = VisitorAttach::query()->where('obj_id', $id)->where('type', 1)->get()->toArray();
        if ($attachs) {
            foreach ($attachs as $attach) {
                $arrAttach[] = OssService::link($attach['file_path']);;
            }
            $data['attach'] = $arrAttach;
        }

        return responseSuccess($data);
    }


    // 申批
    public function check(Request $request)
    {
        $id = $request->input('id');
        $status = $request->input('status', 0);
        $reject_reason = $request->input('reject_reason', '');

        if (empty($id)) {
            return responseFail(__('missing parameter', ['param' => 'id']));
        }

        if (empty($status)) {
            return responseFail(__('missing parameter', ['param' => 'status']));
        }

        if (!in_array($status, [1,2])) {
            return responseFail(__('param error', ['param' => 'status']));
        }

        if ($status == 2 && empty($reject_reason)) {
            return responseFail(__('missing parameter', ['param' => 'reject_reason']));
        }


        // 查看详情
        $info = VisitorApply::where('id', $id)->first();
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        // 是否审批过了
        if (in_array($info['status'], [1,2])) {
            return responseFail(__('param error', ['param' => 'status']));
        }

        VisitorApply::where('id', $id)->update([
            'status' => $status,
            'reject_reason' => $reject_reason,
        ]);

        return responseSuccess();

    }

}
