<?php

namespace App\Services;

use App\Exceptions\AuthException;
use App\Models\ExamQuestionsModel;

class ExamQuestionsServices
{

    public function __construct(ExamQuestionsModel $examQuestionsModel)
    {
        $this->model = $examQuestionsModel;
    }

    public function getList($param){
        $param['page_size'] = $param['page_size'] ?? 10;
        $list = $this->model->select('id','exam_id','type','question','option')
            ->with(['exam:id,name'])
            ->when(!empty($param['exam_id']) && $param['exam_id'] != 0,function($query) use ($param){
                $query->where('exam_id', $param['exam_id']);
            })
            ->when(!empty($param['type']) && $param['type'] != 0,function($query) use ($param){
                $query->where('type', $param['type']);
            });
        if( !empty($param['get_type']) && $param['get_type'] == 'random' )  {
            $items = $list->inRandomOrder()
                ->limit($param['page_size'])
                ->get()->map(function($item){
                    $this->map($item);
                    return $item;
                });
            $paginate = [
                'page_size' => $param['page_size'],
                'total' => $param['page_size'],
                'total_page' => 1,
            ];
        }else{
            $list = $list->paginate($param['page_size'])
                ->through(function($item) {
                    $this->map($item);
                    return $item;
                });
            $items = $list->items();
            $paginate = [
                'page_size' => $param['page_size'],
                'total' => $list->total(),
                'total_page' => $list->lastPage(),
            ];
        }
        return compact('items', 'paginate');
    }

    public function map(&$item){
        $item = $item->toArray();
        $item['type_remark'] = $this->model::TYPE_REMARK[$item['type']];
        $item['option'] = json_decode($item['option'],true);
        //管理后台部分 不需要设定ABCD 只需要提交题库管理
//        shuffle($item['option']);
        // 动态生成选项标识
//        foreach ($item['option'] as $index => &$option) {
//            $option['option_key'] = chr(65 + $index);//这里确认在哪里确定ABCD再操作
//        }
    }

    /**
     * 保存问题（新增或修改）
     * @param array $data 问题数据
     * @param int|null $id 问题ID（修改时必填）
     * @return ExamQuestionsModel
     * @throws \Exception
     */
    public function save(array $data, int $id = null)
    {
        // 检查题目是否重复
        $query = $this->model->where('question', $data['question']);
        
        if ($id) {
            // 修改时，排除自己的记录
            $query->where('id', '!=', $id);
        }
        
        if ($query->exists()) {
            throw new AuthException('该题目已存在');
        }

        if( empty($data['option']) ) {
            throw new AuthException('题目缺少答案选项');
        }

        foreach( $data['option'] as $index => &$option ) {
            // $option['option_key'] = chr(65 + $index);//这里确认在哪里确定ABCD再操作
            $option['is_checked'] = 0;
        }
        $data['option'] = json_encode($data['option'],JSON_UNESCAPED_UNICODE);
        if ($id) {
            // 修改
            $question = $this->model->findOrFail($id);
            $question->update($data);
            return $question;
        } else {
            // 新增
            return $this->model->create($data);
        }
    }

    /**
     * 逻辑删除问题
     * @param int $id
     * @return void
     */
    public function destroy(int $id)
    {
        $question = ExamQuestionsModel::findOrFail($id);
        $question->destroy($id);
    }

}
