<?php

namespace App\Http\Controllers\Api\v1\VisitorLogin;

use App\Http\Controllers\Controller;
use App\Models\AiManFile;
use App\Models\AiManVideo;
use App\Models\VisitorApply;
use App\Models\VisitorAttach;
use App\Models\VisitorLogin;
use App\Models\VisitorPeople;
use App\Models\VisitorReport;
use App\Models\VisitorSignin;
use App\Rules\Mobile\CheckMobileRule;
use App\Services\OssService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class VisitorLoginController extends Controller
{
    const FILE_PATH = 'files/visitor_login';
    const ACTION = 'visitor_login'; //场景

    // 提交来访信息
    public function create(Request $request)
    {
        $messages = [
            'file.mimes' => __('incorrect format img'),
            'file.max' => __('exceed size img', ['limit' => env('ALLOW_FILE_SIZE').'M']),
        ];

        $validator = Validator::make($request->all(), [
            'name'              => 'required',
            'visitor_date'      => 'required',
            'visitor_time'      => ['required'],
            'phone'             => ['required'],
            'company'           => 'required',
           /* 'avatar'            => ['file', 'mimes:pdf,jpg,jpeg,png,webp', 'max:'.(env('ALLOW_FILE_SIZE')*1024)],*/
            'invite_people'     => 'required',
            'visitor'           => ['required'],
            'matters'           => ['required'],
            'place'             => ['required'],
            'expect'            => ['required'],
            'accompany_number'  => 'required',
            'visitor_reason'    => 'required',
            'visitor_desired'   =>  ['required'],
        ], $messages);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $body = $request->all();
        $body = array_map(function ($value) {
            return empty($value) ? '' : $value;
        }, $body);

        $file = $request->file('avatar');
        if ($file) {
            $resource = OssService::upload($file);
            $resource && $body['avatar'] = $resource;
        }

        $result = VisitorLogin::create($body);
        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail();
        }

    }

    // 到访申请表格
    public function apply(Request $request)
    {
        $id = $request->input('id');

        $messages = [
            'file.mimes' => __('incorrect format img'),
            'file.max' => __('exceed size img', ['limit' => env('ALLOW_FILE_SIZE').'M']),
        ];

        $validator = Validator::make($request->all(), [
            'invite_people'     => 'required',
            'invite_phone'      => 'required',
            'name'              => 'required',
            'phone'             => ['required'],
            'visitor_date'      => 'required',
            'visitor_time'      => 'required',
            'leave_time'        => 'required',
            'avatar'            => ['file', 'mimes:pdf,jpg,jpeg,png,webp', 'max:'.(env('ALLOW_FILE_SIZE')*1024)],
            'numbers'           => 'required',
           /* 'visitor_name_list' => 'required',*/
            'company'           => 'required',
          //  'company_information'=> 'required',
            'is_read_cna'       => 'required|in:1,2',
            'visitor'           => 'required',
            'matters'           => 'required',
            'place'             => 'required',
            'expect'            => 'required',
            'is_book_meeting'   => 'required|in:1,2',
            'is_food'           => 'required|in:1,2',
            'location'          => 'required'
        ], $messages);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        // 过滤空值
        $body = $request->all();
        $body = array_map(function ($value) {
            return empty($value)  ? '' : $value;

        }, $body);

        // 预订详细请求
     /*   if ($body['is_book_meeting'] == 1 &&  empty($body['book_detail'])) {
            return responseFail(__('missing parameter', ['param' => 'book_detail']));
        }*/

        // 到访人员图片
        $avatar = $request->file('avatar');
        if ($avatar) {
            $resource = OssService::upload($avatar);
            //$resource = $avatar->store('visitor', 'public');
            $resource && $body['avatar'] = $resource;
        }

        // 企业背书
        $companyAttach = [];
        $files = $request->file('files');
        if ($files) {
            foreach ($files as $file) {
                $resource = OssService::upload($file);
                //$resource = $file->store('visitor', 'public');
                $resource && $companyAttach[] = $resource;
            }
        }
        unset($body['files']);

        try {
            // 开启事务
            DB::beginTransaction();

            // step1 写入到访申请登记表
            if ($id) { // 更新
                $result = VisitorApply::where('id', $id)->update($body);
            } else { // 新增
                unset($body['id']);
                $body['create_date'] = date('Y-m-d');
                $body['create_time'] = date('H:i:s');
                $result = VisitorApply::create($body);
                $id = $result->id;
            }


            // step2 附件表
            $attachData = [];
            if ($companyAttach) {
                foreach ($companyAttach as $item) {
                    $attachData[] = [
                        'obj_id' => $id,
                        'type'       => 1,
                        'file_path'  => $item,
                    ];
                }
                VisitorAttach::insert($attachData);
            }

            // 提交
            DB::commit();

            return responseSuccess();

        } catch (\Exception $e) {
            DB::rollBack();

            return responseFail($e->getMessage());
        }


    }

    // 到访申请表格(保存并转发)
    public function applyTurn(Request $request)
    {
        // 过滤空值
        $body = $request->all();
        $body = array_filter($body);

        // 到访人员图片
        $avatar = $request->file('avatar');
        if ($avatar) {
            $resource = OssService::upload($avatar);
           // $resource = $avatar->store('visitor', 'public');
            $resource && $body['avatar'] = $resource;
        }

        // 企业背书
        $companyAttach = [];
        $files = $request->file('files');
        if ($files) {
            foreach ($files as $file) {
                $resource = OssService::upload($file);
                //$resource = $file->store('visitor', 'public');
                $resource && $companyAttach[] = $resource;
            }
        }
        unset($body['files']);

        try {
            // 开启事务
            DB::beginTransaction();

            // step1 写入到访申请登记表
            $body['create_date'] = date('Y-m-d');
            $body['create_time'] = date('H:i:s');
            $result = VisitorApply::create($body);
            $id = $result->id;

            // step2 附件表
            $attachData = [];
            if ($companyAttach) {
                foreach ($companyAttach as $item) {
                    $attachData[] = [
                        'obj_id' => $id,
                        'type'       => 1,
                        'file_path'  => $item,
                    ];
                }
                VisitorAttach::insert($attachData);
            }

            // 提交
            DB::commit();

            // 生成token
            $encryptedNumber = Crypt::encrypt($id);

            return responseSuccess(['token'=>$encryptedNumber]);

        } catch (\Exception $e) {
            DB::rollBack();

            return responseFail($e->getMessage());
        }


    }

    // 获取到访申请信息
    public function applyEdit(Request $request)
    {
        $token = $request->route('token');
        // 使用 Crypt 解密
        $id = Crypt::decrypt($token);
        if (empty($id)) {
            return responseFail('信息不存在');
        }

        // 查看活动信息
        $info = VisitorApply::where('id', $id)->first();
        if ($info['avatar']) {
            $info['avatar'] = OssService::link($info['avatar']);
        }
        // 附件
        $files = VisitorAttach::where('obj_id', $id)->where('type', 1)->pluck('file_path')->toArray();
        if ($files) {
            foreach ($files as &$item) {
                $item = OssService::link($item);
            }
            $info['files'] = $files;
        }
        if (empty($info)) {
            return responseFail('信息不存在');
        }

        return responseSuccess($info);
    }

    // 贵宾来访登记
    public function signin(Request $request)
    {
        $id = $request->input('id');
        $messages = [
            'file.mimes' => __('incorrect format img'),
            'file.max' => __('exceed size img', ['limit' => env('ALLOW_FILE_SIZE').'M']),
        ];

        $validator = Validator::make($request->all(), [
            'invite_people'     => 'required',
            'invite_phone'      => 'required',
            'name'              => 'required',
            'phone'             => ['required'],
            'visitor_date'      => 'required',
            'visitor_time'      => 'required',
            'leave_time'        => 'required',
            'avatar'            => ['file', 'mimes:pdf,jpg,jpeg,png,webp', 'max:'.(env('ALLOW_FILE_SIZE')*1024)],
            'numbers'           => 'required',
            /*'visitor_name_list' => 'required',*/
           // 'company_information'=> 'required',
            'visitor'           => 'required',
            'is_read_cna'       => 'required|in:1,2',
            'matters'           => 'required',
            'place'             => 'required',
            'expect'            => 'required',
/*            'is_parking'        => 'required|in:1,2',*/
            'is_book_meeting'   => 'required|in:1,2',
            'location'          => 'required'
        ], $messages);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        // 过滤空值
        $body = $request->all();



        $body = array_map(function ($value) {
            return empty($value) ? '' : $value;
        }, $body);

        // 贵宾图片
        $avatar = $request->file('avatar');
        if ($avatar) {
            $resource = OssService::upload($avatar);
            //$resource = $avatar->store('visitor', 'public');
            $resource && $body['avatar'] = $resource;
        }

        // 企业背书
        $companyAttach = [];
        $files = $request->file('files');
        if ($files) {
            foreach ($files as $file) {
                $resource = OssService::upload($file);
               // $resource = $file->store('visitor', 'public');
                $resource && $companyAttach[] = $resource;
            }
        }
        unset($body['files']);

        try {
            // 开启事务
            DB::beginTransaction();

            // step1 写入贵宾登记
            if ($id) {
                $result = VisitorSignin::where('id', $id)->update($body);
            } else {
                unset($body['id']);
                $result = VisitorSignin::create($body);
                $id = $result->id;
            }


            // step2 附件表
            $attachData = [];
            if ($companyAttach) {
                foreach ($companyAttach as $item) {
                    $attachData[] = [
                        'obj_id' => $id,
                        'type'       => 2,
                        'file_path'  => $item,
                    ];
                }
                VisitorAttach::insert($attachData);
            }

            // 提交
            DB::commit();

            return responseSuccess();

        } catch (\Exception $e) {
            DB::rollBack();

            return responseFail($e->getMessage());
        }


    }

    // 贵宾来访登记(保存并转发)
    public function signinTurn(Request $request)
    {

        // 过滤空值
        $body = $request->all();
        $body = array_filter($body);

        // 贵宾图片
        $avatar = $request->file('avatar');
        if ($avatar) {
            $resource = OssService::upload($avatar);
           // $resource = $avatar->store('visitor', 'public');
            $resource && $body['avatar'] = $resource;
        }

        // 企业背书
        $companyAttach = [];
        $files = $request->file('files');
        if ($files) {
            foreach ($files as $file) {
                $resource = OssService::upload($file);
               // $resource = $file->store('visitor', 'public');
                $resource && $companyAttach[] = $resource;
            }
        }
        unset($body['files']);

        try {
            // 开启事务
            DB::beginTransaction();

            // step1 写入贵宾登记
            $result = VisitorSignin::create($body);
            $id = $result->id;

            // step2 附件表
            $attachData = [];
            if ($companyAttach) {
                foreach ($companyAttach as $item) {
                    $attachData[] = [
                        'obj_id' => $id,
                        'type'       => 2,
                        'file_path'  => $item,
                    ];
                }
                VisitorAttach::insert($attachData);
            }

            // 提交
            DB::commit();

            // 生成token
            $encryptedNumber = Crypt::encrypt($id);

            return responseSuccess(['token'=>$encryptedNumber]);

        } catch (\Exception $e) {
            DB::rollBack();

            return responseFail($e->getMessage());
        }


    }

    public function signinEdit(Request $request)
    {
        $token = $request->route('token');
        // 使用 Crypt 解密
        $id = Crypt::decrypt($token);
        if (empty($id)) {
            return responseFail('信息不存在');
        }

        // 查看信息
        $info = VisitorSignin::where('id', $id)->first();
        if ($info['avatar']) {
            $info['avatar'] = OssService::link($info['avatar']);
        }
        // 附件
        $files = VisitorAttach::where('obj_id', $id)->where('type', 2)->pluck('file_path')->toArray();
        if ($files) {
            foreach ($files as &$item) {
                $item = OssService::link($item);
            }
            $info['files'] = $files;
        }
        if (empty($info)) {
            return responseFail('信息不存在');
        }

        return responseSuccess($info);
    }

    // 会谈结果报告
    public function report(Request $request)
    {
        $messages = [
            'file.mimes' => __('incorrect format img'),
            'file.max' => __('exceed size img', ['limit' => env('ALLOW_FILE_SIZE').'M']),
        ];

        $validator = Validator::make($request->all(), [
            'report_author'     => 'required',
            'visitor_date'      => 'required',
            'visitor_time'      => 'required',
            'visitor_purpose'   => 'required',
            'expect'            => 'required',
            'content'           => 'required',
            'developments'      => 'required',
            'result'            => 'required',
            'follow_content'    => 'required',
        ], $messages);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        // 过滤空值
        $body = $request->all();
        $body = array_map(function ($value) {
            return empty($value) ? '' : $value;
        }, $body);


        // 企业背书
        $companyAttach = [];
        $files = $request->file('files');
        if ($files) {
            foreach ($files as $file) {
                $resource = OssService::upload($file);
                //$resource = $file->store('visitor', 'public');
                $resource && $companyAttach[] = $resource;
            }
        }
        unset($body['files']);

        try {
            // 开启事务
            DB::beginTransaction();

            // step1 写入贵宾登记
            $result = VisitorReport::create($body);
            $id = $result->id;

            // step2 附件表
            $attachData = [];
            if ($companyAttach) {
                foreach ($companyAttach as $item) {
                    $attachData[] = [
                        'obj_id' => $id,
                        'type'       => 3,
                        'file_path'  => $item,
                    ];
                }
                VisitorAttach::insert($attachData);
            }

            // 提交
            DB::commit();

            return responseSuccess();

        } catch (\Exception $e) {
            DB::rollBack();

            return responseFail($e->getMessage());
        }
    }

    public function invitePeople()
    {
       $data=  VisitorPeople::get();
       return responseSuccess($data);
    }

}
