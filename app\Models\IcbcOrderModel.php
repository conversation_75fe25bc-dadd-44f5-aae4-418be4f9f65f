<?php

namespace App\Models;

use App\Events\UserPaid;
use App\Services\ICBC\ICBCService;
use App\Services\Wechat\WechatService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IcbcOrderModel extends Model
{
    use HasFactory;

    protected $table = 'cna_icbc_orders';
    protected $guarded = [];

    const TYPE_REGISTER = 1; // 加盟商入驻
    const TYPE_GSP_REGISTER = 2; // 绿智入驻

    //检查注册的支付订单
    public static function checkRegisterOrder($profileId)
    {
        $orders = self::select('id', 'out_trade_no')
            ->where('pay_status', 0)
            ->where('user_id', $profileId)
            ->orderBy('created_at', 'desc')
            ->get();
        if (empty($orders)) {
            return;
        }
        foreach ($orders as $order) {
            $res = ICBCService::payStatusQRcode($order['out_trade_no']);

            if (!$res || $res['pay_status'] != 1) {
                continue;
            }

            $update = [
                'pay_msg_id'        => $res['msg_id'],
                'order_id'          => $res['order_id'],
                'pay_time'          => $res['pay_time'],
                'pay_status'        => $res['pay_status'],
                'cust_id'           => $res['cust_id'],
                'card_no'           => $res['card_no'],
                'bank_name'         => $res['bank_name']??'',
                'channel'           => $res['channel'],
                'attach'            => $res['attach'],
                'tp_cust_id'        => $res['tp_cust_id']?? '',
                'trx_ser_no'        => $res['trx_ser_no']?? '',
                'tp_order_id'       => $res['tp_order_id'] ?? '',
                'sub_open_id'       => $res['sub_open_id'] ?? '',
                'bank_type'         => $res['bank_type'] ?? '',
                'tp_user_id'        => $res['tp_user_id'] ?? '',
            ];
            self::where('out_trade_no', $order['out_trade_no'])->update($update);
            event(new UserPaid($profileId, $res['total_amt']));
            break;
        }
    }

    //检查注册的支付订单并更新(公共)
    public static function checkPayStatus($profileId)
    {
        $orders = self::select('id', 'out_trade_no')
            ->where('pay_status', 0)
            ->where('user_id', $profileId)
            ->orderBy('created_at', 'desc')
            ->get();
        if (empty($orders)) {
            return false;
        }
        foreach ($orders as $order) {
            $res = ICBCService::payStatusQRcode($order['out_trade_no']);

            if ($res && $res['pay_status'] == 1) {
                $update = [
                    'pay_msg_id'        => $res['msg_id'],
                    'order_id'          => $res['order_id'],
                    'pay_time'          => $res['pay_time'],
                    'pay_status'        => $res['pay_status'],
                    'cust_id'           => $res['cust_id'],
                    'card_no'           => $res['card_no'],
                    'bank_name'         => isset($res['bank_name'])??$res['bank_name'],
                    'channel'           => $res['channel'],
                    'attach'            => $res['attach'],
                    'tp_cust_id'        => isset($res['tp_cust_id'])?? $res['tp_cust_id'],
                    'trx_ser_no'        => isset($res['trx_ser_no'])?? $res['trx_ser_no'],
                    'tp_order_id'       => isset($res['tp_order_id'])?? $res['tp_order_id'],
                    'sub_open_id'       => isset($res['sub_open_id'])?? $res['sub_open_id'],
                    'bank_type'         => isset($res['bank_type'])?? $res['bank_type'],
                    'tp_user_id'        => isset($res['tp_user_id'])?? $res['tp_user_id'],
                ];
                self::where('out_trade_no', $order['out_trade_no'])->update($update);
                return true;
            }
        }
    }

    /**
     * 查看付款项目金额
     * @param $type int 平台项目
     * @param $project string 支付的项目
     * @param $obj_id int 对应业务表的ID
     * @return mixed
     */
    public static function projectAmount($type, $project, $obj_id)
    {
        $total_amt  = self::where('type', $type)
            ->where('project', $project)
            ->where('obj_id', $obj_id)
            ->where('pay_status', 1)
            ->value('total_amt');

        $total_amt = bcdiv($total_amt, 100, 2);

        return $total_amt;
    }
}
