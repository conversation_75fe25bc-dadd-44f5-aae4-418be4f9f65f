<?php

namespace App\Http\Controllers\Api\v1\BusinessCase;

use App\Http\Controllers\Controller;
use App\Models\BusinessCase;
use Illuminate\Http\Request;

class BusinessCaseController extends Controller
{
    public function info(Request $request)
    {
        $user = $request->attributes->get('user');
        $data = BusinessCase::where('profileID', $user['profileID'])->first();

        return responseSuccess($data);
    }

    public function edit(Request $request)
    {
        $body = $request->validate([
            'name' => ['required'],
            'content' => ['required'],
            'result' => ['required'],
            'ai_experience' => ['required'],
        ]);
        $user = $request->attributes->get('user');
        BusinessCase::updateOrCreate(['profileID' => $user['profileID']], $body);

        return responseSuccess();
    }
}
