<?php

namespace App\Http\Controllers\Api\v1\Company;

use App\Http\Controllers\Controller;
use App\Models\GSP\GspInvestigationMain;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\CompanyGsp;
use App\Exceptions\DefaultException;
use App\Models\CompanyGspReport;
use App\Models\CountryModel;
use App\Models\GSP\Extra\GspInvestigationAdministrativePenaltyRecords;
use App\Models\GSP\Extra\GspInvestigationBalanceSheet;
use App\Models\GSP\Extra\GspInvestigationCashFlow;
use App\Models\GSP\Extra\GspInvestigationComprehensiveIncomes;
use App\Models\GSP\Extra\GspInvestigationCustomerSituation;
use App\Models\GSP\Extra\GspInvestigationDishonestyRecords;
use App\Models\GSP\Extra\GspInvestigationIntellectualProperties;
use App\Models\GSP\Extra\GspInvestigationLitigationArbitrationCases;
use App\Models\GSP\Extra\GspInvestigationManagementStatement;
use App\Models\GSP\Extra\GspInvestigationOfficeSpaces;
use App\Models\GSP\Extra\GspInvestigationProductApplicationCases;
use App\Models\GSP\Extra\GspInvestigationProductionEquipment;
use App\Models\GSP\Extra\GspInvestigationQualifications;
use App\Models\GSP\Extra\GspInvestigationRawMaterials;
use App\Models\GSP\Extra\GspInvestigationShareholders;
use App\Models\GSP\Extra\GspInvestigationSignificantContracts;
use App\Models\GSP\GspInvestigationConfig;
use App\Models\GSP\GspInvestigationUpload;
use App\Models\GSP\GspInvestigationVersions;
use App\Services\PdfTableService;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;

/**
 * 尽职调查主控制器
 */
class GspInvestigationController extends Controller
{
    //获取静态表的下拉类型
    public function selectOption()
    {
        return responseSuccess(GspInvestigationMain::selectOption());
    }

    public static function countriesData($firstId = false)
    {
        $data = CountryModel::select('countryID', 'countryEN', 'countryMS', 'countryZH', 'countryZT', 'countryCode', 'countryISOCode2');
        $data2 = false;
        if ($firstId) {
            $data2 = clone $data;
            $data2 = $data2->orderByRaw("CASE WHEN countryID = {$firstId} THEN 0 ELSE 1 END")->first();
        }
        $data = $data->get()->toArray();
        array_unshift($data, [
            "countryID" => 1000,
            "countryEN" => "Taiwan, China",
            "countryMS" => "Taiwan, China",
            "countryZH" => "中国台湾",
            "countryZT" => "中國臺灣",
            "countryCode" => 0,
            "countryISOCode2" => 0,
        ], [
            "countryID" => 1001,
            "countryEN" => "Hong Kong, China",
            "countryMS" => "Hong Kong, China",
            "countryZH" => "中国香港",
            "countryZT" => "中國香港",
            "countryCode" => 0,
            "countryISOCode2" => 0,
        ], [
            "countryID" => 1002,
            "countryEN" => "Macao, China",
            "countryMS" => "Makau, China",
            "countryZH" => "中国澳门",
            "countryZT" => "中國澳門",
            "countryCode" => 0,
            "countryISOCode2" => 0,
        ]);
        if ($data2) array_unshift($data, $data2);
        $data = array_column($data, NULL, 'countryID');
        return $data;
    }

    //国家
    public function countries(Request $request)
    {
        $firstId = $request->first_id ?? '';
        $data = self::countriesData($firstId);
        $data = array_values($data);
        return responseSuccess($data);
    }

    //创建主表
    protected function createMain($pid)
    {
        $model = GspInvestigationMain::where('pid', $pid)->value('id');
        if (!$model) {
            GspInvestigationMain::create([
                'pid' => $pid
            ]);
            $main = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->first();
        }
    }

    //获取主页配置
    public function index(Request $request)
    {
        $user       = $request->attributes->get('user');
        $gsp_id     = $request->attributes->get('gsp_id');
        $isPartner  = !empty($user);
        $record     = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid        = $record['id'];
        $id         = $request->id;
        $companyGspStatus = CompanyGsp::where('id', $pid)->value('status');
        $config     = GspInvestigationConfig::with(['upload' => function (Relation $query) use ($pid) {
            $query->where('gsp_id', '=', $pid);
        }])->selectRaw('id,category_zh_cn,description_zh_cn,allow_online_form,allow_upload_file,allow_template_download,sort,pid,level,db_name,type,template_file,allow_upload_file_config,child_upload,upload_name,upload_required,0 as last_upload_status')
            ->orderBy('level', 'asc')
            ->orderBy('sort', 'asc');
        $config2 = [];
        if ($gsp_id) {  //供应商
            $config = $config->where([['supplier', '=', 1]]);
        } else {  //合伙人
            $config = $config->where([['partner', '=', 1]]);
        }
        if ($id) {
            $config2        = clone $config;
            $config         = $config->where([['id', '=', $id]]);
            $childUpload    = $config->value('child_upload');
            if ($childUpload) {   //再读下一级列表
                $config2    = $config2->where([['pid', '=', $id]]);
                $config2    = $config2->get()->toArray();
                foreach($config2 as $config2Key => $config2Val){
                    $config2[$config2Key]['allow_upload_file_config'] = json_decode($config2Val['allow_upload_file_config'], true);
                }
            } else {
                $config2 = [];
            }
        }
        $config = $config->get()->toArray();
        //读取审核失败的
        $gspReportFile = CompanyGspReport::where([
            ['gsp_id', '=', $pid],
            ['status', '=', CompanyGspReport::STATUS_FILE_FAIL],
        ])->pluck('id', 'file_id')->toArray();
        //读取审核失败的上级,也设置为失败
        $parent         = GspInvestigationConfig::whereIn('id', array_keys($gspReportFile))->where('pid', '>', '0')->pluck('id', 'pid')->toArray();
        $gspReportFile  = $gspReportFile + $parent;
        //读取修改时间
        $version = GspInvestigationVersions::where([
            ['pid', '=', $pid],
        ])->get()->toArray();
        $version = array_column($version, null, 'cna_gsp_investigation_config_id');
        foreach ($config as $key => $value) {
            $value['last_modified_time']    = '';
            $value['audit_status']          = '';
            $value['allow_upload_file_config'] = json_decode($value['allow_upload_file_config'], true);
            if (!empty($version[$value['id']])) {
                $value['last_modified_time'] = $version[$value['id']]['last_modified_time'];
            }
            if (!empty($gspReportFile[$value['id']])) { //审核失败
                $value['audit_status'] = 'fail';
            }
            if (!$isPartner) {   //如果不是合伙人，就只能查看
                $value['allow_online_form'] = 0;
                $value['allow_upload_file'] = 0;
                $value['allow_template_download'] = 0;
            }
            $config[$key] = $value;
        }
        if (!empty($gspReportFile)) { //如果有审核失败,不显示成功的列表
            foreach ($config as $key => $value) {
                if (empty($value['audit_status']) && $value['level'] != 0 && !in_array($value['id'], [5, 46])) {
                    unset($config[$key]);
                }
            }
        }
        if ($id) {
            if (!empty($config[0])) $config[0]['child'] = $config2;
            $config = $this->indexStatus($config, $companyGspStatus);
        } else {
            $config = $this->buildTree($config);
            //如果有审核失败
            if (!empty($gspReportFile)) {
                //删除下级ID为空的
                foreach ($config as $key => $value) {
                    if (empty($value['child'])) {
                        unset($config[$key]);
                    }
                }
                $config = array_values($config);
            }
            foreach ($config as $ck => $cv) {
                //处理列表状态
                $config[$ck]['child'] = $this->indexStatus($cv['child'], $companyGspStatus);
            }
        }

        return responseSuccess($config);
    }

    protected function indexStatus($data, $companyGspStatus){
        foreach ($data as $key => $value) {
            $value['last_upload_status']    = 0;   // 未上传，已上传，待更新，已更新
            $lastUploadStatusTimeData       = NULL; // 0待审核 1已审核 2审核失败 3已废弃
            //处理列表状态
            if (!empty($value['upload'])) {
                foreach ($value['upload'] as $k => $v) {
                    if (in_array($companyGspStatus, [CompanyGsp::STATUS_COMPANY_REPORT_FAIL, CompanyGsp::STATUS_REPORT_FAIL])) {
                        if ($v['status'] == 0 || $lastUploadStatusTimeData === 0) {
                            $lastUploadStatusTimeData   = 0;
                        } else if ($v['status'] == 2 || $lastUploadStatusTimeData === 2) {
                            $lastUploadStatusTimeData   = 2;
                        } else {
                            $lastUploadStatusTimeData = $v['status'];
                        }
                    } else {
                        $lastUploadStatusTimeData = 0;
                    }
                }
            }

            if (!empty($value['child'])) {
                $value['last_upload_status']    = !empty($value['upload_required'])? 1: 0;   // 未上传，已上传，待更新，已更新
                $lastUploadStatusTimeData       = NULL; // 0待审核 1已审核 2审核失败 3已废弃
                foreach ($value['child'] as $k => $v) {
                    //处理列表状态
                    if (!empty($v['upload'])) {
                        foreach ($v['upload'] as $k2 => $v2) {
                            if (in_array($companyGspStatus, [CompanyGsp::STATUS_COMPANY_REPORT_FAIL, CompanyGsp::STATUS_REPORT_FAIL])) {
                                if ($v2['status'] == 0 || $lastUploadStatusTimeData === 0) {
                                    $lastUploadStatusTimeData   = 0;
                                } else if ($v2['status'] == 2 || $lastUploadStatusTimeData === 2) {
                                    $lastUploadStatusTimeData   = 2;
                                } else {
                                    $lastUploadStatusTimeData = $v2['status'];
                                }
                            } else {
                                $lastUploadStatusTimeData = 0;
                            }
                        }
                    }
                }
            }
            if ($lastUploadStatusTimeData === 0) {
                $value['last_upload_status']    = 1;
                if (in_array($companyGspStatus, [CompanyGsp::STATUS_COMPANY_REPORT_FAIL, CompanyGsp::STATUS_REPORT_FAIL])) $value['last_upload_status'] = 3;
            } else if ($lastUploadStatusTimeData === 1) {
                $value['last_upload_status']    = 1;
            } else if ($lastUploadStatusTimeData === 2) {
                $value['last_upload_status']    = 2;
            } else if ($lastUploadStatusTimeData === 3) {
                $value['last_upload_status']   = 2;
            }
            $data[$key] = $value;
        }
        return $data;
    }

    //公司简介数据
    protected function companyProfileData($request)
    {
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $this->createMain($pid);
        $main = self::companyProfileDataSearch($pid);
        return $main;
    }
    //获取公司简介 - 获取
    public function companyProfile(Request $request)
    {
        $main = $this->companyProfileData($request);
        return responseSuccess($main);
    }
    //获取公司简介 - 修改
    public function saveCompanyProfile(Request $request)
    {
        $required   = $request->temp ? 'sometimes' : 'required';
        $input      = $request->input();
        foreach ($input as $key => $value) {
            $input[$key] = $this->removeControlChar($value);
        }
        $validated = $request->validate([
            'establishment_date'            => [$required],
            'group_name'                    => [$required],
            'main_producer_nation'          => [$required],
            'main_producer_region'          => [$required],
            'industry_name'                 => [$required],
            'main_industry_direction'       => [$required],
            'main_product'                  => [$required],

            'group_name_en'                 => [$required],
            'main_producer_region_en'       => [$required],
            'main_industry_direction_en'    => [$required],
            'main_product_en'               => [$required],
        ]);

        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        //判断是否可以提交尽调报告，或填写清单
        if (!$this->isWrite($pid, !empty($user))) return responseFail(__('gsp.submit_repeatedly'));
        try {
            // 开启事务
            DB::beginTransaction();
            $configId = 6;
            $version = GspInvestigationVersions::where([
                ['pid', '=', $pid],
                ['cna_gsp_investigation_config_id', '=', $configId],
            ])->first();

            $this->createMain($pid);
            $main = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->first();
            $main->establishment_date           = $input['establishment_date'] ?? NULL;
            $main->group_name                   = $input['group_name'] ?? '';
            $main->main_producer_nation         = $input['main_producer_nation'] ?? '';
            $main->main_producer_region         = $input['main_producer_region'] ?? '';
            $main->industry_name                = $input['industry_name'] ?? '';
            $main->main_industry_direction      = $input['main_industry_direction'] ?? '';
            $main->main_product                 = $input['main_product'] ?? '';

            $main->group_name_en                = $input['group_name_en'] ?? '';
            $main->main_producer_nation_en      = $main->main_producer_nation;
            $main->main_producer_region_en      = $input['main_producer_region_en'] ?? '';
            $main->industry_name_en             = $main->industry_name;
            $main->main_industry_direction_en   = $input['main_industry_direction_en'] ?? '';
            $main->main_product_en              = $input['main_product_en'] ?? '';
            $main->save();

            $lastTime = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->select('updated_at')->orderBy('updated_at', 'desc')->first();
            if (empty($version['last_modified_time'])) $version = new GspInvestigationVersions();
            if ($version['last_modified_time'] < $lastTime['updated_at'] && !$request->temp) {
                $version->pid = $pid;
                $version->cna_gsp_investigation_config_id = $configId;
                $version->last_modified_time = $lastTime['updated_at'];
                $version->save();
            }
            DB::commit();
            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DefaultException();
        }
    }
    //公司简介生成PDF
    public function companyProfileGeneratePdf(Request $request)
    {
        $main       = $this->companyProfileData($request);
        $fileName   = Lang::get("gsp.company_profile.table_name") . '.pdf';
        $date       = new \DateTime($main['establishment_date']);
        $year       = $date->format('Y'); // 获取年份
        $month      = $date->format('m'); // 获取月份
        $day        = $date->format('d'); // 获取日期          
        $title      = Lang::get("gsp.company_profile.table_title") . '<br>' . Lang::get("gsp.company_profile.table_title_en");
        $column     = '';
        $unit       = ''; //表格单位
        $desc       = ''; // 表格描述
        //转换国家 和地区
        $countryModel = self::countriesData()[$main['main_producer_nation']] ?? [];
        // $countryModel = CountryModel::where('countryID', $main['main_producer_nation'])->first();
        $main['main_producer']      = $countryModel['countryZH'] ?? $main['main_producer_nation'];
        $main['main_producer_en']   = $countryModel['countryEN'] ?? $main['main_producer_nation_en'];
        $main['main_producer']      = $main['main_producer'] . ' ' . $main['main_producer_region'];
        $main['main_producer_en']   = $main['main_producer_en'] . ' ' . $main['main_producer_region_en'];
        $industryName         = array_column(GspInvestigationMain::selectOption()['industry_name'], 'label', 'value');
        $industryNameEn       = array_column(GspInvestigationMain::selectOption()['industry_name_en'], 'label', 'value');
        $content = [
            [
                'title' => '',
                'text' => "公司成立于{$year}年，为{$main['group_name']}集团成员，主要生产地位于{$main['main_producer']}，是一家从事{$industryName[$main['industry_name']]}的企业，一直深耕于{$main['main_industry_direction']}，主要产品包括{$main['main_product']}。<br> The Company established in {$year}, is a member of the {$main['group_name_en']} group. Its main production location is in {$main['main_producer_en']}. It is an enterprise engaged in {$industryNameEn[$main['industry_name_en']]}, has been deeply rooted in {$main['main_industry_direction_en']}. Its main products include {$main['main_product_en']}.  "
            ]
        ];
        $bottom = "";
        $version = GspInvestigationVersions::where([
            ['pid', '=', $main['pid']],
            ['cna_gsp_investigation_config_id', '=', 6],
        ])->first();
        $formModify         = date('Y-m-d', strtotime($version['last_modified_time']));
        $lastModify         = date('Y-m-d H:i:s', strtotime($version['last_modified_time']));
        $token = (new PdfTableService($title, $column,  $content, '', $unit, $desc,  $bottom, '', 0, $fileName, $formModify, $lastModify))->generateStaticPdf();
        return responseSuccess(['token' => $token]);
    }

    //公司简介其他部分数据
    protected function companyProfileOtherData($request)
    {
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $this->createMain($pid);
        $main = self::companyProfileOtherDataSearch($pid);
        return $main;
    }
    //公司简介其他部分 - 获取
    public function companyProfileOther(Request $request)
    {
        $main = $this->companyProfileOtherData($request);
        return responseSuccess($main);
    }

    //公司简介其他部分的总结
    public function companyProfileOtherSummary(Request $request)
    {
        $required   = 'required';
        $input      = $request->input();
        foreach ($input as $key => $value) {
            $input[$key] = $this->removeControlChar($value);
        }
        $validated = $request->validate([
            'company_overview'              => [$required],
            'business_overview'             => [$required],
            'customers_performance'         => [$required],
        ]);
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $this->createMain($pid);
        $main = GspInvestigationMain::where([
            ['pid', '=', $pid],
        ])->first();
        $returnData = $main['summary_of_company_profile'];
        $oldMD5 = md5($main->company_overview . $main->business_overview . $main->customers_performance);
        $newMD5 = md5($input['company_overview'] . $input['business_overview'] . $input['customers_performance']);
        if ($oldMD5 != $newMD5 && !$request->temp) {
            $summaryOfCompanyProfile    = $this->sendPostAiAdvisory($input['company_overview'] . $input['business_overview'] . $input['customers_performance']);
            $returnData                 = $summaryOfCompanyProfile['content'][0];
        }
        return responseSuccess($returnData);
    }

    //公司简介其他部分 - 修改
    public function saveCompanyProfileOther(Request $request)
    {
        $required   = $request->temp ? 'sometimes' : 'required';
        $input      = $request->input();
        foreach ($input as $key => $value) {
            $input[$key] = $this->removeControlChar($value);
        }
        $validated = $request->validate([
            'company_overview'              => [$required],
            'business_overview'             => [$required],
            'customers_performance'         => [$required],
            'summary_of_company_profile'    => [$required],
            'company_overview_en'           => [$required],
            'business_overview_en'          => [$required],
            'customers_performance_en'      => [$required],
            'summary_of_company_profile_en' => [$required],
        ]);
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        //判断是否可以提交尽调报告，或填写清单
        if (!$this->isWrite($pid, !empty($user))) return responseFail(__('gsp.submit_repeatedly'));
        try {
            // 开启事务
            DB::beginTransaction();
            $configId = 39;
            $version = GspInvestigationVersions::where([
                ['pid', '=', $pid],
                ['cna_gsp_investigation_config_id', '=', $configId],
            ])->first();
            $this->createMain($pid);
            $main = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->first();
            // $oldMD5 = md5($main->company_overview . $main->business_overview . $main->customers_performance);
            // $newMD5 = md5($input['company_overview'] . $input['business_overview'] . $input['customers_performance']);
            // if ($oldMD5 != $newMD5 && !$request->temp) {
            //     $summaryOfCompanyProfile           = $this->sendPostAiAdvisory($input['company_overview'] . $input['business_overview'] . $input['customers_performance']);
            //     $main->summary_of_company_profile  = $summaryOfCompanyProfile['content'][0];
            // }
            $main->company_overview                 = $input['company_overview'] ?? '';
            $main->business_overview                = $input['business_overview'] ?? '';
            $main->customers_performance            = $input['customers_performance'] ?? '';
            $main->summary_of_company_profile       = $input['summary_of_company_profile'] ?? '';
            $main->company_overview_en              = $input['company_overview_en'] ?? '';
            $main->business_overview_en             = $input['business_overview_en'] ?? '';
            $main->customers_performance_en         = $input['customers_performance_en'] ?? '';
            $main->summary_of_company_profile_en    = $input['summary_of_company_profile_en'] ?? '';
            $main->save();

            $lastTime = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->select('updated_at')->orderBy('updated_at', 'desc')->first();
            if (empty($version['last_modified_time'])) $version = new GspInvestigationVersions();
            if ($version['last_modified_time'] < $lastTime['updated_at'] && !$request->temp) {
                $version->pid = $pid;
                $version->cna_gsp_investigation_config_id = $configId;
                $version->last_modified_time = $lastTime['updated_at'];
                $version->save();
            }
            DB::commit();
            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DefaultException();
        }
    }
    //公司简介其他部分生成PDF
    public function companyProfileOtherGeneratePdf(Request $request)
    {
        $main       = $this->companyProfileOtherData($request);

        $langTable   = Lang::get("gsp.company_profile_other");
        $fileName    = $langTable['table_name'] . '.pdf';
        $title       = $langTable['table_title'] . '<br>' . $langTable['table_title_en'];
        $column      = "";
        $unit        = $langTable['table_unit']; //表格单位
        $desc        = $langTable['table_desc'];; // 表格描述
        $content = [
            [
                'title' => '公司概述：<br>Company Overview',
                'text' => "{$main['company_overview']}<br>{$main['company_overview_en']}"
            ],
            [
                'title' => '业务概述：<br>Business Overview',
                'text' => "{$main['business_overview']}<br>{$main['business_overview_en']}"
            ],
            [
                'title' => '客户及业绩：<br>Clients/Customers and Track Record(s)',
                'text' => "{$main['customers_performance']}<br>{$main['customers_performance_en']}"
            ],
            [
                'title' => '公司简介<br>Brief Introduction',
                'text' => "{$main['summary_of_company_profile']}<br>{$main['summary_of_company_profile_en']}"
            ],
        ];
        $bottom = "";
        $version = GspInvestigationVersions::where([
            ['pid', '=', $main['pid']],
            ['cna_gsp_investigation_config_id', '=', 39],
        ])->first();
        $formModify         = date('Y-m-d', strtotime($version['last_modified_time']));
        $lastModify         = date('Y-m-d H:i:s', strtotime($version['last_modified_time']));
        $token = (new PdfTableService($title, $column,  $content, '', $unit, $desc,  $bottom, '', 0, $fileName, $formModify, $lastModify))->generateStaticPdf();
        return responseSuccess(['token' => $token]);
    }

    //员工人数数据
    protected function employeesData($request)
    {
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $this->createMain($pid);
        $main = self::employeesDataSearch($pid);
        return $main;
    }

    //员工人数 - 获取
    public function employees(Request $request)
    {
        $main = $this->employeesData($request);
        return responseSuccess($main);
    }

    //员工人数 - 修改
    public function saveEmployees(Request $request)
    {
        $required   = $request->temp ? 'sometimes' : 'required';
        $input      = $request->input();
        foreach ($input as $key => $value) {
            $input[$key] = $this->removeControlChar($value);
        }
        $validated = $request->validate([
            'employee_quantity_statistical_date'    => [$required],
            'employee_quantity_total'               => [$required],
            'employee_quantity_social_insurance'    => [$required],
        ]);
        $validated['employee_quantity_total']            = (int)$validated['employee_quantity_total'];
        $validated['employee_quantity_social_insurance'] = (int)$validated['employee_quantity_social_insurance'];
        if ($validated['employee_quantity_total'] < 0 || $validated['employee_quantity_social_insurance'] < 0) {
            throw new DefaultException("请填写正确数值");
        }

        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        //判断是否可以提交尽调报告，或填写清单
        if (!$this->isWrite($pid, !empty($user))) return responseFail(__('gsp.submit_repeatedly'));
        try {
            // 开启事务
            DB::beginTransaction();
            $configId = 22;
            $version = GspInvestigationVersions::where([
                ['pid', '=', $pid],
                ['cna_gsp_investigation_config_id', '=', $configId],
            ])->first();

            $this->createMain($pid);
            $main = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->first();
            $main->employee_quantity_statistical_date       = $input['employee_quantity_statistical_date'] ?? NUll;
            $main->employee_quantity_total                  = $input['employee_quantity_total'] ?? '';
            $main->employee_quantity_social_insurance       = $input['employee_quantity_social_insurance'] ?? '';
            $main->employee_quantity_statistical_date_en    = $input['employee_quantity_statistical_date'] ?? NUll;
            $main->employee_quantity_total_en               = $input['employee_quantity_total'] ?? '';
            $main->employee_quantity_social_insurance_en    = $input['employee_quantity_social_insurance'] ?? '';
            $main->save();

            $lastTime = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->select('updated_at')->orderBy('updated_at', 'desc')->first();
            if (empty($version['last_modified_time'])) $version = new GspInvestigationVersions();
            if ($version['last_modified_time'] < $lastTime['updated_at'] && !$request->temp) {
                $version->pid = $pid;
                $version->cna_gsp_investigation_config_id = $configId;
                $version->last_modified_time = $lastTime['updated_at'];
                $version->save();
            }
            DB::commit();
            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DefaultException();
        }
    }

    //员工人数生成PDF
    public function employeesGeneratePdf(Request $request)
    {
        $langTable          = Lang::get("gsp.employees");
        $main               = $this->employeesData($request);
        $date               = new \DateTime($main['employee_quantity_statistical_date']);
        $year               = $date->format('Y'); // 获取年份
        $month              = $date->format('m'); // 获取月份
        $day                = $date->format('d'); // 获取日期        
        $title              = $langTable['table_title'] . '<br>' . $langTable['table_title_en'];
        $column             = '';
        $unit               = ''; //表格单位
        $desc               = ''; // 表格描述
        $fileName           = $langTable['table_name'] . '.pdf';
        $content = [
            [
                'title' => '',
                'text' => "截至{$year}年{$month}月{$day}日，目标公司员工总数{$main['employee_quantity_total']}人，社会保险实际缴费人数{$main['employee_quantity_social_insurance']}人。<br> As of {$year}-{$month}-{$day}, the total number of employees in the Company is {$main['employee_quantity_total_en']} and the actual number covered by social insurance is {$main['employee_quantity_social_insurance_en']}."
            ]
        ];
        $bottom = "";
        $version = GspInvestigationVersions::where([
            ['pid', '=', $main['pid']],
            ['cna_gsp_investigation_config_id', '=', 22],
        ])->first();
        $formModify         = date('Y-m-d', strtotime($version['last_modified_time']));
        $lastModify         = date('Y-m-d H:i:s', strtotime($version['last_modified_time']));
        $token = (new PdfTableService($title, $column,  $content, '', $unit, $desc,  $bottom, '', 0, $fileName, $formModify, $lastModify))->generateStaticPdf();
        return responseSuccess(['token' => $token]);
    }

    //公司工商情况数据
    protected function companyBusinessesData($request)
    {
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $this->createMain($pid);
        $main = self::companyBusinessesDataSearch($pid);
        return $main;
    }

    //公司工商情况 - 获取
    public function companyBusinesses(Request $request)
    {
        $main = $this->companyBusinessesData($request);
        return responseSuccess($main);
    }
    //公司工商情况 - 修改
    public function saveCompanyBusinesses(Request $request)
    {
        $required   = $request->temp ? 'sometimes' : 'required';
        $input      = $request->input();
        foreach ($input as $key => $value) {
            $input[$key] = $this->removeControlChar($value);
        }
        $validated = $request->validate([
            'company_name'              => "{$required}",
            'used_name'                 => "{$required}",
            'credit_code'               => "{$required}",
            'registered_country'        => "{$required}",
            // 'registered_region'         => "{$required}",
            'establishment_date'        => "{$required}|date",
            'company_type'              => "{$required}",
            'register_capital'          => "{$required}",
            'register_capital_units'    => "{$required}",
            'paid_capital'              => "{$required}",
            'paid_capital_units'        => "{$required}",
            'registered_address'        => "{$required}",
            'operating_address'         => "{$required}",
            'business_scope'            => "{$required}",
            'annual_inspection'         => "{$required}",
            'operating_period'          => "{$required}",

            'company_name_en'              => "{$required}",
            'used_name_en'                 => "{$required}",
            // 'credit_code_en'               => "{$required}",
            // 'registered_country_en'        => "{$required}",
            // 'registered_region_en'         => "{$required}",
            // 'establishment_date_en'        => "{$required}|date",
            // 'company_type_en'              => "{$required}",
            // 'register_capital_en'          => "{$required}",
            // 'register_capital_units_en'    => "{$required}",
            // 'paid_capital_en'              => "{$required}",
            // 'paid_capital_units_en'        => "{$required}",
            'registered_address_en'        => "{$required}",
            'operating_address_en'         => "{$required}",
            'business_scope_en'            => "{$required}",
            'operating_period_en'          => "{$required}",
        ]);


        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        //判断是否可以提交尽调报告，或填写清单
        if (!$this->isWrite($pid, !empty($user))) return responseFail(__('gsp.submit_repeatedly'));
        try {
            // 开启事务
            DB::beginTransaction();
            $configId = 19;
            $version = GspInvestigationVersions::where([
                ['pid', '=', $pid],
                ['cna_gsp_investigation_config_id', '=', $configId],
            ])->first();

            $this->createMain($pid);
            $main = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->first();
            $main->company_name             = $input['company_name'] ?? '';
            $main->used_name                = $input['used_name'] ?? '';
            $main->credit_code              = $input['credit_code'] ?? '';
            $main->registered_country       = $input['registered_country'] ?? '';
            // $main->registered_region        = $input['registered_region'] ?? '';
            $main->establishment_date       = $input['establishment_date'] ?? NULL;
            $main->company_type             = $input['company_type'] ?? '';
            $main->register_capital         = $input['register_capital'] ?? '';
            $main->register_capital_units   = $input['register_capital_units'] ?? '';
            $main->paid_capital             = $input['paid_capital'] ?? '';
            $main->paid_capital_units       = $input['paid_capital_units'] ?? '';
            $main->registered_address       = $input['registered_address'] ?? '';
            $main->operating_address        = $input['operating_address'] ?? '';
            $main->business_scope           = $input['business_scope'] ?? '';
            $main->annual_inspection        = $input['annual_inspection'] ?? NULL;
            $main->operating_period         = $input['operating_period'] ?? '';

            $main->registered_country_en   = $main->registered_country;
            $main->establishment_date_en   = $main->establishment_date;
            $main->company_type_en         = $main->company_type;
            $main->annual_inspection_en    = $main->annual_inspection;
            $main->company_name_en         = $input['company_name_en'] ?? '';
            $main->used_name_en            = $input['used_name_en'] ?? '';
            $main->credit_code_en          = $input['credit_code'] ?? '';
            // $main->registered_region_en    = $input['registered_region_en'] ?? '';
            $main->registered_address_en   = $input['registered_address_en'] ?? '';
            $main->operating_address_en    = $input['operating_address_en'] ?? '';
            $main->business_scope_en       = $input['business_scope_en'] ?? '';
            $main->operating_period_en     = $input['operating_period_en'] ?? '';
            $main->save();
            $lastTime = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->select('updated_at')->orderBy('updated_at', 'desc')->first();
            if (empty($version['last_modified_time'])) $version = new GspInvestigationVersions();
            if ($version['last_modified_time'] < $lastTime['updated_at'] && !$request->temp) {
                $version->pid = $pid;
                $version->cna_gsp_investigation_config_id = $configId;
                $version->last_modified_time = $lastTime['updated_at'];
                $version->save();
            }
            DB::commit();
            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DefaultException();
        }
    }

    //公司工商情况 生成PDF
    public function companyBusinessesGeneratePdf(Request $request)
    {
        $main = $this->companyBusinessesData($request);
        //转换国家 和地区
        $countryModel = self::countriesData()[$main['registered_country']] ?? [];
        // $countryModel = CountryModel::where('countryID', $main['registered_country'])->first();
        $registeredCountry      = $countryModel['countryZH'] ?? $main['registered_country'];
        $registeredCountryEn    = $countryModel['countryEN'] ?? $main['registered_country_en'];
        $main['registered_country']     = "{$registeredCountry} ";
        $main['registered_country_en']  = "{$registeredCountryEn} ";
        $fileName           = Lang::get("gsp.company_businesses.table_name") . '.pdf';
        $title              = Lang::get("gsp.company_businesses.table_title") . '<br>' . Lang::get("gsp.company_businesses.table_title_en");
        $column             = '';
        $unit               = ''; //表格单位
        $desc               = ''; // 表格描述
        $selectOptionCT     = array_column(GspInvestigationMain::selectOption()['company_type'], 'label', 'value');
        $selectOptionCTEn   = array_column(GspInvestigationMain::selectOption()['company_type_en'], 'label', 'value');
        $selectOptionMU     = array_column(GspInvestigationMain::selectOption()['monetary_unit'], 'label', 'value');
        $main['company_type']       = $selectOptionCT[$main['company_type']] ?? '';
        $main['company_type_en']    = $selectOptionCTEn[$main['company_type_en']] ?? '';
        $main['register_capital']   = $main['register_capital'] . $selectOptionMU[$main['register_capital_units']] ?? '';
        $main['paid_capital']       = $main['paid_capital'] . $selectOptionMU[$main['paid_capital_units']] ?? '';
        $content = [
            ['企业名称<br>Company Name', $main['company_name'] . '<br>' . $main['company_name_en']],
            ['曾用名<br>Previous Company Name', $main['used_name'] . '<br>' . $main['used_name_en']],
            ['统一社会信用代码<br>Company Registration Number', $main['credit_code'] . '<br>' . $main['credit_code_en']],
            ['注册国家或地区<br>Country of Registration', $main['registered_country'] . '<br>' . $main['registered_country_en']],
            ['成立日期<br>Date of Incorporation', $main['establishment_date'] . '<br>' . $main['establishment_date_en']],
            ['公司类型<br>Type of Entity', $main['company_type'] . '<br>' . $main['company_type_en']],
            ['注册资本<br>Registered Capital', $main['register_capital']],
            ['实收资本<br>Paid-in Capital', $main['paid_capital']],
            ['注册地址<br>Registered Address', $main['registered_address'] . '<br>' . $main['registered_address_en']],
            ['经营地址<br>Business Address', $main['operating_address'] . '<br>' . $main['operating_address_en']],
            ['经营范围<br>Scope of Business', $main['business_scope'] . '<br>' . $main['business_scope_en']],
            ['年检情况<br>Latest Annual Return Date', $main['annual_inspection'] . '年度报告' . '<br>' . $main['annual_inspection_en'] . 'Annual Return'],
            ['营业期限<br>Business Period', $main['operating_period'] . '<br>' . $main['operating_period_en']],
        ];
        $bottom = "";
        $version = GspInvestigationVersions::where([
            ['pid', '=', $main['pid']],
            ['cna_gsp_investigation_config_id', '=', 19],
        ])->first();
        $formModify         = date('Y-m-d', strtotime($version['last_modified_time']));
        $lastModify         = date('Y-m-d H:i:s', strtotime($version['last_modified_time']));
        $token = (new PdfTableService($title, $column,  $content, '', $unit, $desc,  $bottom, '', 0, $fileName, $formModify, $lastModify))->generatePdf();
        return responseSuccess(['token' => $token]);
    }

    //未来规划数据
    protected function futurePlanningData($request)
    {
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $this->createMain($pid);
        $main = GspInvestigationMain::select(
            'future_planning_detail',
            'future_planning_detail_en',
            'pid',
        )->where([
            ['pid', '=', $pid],
        ])->first();
        return $main;
    }

    //未来规划 - 获取
    public function futurePlanning(Request $request)
    {
        $main = $this->futurePlanningData($request);
        return responseSuccess($main);
    }
    //未来规划 - 修改
    public function saveFuturePlanning(Request $request)
    {
        $required = $request->temp ? 'sometimes' : 'required';
        $input      = $request->input();
        foreach ($input as $key => $value) {
            $input[$key] = $this->removeControlChar($value);
        }
        $validated = $request->validate([
            'future_planning_detail'        => [$required],
            'future_planning_detail_en'     => [$required],
        ]);

        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        //判断是否可以提交尽调报告，或填写清单
        if (!$this->isWrite($pid, !empty($user))) return responseFail(__('gsp.submit_repeatedly'));
        try {
            // 开启事务
            DB::beginTransaction();
            $configId = 18;
            $version = GspInvestigationVersions::where([
                ['pid', '=', $pid],
                ['cna_gsp_investigation_config_id', '=', $configId],
            ])->first();

            $this->createMain($pid);
            $main = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->first();
            $main->future_planning_detail          = $input['future_planning_detail'] ?? '';
            $main->future_planning_detail_en       = $input['future_planning_detail_en'] ?? '';
            $main->save();

            $lastTime = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->select('updated_at')->orderBy('updated_at', 'desc')->first();
            if (empty($version['last_modified_time'])) $version = new GspInvestigationVersions();
            if ($version['last_modified_time'] < $lastTime['updated_at'] && !$request->temp) {
                $version->pid = $pid;
                $version->cna_gsp_investigation_config_id = $configId;
                $version->last_modified_time = $lastTime['updated_at'];
                $version->save();
            }
            DB::commit();
            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DefaultException();
        }
    }

    //未来规划生成PDF
    public function futurePlanningGeneratePdf(Request $request)
    {
        $main           = $this->futurePlanningData($request);
        $fileName       = Lang::get("gsp.future_planning.table_name") . '.pdf';
        $title          = Lang::get("gsp.future_planning.table_title") . '<br>' . Lang::get("gsp.future_planning.table_title_en");
        $column         = '';
        $unit           = ''; //表格单位
        $desc           = ''; // 表格描述
        $content = [
            [
                'title' => '',
                'text' => $main['future_planning_detail'] . '<br>' . $main['future_planning_detail_en']
            ]
        ];
        $bottom = "";
        $version = GspInvestigationVersions::where([
            ['pid', '=', $main['pid']],
            ['cna_gsp_investigation_config_id', '=', 18],
        ])->first();
        $formModify         = date('Y-m-d', strtotime($version['last_modified_time']));
        $lastModify         = date('Y-m-d H:i:s', strtotime($version['last_modified_time']));
        $token = (new PdfTableService($title, $column,  $content, '', $unit, $desc,  $bottom, '', 0, $fileName, $formModify, $lastModify))->generateStaticPdf();
        return responseSuccess(['token' => $token]);
    }

    //生产与交付的产能计算公式数据
    protected function productionDeliveriesData($request)
    {
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $this->createMain($pid);
        $main = GspInvestigationMain::select(
            'capacity_calculation_formula',
            'capacity_year',
            'capacity_quantity',
            'capacity_savings_account',
            'capacity_calculation_formula_en',
            'capacity_quantity_en',
            'pid',
        )->where([
            ['pid', '=', $pid],
        ])->first();
        $main = $main ? $main->toArray() : [];
        $className  = GspInvestigationMain::$investigation_table_class['cna_gsp_investigation_production_deliveries'];
        $main['dynamic'] = [];
        $data = $className::where([
            ['pid', '=', $pid],
        ])->select('id', 'production_line', 'production_cycle', 'delivery_data', 'remarks', 'production_line_en', 'production_cycle_en', 'delivery_data_en', 'remarks_en')->get()->toArray();
        $main['dynamic'] = $data;
        return $main;
    }

    //生产与交付和产能计算公式 - 获取
    public function productionDeliveries(Request $request)
    {
        $main = $this->productionDeliveriesData($request);
        return responseSuccess($main);
    }
    //生产与交付和产能计算公式 - 修改
    public function saveProductionDeliveries(Request $request)
    {
        $required   = $request->temp ? 'sometimes' : 'required';
        $validated = $request->validate([
            'capacity_calculation_formula'      => [$required],
            'capacity_year'                     => [$required],
            'capacity_quantity'                 => [$required],
            'capacity_savings_account'          => [$required],
            'capacity_calculation_formula_en'   => [$required],
            'capacity_quantity_en'              => [$required],
        ]);

        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        //判断是否可以提交尽调报告，或填写清单
        if (!$this->isWrite($pid, !empty($user))) return responseFail(__('gsp.submit_repeatedly'));
        try {
            // 开启事务
            DB::beginTransaction();
            $configId = 10;
            $version = GspInvestigationVersions::where([
                ['pid', '=', $pid],
                ['cna_gsp_investigation_config_id', '=', $configId],
            ])->first();
            $startTime = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->select('updated_at')->orderBy('updated_at', 'desc')->first();
            $this->createMain($pid);
            $main = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->first();
            $main->capacity_calculation_formula     = $this->removeControlChar($request['capacity_calculation_formula'] ?? '');
            $main->capacity_year                    = $this->removeControlChar($request['capacity_year'] ?? NULL);
            $main->capacity_quantity                = $this->removeControlChar($request['capacity_quantity'] ?? '');
            $main->capacity_savings_account         = $this->removeControlChar($request['capacity_savings_account'] ?? '0.00');
            $main->capacity_calculation_formula_en  = $this->removeControlChar($request['capacity_calculation_formula_en'] ?? '');
            $main->capacity_year_en                 = $main->capacity_year;
            $main->capacity_quantity_en             = $this->removeControlChar($request['capacity_quantity_en'] ?? '');
            $main->capacity_savings_account_en      = $main->capacity_savings_account;
            $main->save();
            $lastTime = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->select('updated_at')->orderBy('updated_at', 'desc')->first();
            if (empty($version['last_modified_time'])) $version = new GspInvestigationVersions();
            if ($startTime['updated_at'] != $lastTime['updated_at'] && !$request->temp) {
                $version->pid = $pid;
                $version->cna_gsp_investigation_config_id = $configId;
                $version->last_modified_time = $lastTime['updated_at'];
                $version->save();
            }

            //修改动态数据
            $tableName  = 'cna_gsp_investigation_production_deliveries';
            $value      = $request['dynamic'];
            //调用对应控制器
            $checkValue = 0;
            $temp       = $request->temp ?? 0;
            $method     = 'POST';
            $this->extraTableHandle($value, $method, $pid, $tableName, $checkValue, $temp);
            DB::commit();
            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DefaultException();
        }
    }

    //生产与交付和产能计算公式生成PDF
    public function productionDeliveriesGeneratePdf(Request $request)
    {
        $main               = $this->productionDeliveriesData($request);
        $user               = $request->attributes->get('user');
        $gsp_id             = $request->attributes->get('gsp_id');
        $record             = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid                = $record['id'];
        $tableName          = "cna_gsp_investigation_production_deliveries";
        $className          = GspInvestigationMain::$investigation_table_class[$tableName];
        $langTable          = Lang::get("gsp.{$tableName}");
        $fileName           = $langTable['table_name'] . '.pdf';
        $title              = $langTable['table_title'] . '<br>' . $langTable['table_title_en'];
        $tableStructure     = $langTable['table_structure'];
        //增加英文版本
        foreach ($tableStructure as $k => $v) {
            if (!str_ends_with($k, "_en")) {
                $tableStructure[$k] = $v . '<br>' . $tableStructure[$k . '_en'];
            } else {
                unset($tableStructure[$k]);
            }
        }
        $column             = array_values($tableStructure);
        $unit               = $langTable['table_unit'] . '<br>' . $langTable['table_unit_en'];
        $desc               = $langTable['table_desc'] . '<br>' . $langTable['table_desc_en']; // 表格描述
        $tableData          = $className::where('pid', $pid)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        //增加英文版本
        foreach ($tableData as $k => $v) {
            foreach ($v as $k2 => $v2) {
                if (!str_ends_with($k2, "_en")) {
                    $tableData[$k][$k2] = $v2 . '<br>' . $tableData[$k][$k2 . '_en'];
                } else {
                    unset($tableData[$k][$k2]);
                }
            }
        }
        foreach ($tableData as $key => $value) {
            $tableData[$key] = array_values($value);
        }
        if (empty($tableData)) {  //处理空数据
            $tableData = [];
            $tableDataNull = [];
            foreach ($column as $tableHeaderValue) {
                $tableDataNull[] = '\\';
            }
            $tableData[] = $tableDataNull;
        }
        $content            = $tableData;
        $bottom             = "";
        $extraTableBottom   = $langTable['table_bottom'] ?? '';  //扩展表底部
        $extraTableBottomEn = $langTable['table_bottom_en'] ?? '';  //扩展表底部
        $bottom             = str_replace([
            '{{capacity_calculation_formula}}',
            '{{capacity_year}}',
            '{{capacity_quantity}}',
            '{{capacity_savings_year_account}}',
            '{{capacity_savings_account}}',
        ], [
            $main['capacity_calculation_formula'] ?? '',
            $main['capacity_year'] ?? '',
            $main['capacity_quantity'] ?? '',
            $main['capacity_year'] ?? '',
            $main['capacity_savings_account'] ?? '',
        ], $extraTableBottom);
        $bottomEn             = str_replace([
            '{{capacity_calculation_formula_en}}',
            '{{capacity_year}}',
            '{{capacity_quantity_en}}',
            '{{capacity_savings_year_account}}',
            '{{capacity_savings_account}}',
        ], [
            $main['capacity_calculation_formula_en'] ?? '',
            $main['capacity_year'] ?? '',
            $main['capacity_quantity_en'] ?? '',
            $main['capacity_year'] ?? '',
            $main['capacity_savings_account'] ?? '',
        ], $extraTableBottomEn);
        $bottom = $bottom . '<br>' . $bottomEn;
        $version = GspInvestigationVersions::where([
            ['pid', '=', $main['pid']],
            ['cna_gsp_investigation_config_id', '=', 10],
        ])->first();
        $formModify         = date('Y-m-d', strtotime($version['last_modified_time']));
        $lastModify         = date('Y-m-d H:i:s', strtotime($version['last_modified_time']));
        $token = (new PdfTableService($title, $column,  $content, '', $unit, $desc,  $bottom, '', 0, $fileName, $formModify, $lastModify))->generatePdf();
        return responseSuccess(['token' => $token]);
    }

    //知识产权数据
    protected function intellectualPropertiesData($request)
    {
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $this->createMain($pid);
        $returnArr  = [];
        $main       = GspInvestigationMain::select(
            'no_cna_gsp_investigation_intellectual_properties',
        )->where([
            ['pid', '=', $pid],
        ])->first();
        $className  = GspInvestigationMain::$investigation_table_class['cna_gsp_investigation_intellectual_properties'];
        $returnArr['dynamic']        = [];
        $returnArr['check_value']    = $main['no_cna_gsp_investigation_intellectual_properties'];
        $data = $className::where([
            ['pid', '=', $pid],
        ])->select('id', 'name', 'intellectual_property_type', 'remark', 'status', 'name_en', 'intellectual_property_type_en', 'remark_en', 'status_en')->get()->toArray();
        foreach ($data as $key => $value) {
            if ($value['intellectual_property_type'] == 40) { //商标
                $brand = CompanyGspReport::where([
                    ['id', '=', $value['name']]
                ])->select('id', 'file_name', 'file_path')->first();
                $data[$key]['brand'] = $brand;
            } else {
                $data[$key]['brand'] = NULL;
            }
        }
        $returnArr['dynamic'] = $data;
        return $returnArr;
    }

    //知识产权 - 获取
    public function intellectualProperties(Request $request)
    {
        $main = $this->intellectualPropertiesData($request);
        return responseSuccess($main);
    }
    //知识产权 - 修改
    public function saveIntellectualProperties(Request $request)
    {
        $required = $request->temp ? 'sometimes' : 'required';
        $validated = $request->validate([
            'check_value'   => [$required],
        ]);
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        //判断是否可以提交尽调报告，或填写清单
        if (!$this->isWrite($pid, !empty($user))) return responseFail(__('gsp.submit_repeatedly'));
        try {
            // 开启事务
            DB::beginTransaction();
            //修改动态数据
            $tableName      = 'cna_gsp_investigation_intellectual_properties';
            $value          = $request['dynamic'];
            foreach ($value as $k => $v) {
                if (isset($v['brand']) || $v['brand'] == '') {
                    if (!empty($v['brand']['id'])) {
                        $v['name']      = $v['brand']['id'] ?? '';
                        $v['name_en']   = $v['brand']['id'] ?? '';
                    }
                    unset($v['brand']);
                }
                $v['status_en'] = $v['status'];
                $v['intellectual_property_type_en'] = $v['intellectual_property_type'];
                $value[$k]  = $v;
            }
            //调用对应控制器
            $checkValue = $request['check_value'] ?? 0;
            $temp       = $request->temp ?? 0;
            $method     = 'POST';
            $this->extraTableHandle($value, $method, $pid, $tableName, $checkValue, $temp);
            DB::commit();
            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DefaultException();
        }
    }

    //知识产权生成PDF
    public function intellectualPropertiesGeneratePdf(Request $request)
    {
        $main               = $this->intellectualPropertiesData($request);
        $user               = $request->attributes->get('user');
        $gsp_id             = $request->attributes->get('gsp_id');
        $record             = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid                = $record['id'];
        $tableName          = "cna_gsp_investigation_intellectual_properties";
        $className          = GspInvestigationMain::$investigation_table_class[$tableName];
        $langTable          = Lang::get("gsp.{$tableName}");
        $fileName           = $langTable['table_name'] . '.pdf';
        $title              = $langTable['table_title'] . '<br>' . $langTable['table_title_en'];
        $tableStructure     = $langTable['table_structure'];
        //增加英文版本
        foreach ($tableStructure as $k => $v) {
            if (!str_ends_with($k, "_en")) {
                $tableStructure[$k] = $v . '<br>' . $tableStructure[$k . '_en'];
            } else {
                unset($tableStructure[$k]);
            }
        }
        $column             = array_values($tableStructure);
        $unit               = $langTable['table_unit'];
        $desc               = $langTable['table_desc']; // 表格描述
        $tableData          = [];
        if (!$main['check_value']) {
            $tableData          = $className::where('pid', $pid)->orderBy('intellectual_property_type')->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        }
        foreach ($tableData as $key => $value) {
            $value['intellectual_property_type_shangbiao'] = false;
            $value['intellectual_property_type_shangbiao_en'] = false;
            //判断是不是上传图片
            if ($value['intellectual_property_type'] == '40') { //商标
                $brand = CompanyGspReport::where([
                    ['id', '=', $value['name']]
                ])->first();
                $value['name'] = !empty($brand['file_path']) ? '<Img src="/' . $brand['file_path'] . '"/>' : '';
                $value['intellectual_property_type_shangbiao'] = true;
                $value['intellectual_property_type_shangbiao_en'] = true;
            }
            $t  = array_column(GspInvestigationMain::selectOption()[$tableName]['intellectual_property_type'], 'label', 'value');
            $t2 = array_column(GspInvestigationMain::selectOption()[$tableName]['intellectual_property_type_en'], 'label', 'value');
            $s  = array_column(GspInvestigationMain::selectOption()[$tableName]['status'], 'label', 'value');
            $s2 = array_column(GspInvestigationMain::selectOption()[$tableName]['status_en'], 'label', 'value');
            $value['intellectual_property_type']    = $t[$value['intellectual_property_type']] ?? '';
            $value['intellectual_property_type_en'] = $t2[$value['intellectual_property_type_en']] ?? '';
            $value['status']    = $s[$value['status']] ?? '';
            $value['status_en'] = $s2[$value['status_en']] ?? '';

            $tableData[$key] = $value;
        }

        //增加英文版本
        foreach ($tableData as $k => $v) {
            foreach ($v as $k2 => $v2) {
                if (!str_ends_with($k2, "_en")) {
                    if ($k2 != 'name' || $v['intellectual_property_type_shangbiao'] !== true) {
                        $tableData[$k][$k2] = $v2 . '<br>' . $tableData[$k][$k2 . '_en'];
                    }
                } else {
                    unset($tableData[$k][$k2]);
                }
                unset($tableData[$k]['intellectual_property_type_shangbiao']);
            }
            $tableData[$k] = array_values($tableData[$k]);
        }
        if (empty($tableData)) {  //处理空数据
            $tableData = [];
            $tableDataNull = [];
            foreach ($column as $tableHeaderValue) {
                $tableDataNull[] = '\\';
            }
            $tableData[] = $tableDataNull;
        }
        $content    = $tableData;
        $bottom     = "";
        $checkText  = $main['check_value'] ? $langTable['check_text'] : '';
        $version = GspInvestigationVersions::where([
            ['pid', '=', $pid],
            ['cna_gsp_investigation_config_id', '=', 23],
        ])->first();
        $formModify         = date('Y-m-d', strtotime($version['last_modified_time']));
        $lastModify         = date('Y-m-d H:i:s', strtotime($version['last_modified_time']));
        $token      = (new PdfTableService($title, $column,  $content, '', $unit, $desc,  $bottom, $checkText, $main['check_value'], $fileName, $formModify, $lastModify))->generatePdf();
        return responseSuccess(['token' => $token]);
    }

    //售后服务及保证数据
    protected function afterSalesData($request)
    {
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $this->createMain($pid);
        $main = self::afterSalesDataSearch($pid);
        return $main;
    }

    //售后服务及保证 - 获取
    public function afterSales(Request $request)
    {
        $main = $this->afterSalesData($request);
        return responseSuccess($main);
    }
    //售后服务及保证 - 修改
    public function saveAfterSales(Request $request)
    {
        $required   = $request->temp ? 'sometimes' : 'required';
        $input      = $request->input();
        foreach ($input as $key => $value) {
            $input[$key] = $this->removeControlChar($value);
        }
        $validated = $request->validate([
            'after_sales_standard'      => [$required],
            'after_sales_mode'          => [$required],
            'after_sales_response'      => [$required],
            'after_sales_coverage'      => [$required],
            'after_sales_standard_en'   => [$required],
            'after_sales_mode_en'       => [$required],
            'after_sales_response_en'   => [$required],
            'after_sales_coverage_en'   => [$required],
        ]);

        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        //判断是否可以提交尽调报告，或填写清单
        if (!$this->isWrite($pid, !empty($user))) return responseFail(__('gsp.submit_repeatedly'));
        try {
            // 开启事务
            DB::beginTransaction();
            $configId = 13;
            $version = GspInvestigationVersions::where([
                ['pid', '=', $pid],
                ['cna_gsp_investigation_config_id', '=', $configId],
            ])->first();

            $this->createMain($pid);
            $main = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->first();
            $main->after_sales_standard         = $input['after_sales_standard'] ?? '';
            $main->after_sales_mode             = $input['after_sales_mode'] ?? '';
            $main->after_sales_response         = $input['after_sales_response'] ?? '';
            $main->after_sales_coverage         = $input['after_sales_coverage'] ?? '';
            $main->after_sales_standard_en      = $input['after_sales_standard_en'] ?? '';
            $main->after_sales_mode_en          = $input['after_sales_mode_en'] ?? '';
            $main->after_sales_response_en      = $input['after_sales_response_en'] ?? '';
            $main->after_sales_coverage_en      = $input['after_sales_coverage_en'] ?? '';
            $main->save();

            $lastTime = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->select('updated_at')->orderBy('updated_at', 'desc')->first();
            if (empty($version['last_modified_time'])) $version = new GspInvestigationVersions();
            if ($version['last_modified_time'] < $lastTime['updated_at'] && !$request->temp) {
                $version->pid = $pid;
                $version->cna_gsp_investigation_config_id = $configId;
                $version->last_modified_time = $lastTime['updated_at'];
                $version->save();
            }
            DB::commit();
            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DefaultException();
        }
    }

    //售后服务及保证生成PDF
    public function saveAfterGeneratePdf(Request $request)
    {
        $main               = $this->afterSalesData($request);
        $fileName           = Lang::get("gsp.after_sales.table_name") . '.pdf';
        $title              = Lang::get("gsp.after_sales.table_title");
        $title              = Lang::get("gsp.after_sales.table_title") . '<br>' . Lang::get("gsp.after_sales.table_title_en");
        $column             = '';
        $unit               = ''; //表格单位
        $desc               = ''; // 表格描述
        $content = [
            ['售后服务<br>After Sales Service', '概述<br>Description'],
            ['服务标准<br>Service Standard', $main['after_sales_standard'] . '<br>' . $main['after_sales_standard_en']],
            ['服务方式<br>Service Mode', $main['after_sales_mode'] . '<br>' . $main['after_sales_mode_en']],
            ['响应时效<br>Response Time', $main['after_sales_response'] . '<br>' . $main['after_sales_response_en']],
            ['覆盖地区<br>Coverage Area', $main['after_sales_coverage'] . '<br>' . $main['after_sales_coverage_en']],
        ];
        $bottom = "";
        $version = GspInvestigationVersions::where([
            ['pid', '=', $main['pid']],
            ['cna_gsp_investigation_config_id', '=', 13],
        ])->first();
        $formModify         = date('Y-m-d', strtotime($version['last_modified_time']));
        $lastModify         = date('Y-m-d H:i:s', strtotime($version['last_modified_time']));
        $token = (new PdfTableService($title, $column,  $content, '', $unit, $desc,  $bottom, '', 0, $fileName, $formModify, $lastModify))->generatePdf();
        return responseSuccess(['token' => $token]);
    }

    //概况摘要数据
    protected function summaryData($request, $getExtra = true)
    {
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $this->createMain($pid);
        $main = self::summaryDataSearch($pid, $getExtra);
        return $main;
    }

    //概况摘要 - 获取
    public function summary(Request $request)
    {
        $main = $this->summaryData($request, false);
        return responseSuccess($main);
    }
    //概况摘要 - 修改
    public function saveSummary(Request $request)
    {
        $required   = $request->temp ? 'sometimes' : 'required';
        $input      = $request->input();
        foreach ($input as $key => $value) {
            $input[$key] = $this->removeControlChar($value);
        }
        $validated = $request->validate([
            // --------- 1. 公司概述 ---------
            // 'establishment_date'                        => "{$required}|date",
            // 'group_name'                                => "{$required}",
            'group_headquarters_address'                   => "{$required}",
            'group_headquarters_address_en'                => "{$required}",
            // 'register_capital'                          => "{$required}",
            // 'register_capital_units'                    => "{$required}",
            // 'paid_capital'                              => "{$required}",
            // 'paid_capital_units'                        => "{$required}",
            // 'company_overview'                          => "{$required}",
            // --------- 1. 公司概述 ---------
            // --------- 2. 业务概述 ---------
            'business_overview_core_product'            => "{$required}",
            'business_overview_core_product_en'         => "{$required}",
            'business_overview_quality_control'         => "{$required}",
            'business_overview_quality_control_en'      => "{$required}",
            'business_overview_after_sales'             => "{$required}",
            'business_overview_after_sales_en'          => "{$required}",
            // 'business_overview'                         => "{$required}",
            // --------- 2. 业务概述 ---------
            // --------- 3. 客户及业绩 ---------
            // 'customers_performance'                     => "{$required}",
            // --------- 3. 客户及业绩 ---------
            // --------- 4. 法务与合规 ---------
            'untreated_administrative_penalty_reason'       => "{$required}",
            'untreated_administrative_penalty_reason_en'    => "{$required}",
            // --------- 4. 法务与合规 ---------
            // --------- 6. 未来规划概况 ---------
            'future_planning'                               => "{$required}",
            'future_planning_en'                            => "{$required}",
            // --------- 6. 未来规划概况 ---------
        ]);
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        //判断是否可以提交尽调报告，或填写清单
        if (!$this->isWrite($pid, !empty($user))) return responseFail(__('gsp.submit_repeatedly'));
        try {
            // 开启事务
            DB::beginTransaction();
            $configId = 5;
            $version = GspInvestigationVersions::where([
                ['pid', '=', $pid],
                ['cna_gsp_investigation_config_id', '=', $configId],
            ])->first();

            $this->createMain($pid);
            $main = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->first();
            // --------- 1. 公司概述 ---------
            // $main->establishment_date           = $input['establishment_date'] ?? NULL;
            // $main->group_name                   = $input['group_name'] ?? '';
            $main->group_headquarters_address       = $input['group_headquarters_address'] ?? '';
            $main->group_headquarters_address_en    = $input['group_headquarters_address_en'] ?? '';
            // $main->register_capital             = $input['register_capital'] ?? '';
            // $main->register_capital_units       = $input['register_capital_units'] ?? '';
            // $main->paid_capital                 = $input['paid_capital'] ?? '';
            // $main->paid_capital_units           = $input['paid_capital_units'] ?? '';
            // $main->company_overview             = $input['company_overview'] ?? '';
            // --------- 1. 公司概述 ---------
            // --------- 2. 业务概述 ---------
            $main->business_overview_core_product           = $input['business_overview_core_product'] ?? '';
            $main->business_overview_core_product_en        = $input['business_overview_core_product_en'] ?? '';
            $main->business_overview_quality_control        = $input['business_overview_quality_control'] ?? '';
            $main->business_overview_quality_control_en     = $input['business_overview_quality_control_en'] ?? '';
            $main->business_overview_after_sales            = $input['business_overview_after_sales'] ?? '';
            $main->business_overview_after_sales_en         = $input['business_overview_after_sales_en'] ?? '';
            // $main->business_overview                    = $input['business_overview'] ?? '';
            // --------- 2. 业务概述 ---------
            // --------- 3. 客户及业绩 ---------
            // $main->customers_performance                    = $input['customers_performance'] ?? '';
            // --------- 3. 客户及业绩 ---------
            // --------- 4. 法务与合规 ---------
            $main->untreated_administrative_penalty_reason      = $input['untreated_administrative_penalty_reason'] ?? '';
            $main->untreated_administrative_penalty_reason_en   = $input['untreated_administrative_penalty_reason_en'] ?? '';
            // --------- 4. 法务与合规 ---------
            // --------- 6. 未来规划概况 ---------
            $main->future_planning      = $input['future_planning'] ?? '';
            $main->future_planning_en   = $input['future_planning_en'] ?? '';
            // --------- 6. 未来规划概况 ---------
            $main->save();

            $lastTime = GspInvestigationMain::where([
                ['pid', '=', $pid],
            ])->select('updated_at')->orderBy('updated_at', 'desc')->first();
            if (empty($version['last_modified_time'])) $version = new GspInvestigationVersions();
            if ($version['last_modified_time'] < $lastTime['updated_at'] && !$request->temp) {
                $version->pid = $pid;
                $version->cna_gsp_investigation_config_id = $configId;
                $version->last_modified_time = $lastTime['updated_at'];
                $version->save();
            }
            DB::commit();
            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DefaultException();
        }
    }

    //概况摘要生成PDF
    public function summaryGeneratePdf(Request $request)
    {
        $main               = $this->summaryData($request);
        //判断是否有未填写的
        if (!$main['establishment_date'] || !$main['register_capital_units'] || !$main['register_capital'] || !$main['paid_capital_units'] || !$main['paid_capital']) throw new DefaultException("请填写公司工商情况再生成PDF");
        if (!$main['group_name']) throw new DefaultException("请填写公司简介再生成PDF");
        if (!$main['company_overview']) throw new DefaultException("请填写公司概况及简介其他部分再生成PDF");
        if (!$main['shareholder_name'] || !$main['shareholder_ratio']) throw new DefaultException("请填写股东情况再生成PDF");
        if (!$main['office_spaces_area']) throw new DefaultException("请填写生产及办公面积再生成PDF");
        if (!$main['production_equipment_name']) throw new DefaultException("请填写前五大主要生产设备再生成PDF");
        if (!$main['raw_materials_country'] || !$main['raw_materials_supplier']) throw new DefaultException("请填写前十主要原材料及来源占比再生成PDF");
        if (!$main['customer_situation_industry'] || !$main['customer_situation_name'] || !$main['customer_situation_amount']) throw new DefaultException("请填写前五大主要客户情况再生成PDF");
        if (!$main['product_application_cases_project_name'] || !$main['product_application_cases_country'] || !$main['product_application_cases_amount']) throw new DefaultException("请填写产品应用案例再生成PDF");
        if (empty($main['no_cna_gsp_investigation_qualifications']) && !$main['qualifications_certificate_name']) throw new DefaultException("请填写资质及认证再生成PDF");
        if (empty($main['no_cna_gsp_investigation_intellectual_properties']) && !$main['intellectual_property_type']) throw new DefaultException("请填写知识产权再生成PDF");
        if (!$main['significant_contracts_count']) throw new DefaultException("请填写重大合同再生成PDF");
        if (empty($main['no_cna_gsp_investigation_litigation_arbitration_cases']) && (!$main['litigation_arbitration_cases_count'] || !$main['litigation_arbitration_cases_amount'])) throw new DefaultException("请填写重大诉讼及仲裁信息再生成PDF");
        if (empty($main['no_cna_gsp_investigation_administrative_penalty_records']) && (!$main['administrative_penalty_records_count'])) throw new DefaultException("请填写行政处罚记录再生成PDF");
        if (empty($main['no_cna_gsp_investigation_dishonesty_records']) && (!$main['dishonesty_records_count'])) throw new DefaultException("请填写失信记录再生成PDF");
        $selectOptionMU                 = array_column(GspInvestigationMain::selectOption()['monetary_unit'], 'label', 'value');
        $main['register_capital_units'] = $selectOptionMU[$main['register_capital_units']];
        $main['paid_capital_units']     = $selectOptionMU[$main['paid_capital_units']];
        $fileName           = Lang::get("gsp.summary.table_name") . '.pdf';
        $date               = new \DateTime($main['establishment_date']);
        $establishmentYear  = $date->format('Y'); // 获取年份
        $title              = Lang::get("gsp.summary.table_title");
        $column             = '';
        $unit               = ''; //表格单位
        $desc               = ''; // 表格描述
        $content = [
            [
                'title' => '1. 公司概<br>1. Company Overview',
                'text' => "公司成立于{$establishmentYear}年，为{$main['group_name']}集团成员，总部位于{$main['group_headquarters_address']}。截至报告基准日，公司注册资本为{$main['register_capital']} {$main['register_capital_units']}，实收资本为{$main['paid_capital']} {$main['paid_capital_units']}。主要股东包括{$main['shareholder_name']}（持股比例共计{$main['shareholder_ratio']}%）。<br>{$main['company_overview']}<br>The Company was established in {$establishmentYear}, it is a member of the {$main['group_name_en']} Group, having its headquarter in {$main['group_headquarters_address_en']}. As of the Report Reference Date, the registered capital of the Company is {$main['register_capital']} {$main['register_capital_units']}, the paid-in capital is {$main['paid_capital']} {$main['paid_capital_units']}. The main shareholders include {$main['shareholder_name_en']} (with a shareholding ratio of {$main['shareholder_ratio']}%).<br>{$main['company_overview_en']}"
            ],
            [
                'title' => '2. 业务概述<br>2. Business Overview',
                'text' => "目标公司的核心业务集中在{$main['business_overview_core_product']}，生产及办公面积共计{$main['office_spaces_area']}平方米，配备了{$main['production_equipment_name']}等关键设施。原材料采购来自{$main['raw_materials_country']}如{$main['raw_materials_supplier']}、生产过程及质量控制为{$main['business_overview_quality_control']}。售后服务承诺{$main['business_overview_after_sales']}，满足客户需求。 <br>{$main['business_overview']}<br>The Company's core business focuses on {$main['business_overview_core_product_en']},  with a production plant(s) and office occupy a total area of {$main['office_spaces_area']} square metres, equipped with key facilities such as {$main['production_equipment_name_en']}.The main raw materials come from {$main['raw_materials_country_en']}, such as {$main['raw_materials_supplier_en']}. The quality control system is described as {$main['business_overview_quality_control_en']}. After sales service and  guarantee/warranties are {$main['business_overview_after_sales_en']}, to meet customer needs.<br>{$main['business_overview_en']}"
            ],
            [
                'title' => '3. 客户及业绩<br>3. Clients/Customers and Track Record(s)',
                'text' => "客户及终端用户涵盖{$main['customer_situation_industry']}的企业，包括{$main['customer_situation_name']}（年交易金额合计人民币{$main['customer_situation_amount']}元）。产品应用案例包括{$main['product_application_cases_project_name']},涉及{$main['product_application_cases_country']}，供货金额合计人民币{$main['product_application_cases_amount']}元。<br>{$main['customers_performance']}<br>The Company's clients/customers and end-users cover {$main['customer_situation_industry_en']}, including {$main['customer_situation_name_en']} (with a total annual transaction value of CNY {$main['customer_situation_amount']} ). Product application case(s) include {$main['product_application_cases_project_name_en']}, involving in {$main['product_application_cases_country_en']}, recorded total product supply value of CNY {$main['product_application_cases_amount']}.<br>{$main['customers_performance_en']}"
            ],
            [
                'title' => '4. 法务与合规<br>4. Legal Affairs and Compliance',
                'text' => "目标公司已获得{$main['qualifications_certificate_name']}和知识产权{$main['intellectual_property_type']}。涉及{$main['significant_contracts_count']}份的重大合同（金额超过人民币{$main['significant_contracts_amount']}元）。近三年共有{$main['litigation_arbitration_cases_count']}起重大诉讼/仲裁案件，涉及金额共计人民币{$main['litigation_arbitration_cases_amount']}元；未处理的行政处罚共{$main['administrative_penalty_records_count']}件，主要是由于{$main['untreated_administrative_penalty_reason']}。有{$main['dishonesty_records_count']}条失信记录。<br>The Company has obtained {$main['qualifications_certificate_name_en']} and intellectual property such as {$main['intellectual_property_type_en']}. Major contracts involve {$main['significant_contracts_count']} with a value exceeding CNY {$main['significant_contracts_amount']} . There is/are currently {$main['litigation_arbitration_cases_count']} lawsuits and arbitration case(s) involving a total value of CNY {$main['litigation_arbitration_cases_amount']} in past three years; The number of unresolved administrative penalty record(s) is/are {$main['administrative_penalty_records_count']}, is/are mainly due to {$main['untreated_administrative_penalty_reason_en']}. There is/are {$main['dishonesty_records_count']} record(s) of breach of trust."
            ],
            [
                'title' => '5. 财务报告<br>5. Financial Information',
                'text' => "目标公司委托{$main['comprehensive_incomes_accounting_name']}出具的审计报告显示，目标公司{$main['comprehensive_incomes_year1']}年至{$main['comprehensive_incomes_year2']}年分别实现收入人民币{$main['comprehensive_incomes_revenue1']}元和人民币{$main['comprehensive_incomes_revenue2']}元，净利润率保持在{$main['comprehensive_incomes_profit_for_the_period1']}%-{$main['comprehensive_incomes_profit_for_the_period2']}%。资产负债率为{$main['balance_sheet_asset_liability_ratio1']}% - 资产负债率为{$main['balance_sheet_asset_liability_ratio2']}%<br>Financial data of the Audit Report issued by {$main['comprehensive_incomes_accounting_name_en']} entrusted by the Company shows that the Company's achieving revenues from {$main['comprehensive_incomes_year1']} to {$main['comprehensive_incomes_year2']} are  CNY {$main['comprehensive_incomes_revenue1']} and CNY {$main['comprehensive_incomes_revenue2']}  respectively , with a net profit margin maintained at  {$main['comprehensive_incomes_profit_for_the_period1']}% - {$main['comprehensive_incomes_profit_for_the_period2']}%.  The debt-to-asset ratio was  {$main['balance_sheet_asset_liability_ratio1']}%- {$main['balance_sheet_asset_liability_ratio2']}%."
            ],
            [
                'title' => '6. 未来规划概况<br>6. Future Plan(s)',
                'text' => $main['future_planning'] . '<br>' . $main['future_planning_en']
            ],
        ];
        $bottom = "";
        $version = GspInvestigationVersions::where([
            ['pid', '=', $main['pid']],
            ['cna_gsp_investigation_config_id', '=', 5],
        ])->first();
        $formModify         = date('Y-m-d', strtotime($version['last_modified_time']));
        $lastModify         = date('Y-m-d H:i:s', strtotime($version['last_modified_time']));
        $token = (new PdfTableService($title, $column,  $content, '', $unit, $desc,  $bottom, '', 0, $fileName, $formModify, $lastModify))->generateStaticPdf();
        return responseSuccess(['token' => $token]);
    }



    /**
     *{
     *    "data": 
     *        {
     *            "key": "cna_gsp_investigation_administrative_penalty_records",
     *            "check_value": 0
     *            "value": 
     *                [
     *                    {
     *                        "id": 7,
     *                        "reason": "222",
     *                        "content": "xxxx",
     *                        "penalty_date": "2024-05-06",
     *                        "penalty_unit": "xxxx"
     *                    },
     *                    {
     *                        "reason": "333",
     *                        "content": "xxxx",
     *                        "penalty_date": "2024-05-06",
     *                        "penalty_unit": "xxxx"
     *                    }                    
     *                ]
     *        }
     *    
     *}
     *
     * @param  mixed $request
     * @return void
     */
    public function extraTable(Request $request)
    {
        $validated = $request->validate([
            'data'          => 'sometimes',
            'key'           => 'sometimes',
        ]);
        $user               = $request->attributes->get('user');
        $gsp_id             = $request->attributes->get('gsp_id');
        $record             = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $validated['pid']   = $record['id'];
        $method             = $request->method();
        if ($method == 'GET') {
            $validated['data'] = ['key' => $validated['key'], 'value' => '',];
        } else {
            //判断是否可以提交尽调报告，或填写清单
            if (!$this->isWrite($validated['pid'], !empty($user))) return responseFail(__('gsp.submit_repeatedly'));
        }
        $res                = null;
        try {
            // 开启事务
            DB::beginTransaction();
            $value = $validated['data'];
            //调用对应控制器
            if (array_key_exists($value['key'], GspInvestigationMain::$extra_table)) {
                $tableName  = $value['key'];
                $pid        = $validated['pid'];
                $checkValue = $value['check_value'] ?? 0;
                $temp       = $value['temp'] ?? 0;
                $res        = $this->extraTableHandle($value['value'], $method, $pid, $tableName, $checkValue, $temp);
            }
            // 提交
            DB::commit();
            return responseSuccess($res);
        } catch (\Exception $e) {
            DB::rollBack();
            $message = mb_substr($e->getMessage(), 0, 20);
            return responseFail("未知错误，请联系管理员! {$message}");
        }
    }

    //扩展表处理
    protected function extraTableHandle($request, $method, $pid, $tableName, $requestCheckValue, $temp)
    {
        //获取对应的表模型类
        $className  = GspInvestigationMain::$investigation_table_class[$tableName];
        $langTable  = Lang::get("gsp.{$tableName}");
        $this->createMain($pid);
        $main      =  GspInvestigationMain::where([
            ['pid', '=', $pid],
        ])->first();
        if ($method == 'GET') {
            //获取勾选框
            $checkText      = $langTable['check_text'] ?: '';
            $checkTextEn    = $langTable['check_text_en'] ?: '';
            $checkValue     = $main["no_{$tableName}"] ?? 0;
            // 制作表头
            $columns = DB::select(
                "SELECT 
                    COLUMN_NAME AS name, 
                    COLUMN_TYPE AS type, 
                    COLUMN_COMMENT AS comment 
                FROM information_schema.COLUMNS 
                WHERE TABLE_NAME = ? 
                AND TABLE_SCHEMA = DATABASE() 
                AND COLUMN_NAME <> 'created_at'
                AND COLUMN_NAME <> 'updated_at'
                AND COLUMN_NAME <> 'id'
                AND COLUMN_NAME <> 'pid'
                ORDER BY 
                    ORDINAL_POSITION",
                [$tableName]
            );
            $tableHeader = [];
            foreach ($columns as $column) {
                $wordCount  = 0;
                $options    = [];
                $type       = $column->type;
                if (strpos($type, 'varchar') !== false) {
                    $type = "text";
                }
                if (strpos($type, 'year') !== false) {
                    $type = "year";
                }
                if (strpos($type, 'date') !== false) {
                    $type = "date";
                }
                if (strpos($type, 'timestamp') !== false) {
                    $type = "timestamp";
                }
                if (strpos($type, 'decimal') !== false) {
                    $type = "decimal";
                }
                if (strpos($type, 'int') !== false && strpos($type, 'unsigned') !== false) {
                    $type = "uint";
                }
                //下拉选择
                if (!empty(GspInvestigationMain::$select[$tableName])) {
                    if (!empty(GspInvestigationMain::$select[$tableName][$column->name])) {
                        $options    = GspInvestigationMain::$select[$tableName][$column->name];
                        $type       = 'select';
                    }
                }
                //选择国家地区
                if (!empty(GspInvestigationMain::$select_country[$tableName])) {
                    if (!empty(GspInvestigationMain::$select_country[$tableName][$column->name])) {
                        $type = 'select_country';
                    }
                }
                //百分比
                if (!empty(GspInvestigationMain::$percent_type[$tableName])) {
                    if (!empty(GspInvestigationMain::$percent_type[$tableName][$column->name])) {
                        $type = 'percent';
                    }
                }
                //字数限制
                if (!empty($langTable['word_count'][$column->name])) {
                    $wordCount = $langTable['word_count'][$column->name];
                }
                $tableHeader[] = [
                    'key'           => $column->name,
                    'type'          => $type,
                    'value'         => $langTable['table_structure'][$column->name . '_client_show'] ?? $langTable['table_structure'][$column->name] ?? '',
                    'options'       => $options,
                    '_en'           => [],
                    'word_count'    => $wordCount,
                ];
            }
            //读取英文版本
            $tableHeader = array_column($tableHeader, NULL, 'key');
            foreach ($tableHeader as $tak => $tav) {
                if (substr($tak, -strlen('_en')) === '_en') {
                    unset($tableHeader[$tak]);
                } else {
                    $tableHeader[$tak]['_en'] = $tableHeader[$tak . '_en'];
                }
            }
            $tableHeader = array_values($tableHeader);
            //查找
            $tableData = $className::where('pid', $pid)->get();
            $tableBottom = "";
            if ($tableName == 'cna_gsp_investigation_controlling_shareholder') {
                $tableBottom = $langTable['table_bottom'];
            }
            return [
                'table_data'            => $tableData,
                'table_header'          => $tableHeader,
                'table_title'           => $langTable['table_name'],
                'table_title_en'        => $langTable['table_title_en'],
                'table_comment'         => $langTable['table_desc'] ?? '',
                'table_comment_en'      => $langTable['table_desc_en'] ?? '',
                'table_bottom'          => $tableBottom,
                'table_right_unit'      => $langTable['table_unit'] ?? '',
                'table_right_unit_en'   => $langTable['table_unit_en'] ?? '',
                'table_check'           => ['name' => $checkText, 'name_en' => $checkTextEn, 'value' => $checkValue],
                'max_rows'              => $langTable['max_rows'] ?? 0,
                'show_en'               => $langTable['show_en'],
            ];
        } else {
            $configId = GspInvestigationConfig::where('db_name', $tableName)->value('id');
            $version = GspInvestigationVersions::where([
                ['pid', '=', $pid],
                ['cna_gsp_investigation_config_id', '=', $configId],
            ])->first();

            //公司声明没有内容
            if (isset($main["no_{$tableName}"])) {
                $main["no_{$tableName}"] = $requestCheckValue;
                $main->save();
            }

            //已有数据新增和修改
            if (!empty($request[0])) {
                //删除不存的
                $input = array_column($request, 'id');
                $exist = $className::where([
                    ['pid', '=', $pid]
                ])->pluck('id')->toArray();
                $del = array_diff($exist, $input);
                $className::whereIn('id', $del)->delete();
                //检查数据
                if (!empty(GspInvestigationMain::customCheck()[$tableName])) {
                    GspInvestigationMain::customCheck()[$tableName]($request, $temp);
                }
                foreach ($request as $key => $value) {
                    if (!empty($value['id'])) {
                        //修改
                        $model = $className::where([
                            ['id', '=', $value['id']],
                            ['pid', '=', $pid]
                        ])->first();
                        if (!$model) throw new DefaultException("找不到扩展表");
                    } else {
                        //写入
                        $model = new $className();
                        $model->pid = $pid;
                    }
                    unset($value['id']);
                    unset($value['pid']);
                    unset($value['created_at']);
                    unset($value['updated_at']);
                    // 获取该表的所有字段名
                    $columns = array_flip(Schema::getColumnListing($tableName));
                    unset($columns['id']);
                    unset($columns['pid']);
                    unset($columns['created_at']);
                    unset($columns['updated_at']);
                    if (!$temp && !$requestCheckValue) { //验证空
                        foreach ($value as $hk => $hv) {
                            if (!str_ends_with($hk, '_en') && empty($value[$hk . '_en'])) {
                                $value[$hk . '_en'] = $hv;
                            }
                        }
                        if (count(array_keys($columns)) !== count(array_keys($value))) throw new DefaultException("请填写完整");
                        foreach ($value as $mk => $mv) {
                            if (!isset($mv)) throw new DefaultException("请填写完整");
                        }
                    }
                    $default = $this->getTableDefaultValue($tableName);
                    foreach ($columns as $mk => $mv) {
                        $model->$mk = $default[$mk];
                        if (isset($value[$mk]) && strtolower($value[$mk]) != 'null') {   // 传null不写入
                            $model->$mk = $this->removeControlChar($value[$mk]);
                        }
                    }
                    $model->save();
                }
            } else {
                //删除所有
                $className::where('pid', $pid)->delete();
            }


            $lastTime = $className::where([
                ['pid', '=', $pid],
            ])->select('updated_at')->orderBy('updated_at', 'desc')->first();
            if (empty($lastTime)) $lastTime['updated_at'] = date('Y-m-d H:i:s', time());
            if (empty($version['last_modified_time'])) $version = new GspInvestigationVersions();
            if ($version['last_modified_time'] < $lastTime['updated_at'] && !$temp) {
                $version->pid = $pid;
                $version->cna_gsp_investigation_config_id = $configId;
                $version->last_modified_time = $lastTime['updated_at'];
                $version->save();
            }
        }
    }

    //动态表生成PDF
    public function generatePdf(Request $request)
    {
        $validated = $request->validate([
            'id'           => 'required',
        ]);
        $user               = $request->attributes->get('user');
        $gsp_id             = $request->attributes->get('gsp_id');
        $record             = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid                = $record['id'];
        $config = GspInvestigationConfig::find($validated['id']);
        if (!$config) throw new DefaultException("找不到对应的表单");
        if (empty($config['db_name'])) throw new DefaultException("找不到对应的表单");
        $tableName          = $config['db_name'];
        //获取对应的表模型类
        $className = GspInvestigationMain::$investigation_table_class[$tableName];
        $main      =  GspInvestigationMain::where([
            ['pid', '=', $pid],
        ])->first();
        // 制作表头
        $columns = DB::select(
            "SELECT 
                COLUMN_NAME AS name, 
                COLUMN_TYPE AS type, 
                COLUMN_COMMENT AS comment 
            FROM information_schema.COLUMNS 
            WHERE TABLE_NAME = ? 
            AND TABLE_SCHEMA = DATABASE() 
            AND COLUMN_NAME <> 'created_at'
            AND COLUMN_NAME <> 'updated_at'
            AND COLUMN_NAME <> 'id'
            AND COLUMN_NAME <> 'pid'
            ORDER BY 
                ORDINAL_POSITION",
            [$tableName]
        );
        $columnType         = array_column($columns, 'type', 'name');
        $tableHeader        = [];
        $langTable          = Lang::get("gsp.{$tableName}");
        $tableStructure     = $langTable['table_structure'] ?? [];
        $tableDesc          = $langTable['table_desc'] ?? '';
        $tableDescEn        = $langTable['table_desc_en'] ?? '';
        $tableUnit          = $langTable['table_unit'] ?? '';
        $tableUnitEn        = $langTable['table_unit_en'] ?? '';
        $checkValue         = $main["no_{$tableName}"] ?? 0;
        $checkText          = $checkValue ? $langTable['check_text'] : '';
        $tableData          = [];
        if (!$checkValue) {
            if ($tableName == 'cna_gsp_investigation_controlling_shareholder') {
                $tableData = $className::where('pid', $pid)->selectRaw('*,"" as sign,"" as sign_en')->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
            } else {
                $tableData = $className::where('pid', $pid)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
            }
        }
        //去除客户端显示的字段
        foreach ($tableStructure as $k => $v) {
            if (strpos($k, '_client_show') !== false) {
                unset($tableStructure[$k]);
            }
        }
        //增加英文版本
        foreach ($tableStructure as $k => $v) {
            if (!str_ends_with($k, "_en")) {
                $tableStructure[$k] = $this->zhAndEnPDF($v, $tableStructure[$k . '_en'], $langTable['show_en']);
            } else {
                unset($tableStructure[$k]);
            }
        }

        foreach ($tableData as $key => $value) {
            foreach ($value as $k => $v) {
                if (!empty(GspInvestigationMain::$select[$tableName])) {
                    if (!empty(GspInvestigationMain::$select[$tableName][$k])) {
                        $arr = array_column(GspInvestigationMain::$select[$tableName][$k], 'label', 'value');
                        $value[$k] = $arr[$v] ?? '';
                    }
                }
                //转换国家 和地区
                if (!empty($columnType[$k]) && $k == 'country') {
                    $countryModel = self::countriesData()[$v] ?? [];
                    // $countryModel = CountryModel::where('countryID', $v)->first();
                    $value[$k] = $countryModel['countryZH'] ?? $v;
                    if (!empty($value['region'])) {
                        $value[$k] = $value[$k] . ' - ' . $value['region'];
                        unset($value['region']);
                        unset($tableStructure['region']);
                    }
                }
                //转换国家 和地区 英文
                if (!empty($columnType[$k]) && $k == 'country_en') {
                    $countryModel = self::countriesData()[$v] ?? [];
                    // $countryModel = CountryModel::where('countryID', $v)->first();
                    $value[$k] = $countryModel['countryEN'] ?? $v;
                    if (!empty($value['region_en'])) {
                        $value[$k] = $value[$k] . ' - ' . $value['region_en'];
                        unset($value['region_en']);
                        unset($tableStructure['region_en']);
                    }
                }
                //转换日期
                if (!empty($columnType[$k]) && $columnType[$k] == 'date') {
                    $date = new \DateTime($v);
                    // $value[$k] = $date->format('Y年m月d日');
                    $value[$k] = $date->format('Y-m-d');
                }
                //百分比处理
                if (!empty(GspInvestigationMain::$percent_type[$tableName])) {
                    if (!empty(GspInvestigationMain::$percent_type[$tableName][$k])) {
                        $value[$k] = $v . '%';
                    }
                }
            }
            $tableData[$key] = $value;
        }
        //增加英文版本
        foreach ($tableData as $k => $v) {
            foreach ($v as $k2 => $v2) {
                if (!str_ends_with($k2, "_en")) {
                    $tableData[$k][$k2] = $this->zhAndEnPDF($v2, $tableData[$k][$k2 . '_en'], $langTable['show_en']);
                } else {
                    unset($tableData[$k][$k2]);
                }
            }
        }
        $version = GspInvestigationVersions::where([
            ['pid', '=', $pid],
            ['cna_gsp_investigation_config_id', '=', $config['id']],
        ])->first();
        $tableHeader        = array_values($tableStructure);
        if (empty($tableData)) {  //处理空数据
            $tableData = [];
            $tableDataNull = [];
            foreach ($tableHeader as $tableHeaderValue) {
                $tableDataNull[] = '\\';
            }
            $tableData[] = $tableDataNull;
        }
        $title              = $this->zhAndEnPDF($langTable['table_title'], $langTable['table_title_en'], $langTable['show_en']);
        $column             = $tableHeader;
        $unit               = $this->zhAndEnPDF($tableUnit, $tableUnitEn, $langTable['show_en']); //表格单位
        $desc               = $this->zhAndEnPDF($tableDesc, $tableDescEn, $langTable['show_en']); // 表格描述
        $bottom             = '';
        $formModify         = date('Y-m-d', strtotime($version['last_modified_time']));
        $lastModify         = date('Y-m-d H:i:s', strtotime($version['last_modified_time']));
        $fileName           = $langTable['table_name'] . '.pdf';
        $content            = $tableData;
        if ($tableName == 'cna_gsp_investigation_production_deliveries') {
            $extraTableBottom   = $langTable['table_bottom'] ?? '';  //扩展表底部
            $bottom             = str_replace([
                '{{capacity_calculation_formula}}',
                '{{capacity_year}}',
                '{{capacity_quantity}}',
                '{{capacity_savings_year_account}}',
                '{{capacity_savings_account}}',
            ], [
                $main['capacity_calculation_formula'] ?? '',
                $main['capacity_year'] ?? '',
                $main['capacity_quantity'] ?? '',
                $main['capacity_savings_year_account'] ?? '',
                $main['capacity_savings_account'] ?? '',
            ], $extraTableBottom);
        }
        $token = (new PdfTableService($title, $column,  $content, '', $unit, $desc,  $bottom, $checkText, $checkValue, $fileName, $formModify, $lastModify))->generatePdf();
        return responseSuccess(['token' => $token]);
    }

    //财务报表 - 综合损益表/利润表
    public function comprehensiveIncomeData($request)
    {
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $res = GspInvestigationComprehensiveIncomes::where([
            ['pid', '=', $pid]
        ])->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        // $res = array_column($res, NULL, 'year');
        return $res;
    }
    //财务报表 - 综合损益表/利润表
    public function comprehensiveIncome(Request $request)
    {
        $res = $this->comprehensiveIncomeData($request);
        return responseSuccess($res);
    }
    //财务报表 - 综合损益表/利润表 - 修改
    public function saveComprehensiveIncome(Request $request)
    {
        $required   = $request->temp ? 'sometimes' : 'required';
        $validated = $request->validate([
            'data'                  => [$required],
            'accounting_name'       => [$required],
            'accounting_name_en'    => [$required],
        ]);
        $input      = $request->input();
        if (count($input['data']) != 2 && !$request->temp) throw new DefaultException("需要两个年份");
        foreach ($input['data'] as $key => $value) {
            foreach ($value as $k => $v) {
                $value[$k] = $this->removeControlChar($v);
            }
            $input['data'][$key] = $value;
        }
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        //判断是否可以提交尽调报告，或填写清单
        if (!$this->isWrite($pid, !empty($user))) return responseFail(__('gsp.submit_repeatedly'));
        try {
            // 开启事务
            DB::beginTransaction();
            $configId   = 30;
            $version    = GspInvestigationVersions::where([
                ['pid', '=', $pid],
                ['cna_gsp_investigation_config_id', '=', $configId],
            ])->first();
            //删除不存的
            $inputYear = array_column($validated['data'], 'year');
            $existYear = GspInvestigationComprehensiveIncomes::where([
                ['pid', '=', $pid]
            ])->pluck('year', 'id')->toArray();
            $del = array_diff($existYear, $inputYear);
            GspInvestigationComprehensiveIncomes::whereIn('id', array_keys($del))->delete();
            foreach ($validated['data'] as $key => $value) {
                $model = GspInvestigationComprehensiveIncomes::where([
                    ['pid', '=', $pid],
                    ['year', '=', $value['year']],
                ])->first();
                if (empty($model)) $model = new GspInvestigationComprehensiveIncomes();
                $model->pid = $pid;
                $model->accounting_name     = $validated['accounting_name'];
                $model->accounting_name_en  = $validated['accounting_name_en'];
                $model->year = $value['year'];
                $model->revenue = $value['revenue'] ?? NULL;
                $model->cogs = $value['cogs'] ?? NULL;
                $model->gross_profit = $this->bcSubMultiple([$model->revenue, $model->cogs], 2);    //毛利
                $model->research_and_development_expense = $value['research_and_development_expense'] ?? NULL;
                $model->general_expenses = $value['general_expenses'] ?? NULL;
                $model->administrative_expenses = $value['administrative_expenses'] ?? NULL;
                $model->general_and_administrative_expense = $this->bcAddMultiple([$model->general_expenses, $model->administrative_expenses], 2); //销售及管理费用
                $model->net_exposure_hedging_loss = $value['net_exposure_hedging_loss'] ?? NULL;
                $model->fair_value_change_loss = $value['fair_value_change_loss'] ?? NULL;
                $model->credit_impairment_loss = $value['credit_impairment_loss'] ?? NULL;
                $model->asset_impairment_loss = $value['asset_impairment_loss'] ?? NULL;
                $model->asset_disposal_loss = $value['asset_disposal_loss'] ?? NULL;
                $model->other_non_operating_expenses = $value['other_non_operating_expenses'] ?? NULL;
                $model->other_operating_expenses = $this->bcAddMultiple([
                    $model->net_exposure_hedging_loss,     //营业外支出
                    $model->fair_value_change_loss,
                    $model->credit_impairment_loss,
                    $model->asset_impairment_loss,
                    $model->asset_disposal_loss,
                    $model->other_non_operating_expenses
                ], 2);
                $model->financial_asset_income = $value['financial_asset_income'] ?? NULL;
                $model->net_exposure_hedging_gain = $value['net_exposure_hedging_gain'] ?? NULL;
                $model->fair_value_change_gain = $value['fair_value_change_gain'] ?? NULL;
                $model->asset_disposal_gain = $value['asset_disposal_gain'] ?? NULL;
                $model->other_non_operating_income = $value['other_non_operating_income'] ?? NULL;
                $model->other_operating_income = $this->bcAddMultiple([
                    $model->financial_asset_income, //营业外收入
                    $model->net_exposure_hedging_gain,
                    $model->fair_value_change_gain,
                    $model->asset_disposal_gain,
                    $model->other_non_operating_income,
                ], 2);
                $model->oprating_profit = $this->bcSubMultiple([
                    $model->gross_profit,               //营业利润
                    $model->research_and_development_expense,
                    $model->general_and_administrative_expense,
                    $model->other_operating_expenses,
                ], 2);
                $model->oprating_profit = $this->bcAddMultiple([
                    $model->oprating_profit,            //营业利润
                    $model->other_operating_income,
                ], 2);
                $model->interest_income = $value['interest_income'] ?? NULL;
                $model->investment_income = $value['investment_income'] ?? NULL;
                $model->finance_income = $this->bcAddMultiple([$model->interest_income, $model->investment_income], 2); //财务收入
                $model->interest_expense = $value['interest_expense'] ?? NULL;
                $model->investment_loss = $value['investment_loss'] ?? NULL;
                $model->finance_expenses = $this->bcAddMultiple([$model->interest_expense, $model->investment_loss], 2); //财务费用
                $model->share_of_net_profit = $value['share_of_net_profit'] ?? NULL;
                //--- 税前利润 ---
                $model->profit_before_tax = $this->bcAddMultiple([
                    $model->oprating_profit,
                    $model->finance_income,
                    $model->share_of_net_profit,
                ], 2);
                $model->profit_before_tax = bcsub($model->profit_before_tax, $model->finance_expenses, 2);
                //--- 税前利润 ---
                $model->taxation = $value['taxation'] ?? NULL;
                $model->profit_for_the_period = bcsub($model->profit_before_tax, $model->taxation, 2);  //本期利润 
                // $model->profit_for_the_period = $this->bcAddMultiple([
                //     $model->profit_before_tax,               
                //     $model->taxation,
                // ], 2);
                $model->items_that_will_not_be_reclassified_to_profit_and_loss = $value['items_that_will_not_be_reclassified_to_profit_and_loss'] ?? NULL;
                $model->other_comprehensive_income = $value['other_comprehensive_income'] ?? NULL;
                $model->other_comprehensive_income_all = $this->bcAddMultiple([
                    $model->items_that_will_not_be_reclassified_to_profit_and_loss, //其他综合收益 合计
                    $model->other_comprehensive_income,
                ], 2);
                $model->gains_losses_on_cash_flow_hedges = $value['gains_losses_on_cash_flow_hedges'] ?? NULL;
                $model->currency_retranslation_gains = $value['currency_retranslation_gains'] ?? NULL;
                $model->other_comprehensive_expense = $this->bcAddMultiple([
                    $model->gains_losses_on_cash_flow_hedges, //本期其他综合（费用）/收入，税后净额
                    $model->currency_retranslation_gains,
                ], 2);
                $model->total_comprehensive_income_for_the_period = $this->bcAddMultiple([
                    $model->profit_for_the_period,               //本期利润 
                    $model->other_comprehensive_income_all,
                    $model->other_comprehensive_expense,
                ], 2);
                $model->save();
            }
            $lastTime = GspInvestigationComprehensiveIncomes::where([
                ['pid', '=', $pid],
            ])->select('updated_at')->orderBy('updated_at', 'desc')->first();
            if (empty($version['last_modified_time'])) $version = new GspInvestigationVersions();
            if ($version['last_modified_time'] < $lastTime['updated_at'] && !$request->temp) {
                $version->pid = $pid;
                $version->cna_gsp_investigation_config_id = $configId;
                $version->last_modified_time = $lastTime['updated_at'];
                $version->save();
            }
            DB::commit();
            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DefaultException();
        }
    }

    //财务报表 - 综合损益表/利润表 - PDF
    public function comprehensiveIncomeGeneratePdf(Request $request)
    {
        $res = $this->comprehensiveIncomeData($request);
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $columns = DB::select(
            "SELECT 
                COLUMN_NAME AS name
            FROM information_schema.COLUMNS 
            WHERE TABLE_NAME = ? 
            AND TABLE_SCHEMA = DATABASE() 
            AND COLUMN_NAME <> 'created_at'
            AND COLUMN_NAME <> 'updated_at'
            AND COLUMN_NAME <> 'id'
            AND COLUMN_NAME <> 'pid'
            ORDER BY 
                ORDINAL_POSITION",
            [(new GspInvestigationComprehensiveIncomes)->getTable()]
        );
        $columns = array_column($columns, 'name', 'name');
        $columns = array_map(function ($v) {
            return '\\';
        }, $columns);

        $content = [
            0 => $res[0] ?? $columns,
            1 => $res[1] ?? $columns,
        ];
        $version = GspInvestigationVersions::where([
            ['pid', '=', $pid],
            ['cna_gsp_investigation_config_id', '=', 30],
        ])->first();
        $formModify         = date('Y-m-d', strtotime($version['last_modified_time']));
        $lastModify         = date('Y-m-d H:i:s', strtotime($version['last_modified_time']));
        // 利润表
        $fileName   = Lang::get("gsp.cna_gsp_investigation_comprehensive_incomes.table_name") . '.pdf';
        $title      = Lang::get("gsp.cna_gsp_investigation_comprehensive_incomes.table_name");
        $column     = '';
        $token      = (new PdfTableService($title, $column,  $content, '', '', '',  '', '', 0, $fileName, $formModify, $lastModify))->incomeStatement();

        return responseSuccess(['token' => $token]);
    }


    //财务报表 - 现金流量表
    public function cashFlowData($request)
    {
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $res = GspInvestigationCashFlow::where([
            ['pid', '=', $pid]
        ])->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        return $res;
    }
    //财务报表 - 现金流量表
    public function cashFlow(Request $request)
    {
        $res = $this->cashFlowData($request);
        return responseSuccess($res);
    }
    //财务报表 - 现金流量表 - 修改
    public function saveCashFlow(Request $request)
    {
        $required   = $request->temp ? 'sometimes' : 'required';
        $validated = $request->validate([
            'data' => [$required],
        ]);
        $input      = $request->input();
        if (count($input['data']) != 2 && !$request->temp) throw new DefaultException("需要两个年份");
        foreach ($input['data'] as $key => $value) {
            foreach ($value as $k => $v) {
                $value[$k] = $this->removeControlChar($v);
            }
            $input['data'][$key] = $value;
        }
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        //判断是否可以提交尽调报告，或填写清单
        if (!$this->isWrite($pid, !empty($user))) return responseFail(__('gsp.submit_repeatedly'));
        try {
            // 开启事务
            DB::beginTransaction();
            $configId   = 32;
            $version    = GspInvestigationVersions::where([
                ['pid', '=', $pid],
                ['cna_gsp_investigation_config_id', '=', $configId],
            ])->first();
            //删除不存的
            $inputYear = array_column($validated['data'], 'year');
            $existYear = GspInvestigationCashFlow::where([
                ['pid', '=', $pid]
            ])->pluck('year', 'id')->toArray();
            $del = array_diff($existYear, $inputYear);
            GspInvestigationCashFlow::whereIn('id', array_keys($del))->delete();
            foreach ($validated['data'] as $key => $value) {
                $model = GspInvestigationCashFlow::where([
                    ['pid', '=', $pid],
                    ['year', '=', $value['year']],
                ])->first();
                if (empty($model)) $model = new GspInvestigationCashFlow();
                $model->pid = $pid;
                $model->year = $value['year'];
                $model->cash_received_from_the_sale = $value['cash_received_from_the_sale'] ?? NULL;
                $model->tax_refunds_received = $value['tax_refunds_received'] ?? NULL;
                $model->other_cash_received_in_connection_with_operating_activities = $value['other_cash_received_in_connection_with_operating_activities'] ?? NULL;
                $model->subtotal_cash_inflows_from_operating_activities = $this->bcAddMultiple([
                    $model->cash_received_from_the_sale, //经营活动现金流入小计
                    $model->tax_refunds_received,
                    $model->other_cash_received_in_connection_with_operating_activities,
                ], 2);
                $model->cash_for_the_purchase = $value['cash_for_the_purchase'] ?? NULL;
                $model->cash_paid_to_and_on_behalf = $value['cash_paid_to_and_on_behalf'] ?? NULL;
                $model->taxes_and_fees_paid = $value['taxes_and_fees_paid'] ?? NULL;
                $model->payment_of_other_cash_related_to_operating_activities = $value['payment_of_other_cash_related_to_operating_activities'] ?? NULL;
                $model->subtotal_cash_otflows_from_operating_activities = $this->bcAddMultiple([
                    $model->cash_for_the_purchase,                          //经营活动现金流出小计
                    $model->cash_paid_to_and_on_behalf,
                    $model->taxes_and_fees_paid,
                    $model->payment_of_other_cash_related_to_operating_activities,
                ], 2);
                $model->proceeds_from_sale_of_plant = $value['proceeds_from_sale_of_plant'] ?? NULL;
                $model->proceeds_from_sale_of_investments = $value['proceeds_from_sale_of_investments'] ?? NULL;
                $model->proceeds_from_sale_of_goodwill = $value['proceeds_from_sale_of_goodwill'] ?? NULL;
                $model->net_cash_received_from_sales_of_subsidiaries = $value['net_cash_received_from_sales_of_subsidiaries'] ?? NULL;
                $model->other_proceeds_from_investments = $value['other_proceeds_from_investments'] ?? NULL;
                $model->operating_incomes_from_investments = $value['operating_incomes_from_investments'] ?? NULL;
                $model->dividends_received = $value['dividends_received'] ?? NULL;
                $model->interest_received = $value['interest_received'] ?? NULL;
                $model->rent_on_property_received = $value['rent_on_property_received'] ?? NULL;
                $model->purchase_of_plant = $value['purchase_of_plant'] ?? NULL;
                $model->purchase_of_investments = $value['purchase_of_investments'] ?? NULL;
                $model->purchase_of_joint_ventures_and_associates = $value['purchase_of_joint_ventures_and_associates'] ?? NULL;
                $model->purchase_of_intangible_assets = $value['purchase_of_intangible_assets'] ?? NULL;
                $model->other_payments_for_investing_activities = $value['other_payments_for_investing_activities'] ?? NULL;
                $model->net_cash_from_investing_activities = $this->bcAddMultiple([
                    $model->proceeds_from_sale_of_plant, //净现金流入/(流出）（用于/来自投资活动）
                    $model->proceeds_from_sale_of_investments,
                    $model->proceeds_from_sale_of_goodwill,
                    $model->net_cash_received_from_sales_of_subsidiaries,
                    $model->other_proceeds_from_investments,
                    $model->operating_incomes_from_investments,
                    $model->dividends_received,
                    $model->interest_received,
                    $model->rent_on_property_received,
                    $model->purchase_of_plant,
                    $model->purchase_of_investments,
                    $model->purchase_of_joint_ventures_and_associates,
                    $model->purchase_of_intangible_assets,
                    $model->other_payments_for_investing_activities,
                ], 2);
                $model->proceeds_from_issue_of_share_capital_and_borrowings = $value['proceeds_from_issue_of_share_capital_and_borrowings'] ?? NULL;
                $model->proceeds_from_issue_of_ordinary_shares_capital = $value['proceeds_from_issue_of_ordinary_shares_capital'] ?? NULL;
                $model->proceeds_from_issue_of_preference_shares_capital = $value['proceeds_from_issue_of_preference_shares_capital'] ?? NULL;
                $model->proceeds_from_loans_and_borrowings_etc = $value['proceeds_from_loans_and_borrowings_etc'] ?? NULL;
                $model->buy_back_of_equity_shares = $value['buy_back_of_equity_shares'] ?? NULL;
                $model->redemption_of_preference_shares = $value['redemption_of_preference_shares'] ?? NULL;
                $model->repayment_of_loans_and_borrowings = $value['repayment_of_loans_and_borrowings'] ?? NULL;
                $model->redemption_of_debentures = $value['redemption_of_debentures'] ?? NULL;
                $model->dividends_paid_on_equity_shares = $value['dividends_paid_on_equity_shares'] ?? NULL;
                $model->dividend_on_preference_shares = $value['dividend_on_preference_shares'] ?? NULL;
                $model->repayment_of_obligations_under_leases = $value['repayment_of_obligations_under_leases'] ?? NULL;
                $model->other_payments_for_financing_activities = $value['other_payments_for_financing_activities'] ?? NULL;
                $model->from_financing_activities = $value['from_financing_activities'] ?? NULL;
                $model->net_increase = $value['net_increase'] ?? NULL;
                $model->cash_equivalents_at_the_beginning_of_the_period = $value['cash_equivalents_at_the_beginning_of_the_period'] ?? NULL;
                $model->effect_of_foreign_exchange_rate_changes = $value['effect_of_foreign_exchange_rate_changes'] ?? NULL;
                $model->cash_equivalents_at_the_end_of_the_year = $value['cash_equivalents_at_the_end_of_the_year'] ?? NULL;
                $model->save();
            }
            $lastTime = GspInvestigationCashFlow::where([
                ['pid', '=', $pid],
            ])->select('updated_at')->orderBy('updated_at', 'desc')->first();
            if (empty($version['last_modified_time'])) $version = new GspInvestigationVersions();
            if ($version['last_modified_time'] < $lastTime['updated_at'] && !$request->temp) {
                $version->pid = $pid;
                $version->cna_gsp_investigation_config_id = $configId;
                $version->last_modified_time = $lastTime['updated_at'];
                $version->save();
            }
            DB::commit();
            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DefaultException();
        }
    }

    //财务报表 - 现金流量表 - PDF
    public function cashFlowGeneratePdf(Request $request)
    {
        $res = $this->cashFlowData($request);
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $columns = DB::select(
            "SELECT 
                COLUMN_NAME AS name
            FROM information_schema.COLUMNS 
            WHERE TABLE_NAME = ? 
            AND TABLE_SCHEMA = DATABASE() 
            AND COLUMN_NAME <> 'created_at'
            AND COLUMN_NAME <> 'updated_at'
            AND COLUMN_NAME <> 'id'
            AND COLUMN_NAME <> 'pid'
            ORDER BY 
                ORDINAL_POSITION",
            [(new GspInvestigationCashFlow)->getTable()]
        );
        $columns = array_column($columns, 'name', 'name');
        $columns = array_map(function ($v) {
            return '\\';
        }, $columns);

        $content = [
            0 => $res[0] ?? $columns,
            1 => $res[1] ?? $columns,
        ];
        $version = GspInvestigationVersions::where([
            ['pid', '=', $pid],
            ['cna_gsp_investigation_config_id', '=', 32],
        ])->first();
        $formModify         = date('Y-m-d', strtotime($version['last_modified_time']));
        $lastModify         = date('Y-m-d H:i:s', strtotime($version['last_modified_time']));
        // 利润表
        $fileName   = Lang::get("gsp.cna_gsp_investigation_cash_flow.table_name") . '.pdf';
        $title      = Lang::get("gsp.cna_gsp_investigation_cash_flow.table_name");
        $column     = '';
        $token      = (new PdfTableService($title, $column,  $content, '', '', '',  '', '', 0, $fileName, $formModify, $lastModify))->cashFlow();

        return responseSuccess(['token' => $token]);
    }

    //财务报表 - 资产负债表 (工作版本）
    public function balanceSheetData($request)
    {
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $res = GspInvestigationBalanceSheet::where([
            ['pid', '=', $pid]
        ])->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        return $res;
    }
    //财务报表 - 资产负债表 (工作版本）
    public function balanceSheet(Request $request)
    {
        $res = $this->balanceSheetData($request);
        return responseSuccess($res);
    }
    //财务报表 - 资产负债表 (工作版本） - 修改
    public function saveBalanceSheet(Request $request)
    {
        $required   = $request->temp ? 'sometimes' : 'required';
        $validated = $request->validate([
            'data' => [$required],
        ]);
        $input      = $request->input();
        if (count($input['data']) != 2 && !$request->temp) throw new DefaultException("需要两个年份");
        foreach ($input['data'] as $key => $value) {
            foreach ($value as $k => $v) {
                $value[$k] = $this->removeControlChar($v);
            }
            $input['data'][$key] = $value;
        }
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        //判断是否可以提交尽调报告，或填写清单
        if (!$this->isWrite($pid, !empty($user))) return responseFail(__('gsp.submit_repeatedly'));
        try {
            // 开启事务
            DB::beginTransaction();
            $configId   = 31;
            $version    = GspInvestigationVersions::where([
                ['pid', '=', $pid],
                ['cna_gsp_investigation_config_id', '=', $configId],
            ])->first();
            //删除不存的
            $inputYear = array_column($validated['data'], 'year');
            $existYear = GspInvestigationBalanceSheet::where([
                ['pid', '=', $pid]
            ])->pluck('year', 'id')->toArray();
            $del = array_diff($existYear, $inputYear);
            GspInvestigationBalanceSheet::whereIn('id', array_keys($del))->delete();
            foreach ($input['data'] as $key => $value) {
                $model = GspInvestigationBalanceSheet::where([
                    ['pid', '=', $pid],
                    ['year', '=', $value['year']],
                ])->first();
                if (empty($model)) $model = new GspInvestigationBalanceSheet();
                $model->pid = $pid;
                $model->year = $value['year'];
                $model->fixed_assets = $value['fixed_assets'] ?? NULL;
                $model->construction_in_progress = $value['construction_in_progress'] ?? NULL;
                $model->property_plant_and_equipment = $this->bcAddMultiple([
                    $model->fixed_assets, //固定资产和在建工程的合计
                    $model->construction_in_progress,
                ], 2);
                $model->goodwill = $value['goodwill'] ?? NULL;
                $model->intangible_assets = $value['intangible_assets'] ?? NULL;
                $model->right_of_use_assets = $value['right_of_use_assets'] ?? NULL;
                $model->investments_properties = $value['investments_properties'] ?? NULL;
                $model->debt_investments = $value['debt_investments'] ?? NULL;
                $model->available_for_sale_financial_assets = $value['available_for_sale_financial_assets'] ?? NULL;
                $model->other_debt_investments = $value['other_debt_investments'] ?? NULL;
                $model->held_to_maturity_investments = $value['held_to_maturity_investments'] ?? NULL;
                $model->long_term_equity_investments = $value['long_term_equity_investments'] ?? NULL;
                $model->other_equity_instrument_investments = $value['other_equity_instrument_investments'] ?? NULL;
                $model->other_non_current_financial_assets = $value['other_non_current_financial_assets'] ?? NULL;
                $model->other_investments_all = $this->bcAddMultiple([
                    $model->debt_investments,     //债权投资 ~ 其他非流动金融资产合计
                    $model->available_for_sale_financial_assets,
                    $model->other_debt_investments,
                    $model->held_to_maturity_investments,
                    $model->long_term_equity_investments,
                    $model->other_equity_instrument_investments,
                    $model->other_non_current_financial_assets,
                ], 2);
                $model->long_term_receivables = $value['long_term_receivables'] ?? NULL;
                $model->deferred_tax_assets = $value['deferred_tax_assets'] ?? NULL;
                $model->productive_biological_assets = $value['productive_biological_assets'] ?? NULL;
                $model->oil_and_gas_assets = $value['oil_and_gas_assets'] ?? NULL;
                $model->development_expenditure = $value['development_expenditure'] ?? NULL;
                $model->long_term_prepaid_expenses = $value['long_term_prepaid_expenses'] ?? NULL;
                $model->other_non_current_assets = $value['other_non_current_assets'] ?? NULL;
                $model->other_non_current_assets_all = $this->bcAddMultiple([
                    $model->productive_biological_assets,  //生产性生物资产合计 ~ 其他非流动资产
                    $model->oil_and_gas_assets,
                    $model->development_expenditure,
                    $model->long_term_prepaid_expenses,
                    $model->other_non_current_assets,
                ], 2);
                $model->total_non_current_assets = $this->bcAddMultiple([
                    $model->property_plant_and_equipment,     //非流动资产 （共）
                    $model->goodwill,
                    $model->intangible_assets,
                    $model->right_of_use_assets,
                    $model->investments_properties,
                    $model->other_investments_all,
                    $model->long_term_receivables,
                    $model->deferred_tax_assets,
                    $model->other_non_current_assets_all,
                ], 2);
                $model->inventories = $value['inventories'] ?? NULL;
                $model->notes_receivable = $value['notes_receivable'] ?? NULL;
                $model->accounts_receivable = $value['accounts_receivable'] ?? NULL;
                $model->advance_receipts = $value['advance_receipts'] ?? NULL;
                $model->other_receivables = $value['other_receivables'] ?? NULL;
                $model->trade_and_other_receivables = $this->bcAddMultiple([
                    $model->notes_receivable,     //贸易及其他应收款项
                    $model->accounts_receivable,
                    $model->advance_receipts,
                    $model->other_receivables,
                ], 2);
                $model->trading_financial_assets = $value['trading_financial_assets'] ?? NULL;
                $model->financial_assets_measured = $value['financial_assets_measured'] ?? NULL;
                $model->derivative_financial_assets = $value['derivative_financial_assets'] ?? NULL;
                $model->other_investments = $this->bcAddMultiple([
                    $model->trading_financial_assets,     //其它投资 （共）
                    $model->financial_assets_measured,
                    $model->derivative_financial_assets,
                ], 2);
                $model->contract_assets = $value['contract_assets'] ?? NULL;
                $model->held_for_sale_assets = $value['held_for_sale_assets'] ?? NULL;
                $model->non_current_assets_due_within_one_year = $value['non_current_assets_due_within_one_year'] ?? NULL;
                $model->other_current_assets = $value['other_current_assets'] ?? NULL;
                $model->receivables_financing = $value['receivables_financing'] ?? NULL;
                $model->other_current_assets_all = $this->bcAddMultiple([
                    $model->contract_assets,     //其它流动资产 （共）
                    $model->held_for_sale_assets,
                    $model->non_current_assets_due_within_one_year,
                    $model->other_current_assets,
                    $model->receivables_financing,
                ], 2);
                $model->income_tax_receivables = $value['income_tax_receivables'] ?? NULL;
                $model->cash_and_cash_equivalents = $value['cash_and_cash_equivalents'] ?? NULL;
                $model->total_current_assets = $this->bcAddMultiple([
                    $model->inventories,     //流动资产 (共）
                    $model->trade_and_other_receivables,
                    $model->other_investments,
                    $model->other_current_assets_all,
                    $model->income_tax_receivables,
                    $model->cash_and_cash_equivalents,
                ], 2);
                $model->total_assets = $this->bcAddMultiple([
                    $model->total_current_assets,     //资产（共）
                    $model->total_non_current_assets,
                ], 2);
                $model->short_term_borrowings = $value['short_term_borrowings'] ?? NULL;
                $model->trading_financial_liabilities = $value['trading_financial_liabilities'] ?? NULL;
                $model->derivative_financial_liabilities = $value['derivative_financial_liabilities'] ?? NULL;
                $model->interest_bearing_loans_and_borrowings_short = $this->bcAddMultiple([
                    $model->short_term_borrowings,     //计息贷款及借款
                    $model->trading_financial_liabilities,
                    $model->derivative_financial_liabilities,
                ], 2);
                $model->lease_liabilities_short = $value['lease_liabilities_short'] ?? NULL;
                $model->accounts_payable = $value['accounts_payable'] ?? NULL;
                $model->notes_payable = $value['notes_payable'] ?? NULL;

                $model->prepayments = $value['prepayments'] ?? NULL;
                $model->contract_liabilities = $value['contract_liabilities'] ?? NULL;
                $model->employee_benefits_payable = $value['employee_benefits_payable'] ?? NULL;
                $model->other_payables = $value['other_payables'] ?? NULL;
                $model->trade_and_other_payables = $this->bcAddMultiple([
                    $model->accounts_payable,     //贸易及其他应付款
                    $model->notes_payable,
                    $model->prepayments,
                    $model->contract_liabilities,
                    $model->employee_benefits_payable,
                    $model->other_payables,
                ], 2);
                $model->provisions_short = $value['provisions_short'] ?? NULL;
                $model->income_tax_payables_short = $value['income_tax_payables_short'] ?? NULL;
                $model->held_for_sale_liabilities = $value['held_for_sale_liabilities'] ?? NULL;
                $model->non_current_liabilities_due_within_one_year = $value['non_current_liabilities_due_within_one_year'] ?? NULL;
                $model->other_current_liabilities = $value['other_current_liabilities'] ?? NULL;
                $model->other_current_liabilities_all = $this->bcAddMultiple([
                    $model->held_for_sale_liabilities,     //其他流动负债 (共）
                    $model->non_current_liabilities_due_within_one_year,
                    $model->other_current_liabilities,
                ], 2);
                $model->total_current_liabilities = $this->bcAddMultiple([
                    $model->interest_bearing_loans_and_borrowings_short,     //流动负债 （共）
                    $model->lease_liabilities_short,
                    $model->trade_and_other_payables,
                    $model->provisions_short,
                    $model->income_tax_payables_short,
                    $model->other_current_liabilities_all,
                ], 2);
                $model->long_term_borrowings = $value['long_term_borrowings'] ?? NULL;
                $model->bonds_payable = $value['bonds_payable'] ?? NULL;
                $model->interest_bearing_loans_and_borrowings_long = $this->bcAddMultiple([
                    $model->long_term_borrowings,     //计息贷款及借款
                    $model->bonds_payable,
                ], 2);
                $model->lease_liabilities_long = $value['lease_liabilities_long'] ?? NULL;
                $model->deferred_tax_liabilities = $value['deferred_tax_liabilities'] ?? NULL;
                $model->provisions_long = $value['provisions_long'] ?? NULL;
                $model->income_tax_payables_long = $value['income_tax_payables_long'] ?? NULL;
                $model->long_term_payables = $value['long_term_payables'] ?? NULL;
                $model->long_term_employee_benefits_payable = $value['long_term_employee_benefits_payable'] ?? NULL;
                $model->provision_liabilities = $value['provision_liabilities'] ?? NULL;
                $model->other_paybales = $this->bcAddMultiple([
                    $model->long_term_payables,             //其它应付账款 (共）
                    $model->long_term_employee_benefits_payable,
                    $model->provision_liabilities,
                ], 2);
                $model->preferred_stock = $value['preferred_stock'] ?? NULL;
                $model->perpetual_bonds = $value['perpetual_bonds'] ?? NULL;
                $model->deferred_revenue = $value['deferred_revenue'] ?? NULL;
                $model->other_non_current_liabilities = $value['other_non_current_liabilities'] ?? NULL;
                $model->other_non_current_liabilities_all = $this->bcAddMultiple([
                    $model->preferred_stock,             //其他非流动负债 （共）
                    $model->perpetual_bonds,
                    $model->deferred_revenue,
                    $model->other_non_current_liabilities,
                ], 2);
                $model->total_non_current_liabilities = $this->bcAddMultiple([
                    $model->interest_bearing_loans_and_borrowings_long,             //非流动负债 （共）
                    $model->lease_liabilities_long,
                    $model->deferred_tax_liabilities,
                    $model->provisions_long,
                    $model->income_tax_payables_long,
                    $model->other_paybales,
                    $model->other_non_current_liabilities_all,
                ], 2);
                $model->total_liabilities = $this->bcAddMultiple([
                    $model->total_non_current_liabilities,             //负债 （共）
                    $model->total_current_liabilities,
                ], 2);
                $model->reserves_attributable_to_equity_holders_of_the_company = $value['reserves_attributable_to_equity_holders_of_the_company'] ?? NULL;
                $model->ordinary_share_capital = $value['ordinary_share_capital'] ?? NULL;
                // $model->less_treasury_stocks = $value['less_treasury_stocks'] ?? NULL;
                $model->preferred_share_holders = $value['preferred_share_holders'] ?? NULL;
                $model->share_premium = $value['share_premium'] ?? NULL;
                $model->share_capital = $this->bcAddMultiple([  //股本
                    $model->ordinary_share_capital,
                    $model->preferred_share_holders,
                    $model->share_premium,
                ], 2);
                $model->other_comprehensive_income = $value['other_comprehensive_income'] ?? NULL;
                $model->special_reserves = $value['special_reserves'] ?? NULL;
                $model->surplus_reserves = $value['surplus_reserves'] ?? NULL;
                $model->other_reserves = $this->bcAddMultiple([
                    $model->other_comprehensive_income,             //其他储备
                    $model->special_reserves,
                    $model->surplus_reserves,
                ], 2);
                $model->retained_eranings = $value['retained_eranings'] ?? NULL;
                $model->total_equity = $this->bcAddMultiple([
                    $model->ordinary_share_capital,             //股本加利润（年利润加累计利润）
                    // $model->less_treasury_stocks,
                    $model->preferred_share_holders,
                    $model->share_premium,
                    $model->other_reserves,
                    $model->retained_eranings,
                ], 2);
                $model->total_equity_and_liabilities = $this->bcAddMultiple([
                    $model->total_liabilities,             //股本加利润（年利润加累计利润）加负债（共）
                    $model->total_equity,
                ], 2);
                //备注：检查利润和负债是否等于资产
                $model->check = bcsub($model->total_assets, $model->total_equity_and_liabilities, 2);
                $model->non_controlling_interest = $value['non_controlling_interest'] ?? NULL;
                $model->save();
            }
            $lastTime = GspInvestigationBalanceSheet::where([
                ['pid', '=', $pid],
            ])->select('updated_at')->orderBy('updated_at', 'desc')->first();
            if (empty($version['last_modified_time'])) $version = new GspInvestigationVersions();
            if ($version['last_modified_time'] < $lastTime['updated_at'] && !$request->temp) {
                $version->pid = $pid;
                $version->cna_gsp_investigation_config_id = $configId;
                $version->last_modified_time = $lastTime['updated_at'];
                $version->save();
            }
            DB::commit();
            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DefaultException();
        }
    }

    //财务报表 - 资产负债表 - PDF
    public function balanceSheetGeneratePdf(Request $request)
    {
        $res = $this->balanceSheetData($request);
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $columns = DB::select(
            "SELECT 
                COLUMN_NAME AS name
            FROM information_schema.COLUMNS 
            WHERE TABLE_NAME = ? 
            AND TABLE_SCHEMA = DATABASE() 
            AND COLUMN_NAME <> 'created_at'
            AND COLUMN_NAME <> 'updated_at'
            AND COLUMN_NAME <> 'id'
            AND COLUMN_NAME <> 'pid'
            ORDER BY 
                ORDINAL_POSITION",
            [(new GspInvestigationBalanceSheet)->getTable()]
        );
        $columns = array_column($columns, 'name', 'name');
        $columns = array_map(function ($v) {
            return '\\';
        }, $columns);

        $content = [
            0 => $res[0] ?? $columns,
            1 => $res[1] ?? $columns,
        ];
        $version = GspInvestigationVersions::where([
            ['pid', '=', $pid],
            ['cna_gsp_investigation_config_id', '=', 31],
        ])->first();

        $formModify         = date('Y-m-d', strtotime($version['last_modified_time']));
        $lastModify         = date('Y-m-d H:i:s', strtotime($version['last_modified_time']));
        // 利润表
        $fileName   = Lang::get("gsp.cna_gsp_investigation_balance_sheet.table_name") . '.pdf';
        $title      = Lang::get("gsp.cna_gsp_investigation_balance_sheet.table_name");
        $column     = '';
        $token      = (new PdfTableService($title, $column,  $content, '', '', '',  '', '', 0, $fileName, $formModify, $lastModify))->balanceSheet();

        return responseSuccess(['token' => $token]);
    }

    /**
     * 提交尽调报告
     * @param Request $request
     * @return void
     */
    public function applyReport(Request $request)
    {
        $user = $request->attributes->get('user');
        // 企业信息
        $info = $this->getCurrentGsp($user);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }
        $gspId = $info['id'];
        //判断是否可以提交尽调报告，或填写清单
        if (!$this->isWrite($gspId)) return responseFail(__('gsp.submit_repeatedly'));

        // 判断是否必填都填了
        $configTableName = (new GspInvestigationConfig)->getTable();
        $config = GspInvestigationConfig::leftJoin((new CompanyGspReport)->getTable() . ' as b', function ($join) use ($configTableName, $gspId) {
            $join->on("{$configTableName}.id", '=', 'b.file_id')->where([
                ['b.gsp_id', '=', $gspId],
                ['b.status', '<>', CompanyGspReport::STATUS_FILE_DISCARD],
                ['b.status', '<>', CompanyGspReport::STATUS_FILE_FAIL],
            ]);
        })->where([
            ['allow_upload_file', '=', 1],
            ['upload_required', '=', 1],
            ['partner', '=', 1],
        ])->select("{$configTableName}.*")->whereNull('b.file_id')->get()->toArray();
        if ($config) return responseFail(__('please upload complete file') . '：' . ($config[0]['category_zh_cn'] ?? ''));
        $config2 = GspInvestigationConfig::leftJoin((new GspInvestigationVersions)->getTable() . ' as b', function ($join) use ($configTableName, $gspId) {
            $join->on("{$configTableName}.id", '=', 'b.cna_gsp_investigation_config_id')->where('b.pid', $gspId);
        })->where([
            ['type', '>', 0],
            ['allow_online_form', '=', 1],
            ['partner', '=', 1],
        ])->select("{$configTableName}.*")->whereNull('b.pid')->get()->toArray();
        if ($config2) return responseFail('请填写完整' . '：' . ($config2[0]['category_zh_cn'] ?? ''));

        try {
            // 开启事务
            DB::beginTransaction();
            //把上传文件审核失败改为未审核
            CompanyGspReport::where([
                ['gsp_id', '=', $gspId],
                ['status', '=', CompanyGspReport::STATUS_FILE_FAIL],
            ])->update([
                'status' => CompanyGspReport::STATUS_FILE_DISCARD
            ]);
            // step3 更新提交状态
            CompanyGsp::where('id', $gspId)->update([
                'status' => CompanyGsp::STATUS_POST_REPORT,
                'report_post_date' => date('Y-m-d H:i:s')
            ]);
            // 提交
            DB::commit();
            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail($e->getMessage());
        }
    }

    //判断是否可以提交尽调报告，或填写清单
    protected function isWrite($gspId, $isPartner = true)
    {
        //判断是否可以提交尽调报告
        $GspReportStatus = CompanyGsp::where('id', $gspId)->value('status');
        if (!in_array($GspReportStatus, [CompanyGsp::STATUS_CONTRACT_SUCCESS, CompanyGsp::STATUS_REPORT_FAIL, CompanyGsp::STATUS_COMPANY_REPORT_FAIL])) {
            return false;
        }
        if (!$isPartner) { //只能合伙人提交
            return false;
        }
        return true;
    }

    //AI总结
    protected function sendPostAiAdvisory($prompt)
    {
        $content    = "";
        $returnArr = [];
        // // 要请求的URL
        $url = "https://api.testai.corporate-advisory.cn/train/api/v1/ai/chat/completions";
        // // 需要POST的数据
        $data = array(
            "prompt" => $prompt,
            "roleId" => "42cfd529-f922-4936-ae6f-5af52149985c",
            "stream" => false,
        );
        // 初始化cURL会话
        $ch = curl_init($url);
        // 设置cURL传输选项
        curl_setopt($ch, CURLOPT_POST, 1); // 启用POST请求
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data); // POST的数据
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // 返回结果而非直接输出
        // 执行cURL会话并获取返回结果
        $response = curl_exec($ch);
        // 检查是否有错误发生
        if (curl_errno($ch)) {
            $content = "";
        }
        $arr = json_decode($response, true);
        if (!empty($arr['data']) && !empty($arr['data']['content']) && !empty($arr['data']['content'][0])) {
            $content = [$arr['data']['content']];
        }
        $returnArr['content'] = $content;
        // 关闭cURL资源
        curl_close($ch);
        // 输出响应结果
        return $returnArr;
    }

    protected function bcAddMultiple(array $numbers, int $scale = 2): string
    {
        $result = '0';
        foreach ($numbers as $number) {
            $result = bcadd($result, $number, $scale);
        }
        return $result;
    }

    protected function bcSubMultiple(array $numbers, int $scale = 2): string
    {
        $result = '0';
        foreach ($numbers as $number) {
            if ($result == '0') {
                $result = $number;
            } else {
                $result = bcsub($result, $number, $scale);
            }
        }
        return $result;
    }


    //组装树结构
    protected static function buildTree($original)
    {
        $refs = array();
        // 初始化所有节点，并创建引用数组
        foreach ($original as $item) {
            $id = $item['id'];
            $refs[$id] = array_merge($item, array('child' => array()));
        }
        // 建立父子关系
        foreach ($original as $item) {
            $id = $item['id'];
            $pid = $item['pid'];
            if ($pid != '0' && isset($refs[$pid])) {
                // 将当前节点的引用添加到父节点的child中
                $refs[$pid]['child'][] = &$refs[$id];
            }
        }
        // 收集根节点
        $tree = array();
        foreach ($original as $item) {
            if ($item['pid'] == '0') {
                $tree[] = &$refs[$item['id']];
            }
        }
        return $tree;
    }

    // 当前正在进行中流程
    protected function getCurrentGsp($user, $gsp_id = '')
    {
        $record = [];
        if ($user) {
            $record = CompanyGsp::where('profile_id', $user['profileID'])->where('status', '!=', CompanyGsp::STATUS_ACTIVE)->first();
        } else if ($gsp_id) {
            $record = CompanyGsp::where('id', $gsp_id)->first();
        }

        return $record;
    }

    //移除控制字符
    protected function removeControlChar($input)
    {
        if ($input == NULL) return $input;
        return preg_replace('/\t/u', '', $input);
    }

    //获取数据表的默认值
    protected function getTableDefaultValue($tableName)
    {
        //查找默认值
        $databaseName = DB::connection()->getDatabaseName();
        $default  = DB::select("
                        SELECT COLUMN_NAME, COLUMN_DEFAULT 
                        FROM INFORMATION_SCHEMA.COLUMNS 
                        WHERE TABLE_SCHEMA = ?
                          AND TABLE_NAME = ?
                          AND COLUMN_NAME != 'id' 
                          AND COLUMN_NAME != 'pid' 
                          AND COLUMN_NAME != 'created_at' 
                          AND COLUMN_NAME != 'updated_at' 
                    ", [$databaseName, $tableName]);
        return $default = array_column($default, 'COLUMN_DEFAULT', 'COLUMN_NAME');
    }

    //售后服务及保证数据查询
    public static function afterSalesDataSearch($pid)
    {
        $main = GspInvestigationMain::select(
            'after_sales_standard',
            'after_sales_mode',
            'after_sales_response',
            'after_sales_coverage',
            'after_sales_standard_en',
            'after_sales_mode_en',
            'after_sales_response_en',
            'after_sales_coverage_en',
            'pid',
        )->where([
            ['pid', '=', $pid],
        ])->first();
        return $main;
    }

    //公司工商情况数据查询
    public static function companyBusinessesDataSearch($pid)
    {
        $main = GspInvestigationMain::select(
            'company_name',
            'used_name',
            'credit_code',
            'registered_country',
            'registered_region',
            'establishment_date',
            'company_type',
            'register_capital',
            'register_capital_units',
            'paid_capital',
            'paid_capital_units',
            'registered_address',
            'operating_address',
            'business_scope',
            'annual_inspection',
            'operating_period',
            'company_name_en',
            'used_name_en',
            'credit_code_en',
            'registered_country_en',
            'registered_region_en',
            'establishment_date_en',
            'company_type_en',
            'registered_address_en',
            'operating_address_en',
            'business_scope_en',
            'annual_inspection_en',
            'operating_period_en',
            'pid',
        )->where([
            ['pid', '=', $pid],
        ])->first();
        return $main;
    }

    //员工人数数据查询
    public static function employeesDataSearch($pid)
    {
        $main = GspInvestigationMain::select(
            'employee_quantity_statistical_date',
            'employee_quantity_total',
            'employee_quantity_social_insurance',
            'employee_quantity_statistical_date_en',
            'employee_quantity_total_en',
            'employee_quantity_social_insurance_en',
            'pid',
        )->where([
            ['pid', '=', $pid],
        ])->first();
        return $main;
    }

    //公司简介其他部分数据查询
    public static function companyProfileOtherDataSearch($pid)
    {
        $main = GspInvestigationMain::select(
            'company_overview',
            'business_overview',
            'customers_performance',
            'summary_of_company_profile',
            'company_overview_en',
            'business_overview_en',
            'customers_performance_en',
            'summary_of_company_profile_en',
            'pid',
        )->where([['pid', '=', $pid],])->first();
        return $main;
    }

    //公司简介数据查询
    public static function companyProfileDataSearch($pid)
    {
        $main = GspInvestigationMain::select(
            'establishment_date',
            'group_name',
            'main_producer_nation',
            'main_producer_region',
            // 'main_producer',
            'industry_name',
            'main_industry_direction',
            'main_product',
            'pid',
            // 'company_overview',
            // 'business_overview',
            // 'customers_performance',
            'establishment_date_en',
            'group_name_en',
            'main_producer_nation_en',
            'main_producer_region_en',
            'industry_name_en',
            'main_industry_direction_en',
            'main_product_en',
        )->where([['pid', '=', $pid],])->first();
        return $main;
    }

    //概况摘要数据查询
    public static function summaryDataSearch($pid, $getExtra = true)
    {
        $main = GspInvestigationMain::select(
            'pid',
            'no_cna_gsp_investigation_commendatory_letters',
            'no_cna_gsp_investigation_honors',
            'no_cna_gsp_investigation_qualifications',
            'no_cna_gsp_investigation_intellectual_properties',
            'no_cna_gsp_investigation_significant_contracts',
            'no_cna_gsp_investigation_litigation_arbitration_cases',
            'no_cna_gsp_investigation_administrative_penalty_records',
            'no_cna_gsp_investigation_dishonesty_records',
            // --------- 1. 公司概述 ---------
            'establishment_date',               //公司概述 - 成立日期
            'establishment_date_en',            //公司概述 - 成立日期
            'group_name',                       //公司概述 - 集团名称
            'group_name_en',                    //公司概述 - 集团名称
            'group_headquarters_address',       //公司概述 - 集团总部地址
            'group_headquarters_address_en',    //公司概述 - 集团总部地址
            'register_capital',             //公司概述 - 注册资本
            'register_capital_units',       //公司概述 - 注册资本货币单位
            'paid_capital',                 //公司概述 - 实收资本
            'paid_capital_units',           //公司概述 - 实收资本货币单位
            'company_overview',             //公司概述
            'company_overview_en',             //公司概述
            // --------- 1. 公司概述 ---------
            // --------- 2. 业务概述 ---------
            'business_overview_core_product',               //业务概述 - 产品名称
            'business_overview_core_product_en',            //业务概述 - 产品名称
            'business_overview_quality_control',            //业务概述 - 生产过程及质量控制
            'business_overview_quality_control_en',         //业务概述 - 生产过程及质量控制
            'business_overview_after_sales',                //业务概述 - 售后服务
            'business_overview_after_sales_en',             //业务概述 - 售后服务
            'business_overview',                       //业务概述
            'business_overview_en',                       //业务概述
            'after_sales_response',                    //业务概述 - 响应时效
            'after_sales_response_en',                    //业务概述 - 响应时效
            'after_sales_coverage',                    //业务概述 - 覆盖地区
            'after_sales_coverage_en',                    //业务概述 - 覆盖地区
            // --------- 2. 业务概述 ---------
            // --------- 3. 客户及业绩 ---------
            'customers_performance',                        //客户及业绩
            'customers_performance_en',                     //客户及业绩
            // --------- 3. 客户及业绩 ---------
            // --------- 4. 法务与合规 ---------
            'untreated_administrative_penalty_reason',      //未处理的行政处罚原因
            'untreated_administrative_penalty_reason_en',   //未处理的行政处罚原因
            // --------- 4. 法务与合规 ---------
            // --------- 5. 财务报告 ---------
            // 暂时还没有做
            // --------- 5. 财务报告 ---------
            // --------- 6. 未来规划概况 ---------
            'future_planning',                              //未来规划概况
            'future_planning_en',                           //未来规划概况
            // --------- 6. 未来规划概况 ---------
        )->where([
            ['pid', '=', $pid],
        ])->first();
        if ($getExtra) {
            // --------- 1. 公司概述 ---------
            // 股东情况
            $shareholder = GspInvestigationShareholders::where([
                ['pid', '=', $pid]
            ])->get()->toArray();
            $shareholderName    = array_column($shareholder, 'name');
            $shareholderNameEn  = array_column($shareholder, 'name_en');
            $shareholderRatio   = array_column($shareholder, 'shareholding_ratio');
            $main['shareholder_name']       = implode(',', $shareholderName);
            $main['shareholder_name_en']    = implode(',', $shareholderNameEn);
            $main['shareholder_ratio']      = array_sum($shareholderRatio);
            // --------- 1. 公司概述 ---------
            // --------- 2. 业务概述 ---------
            //生产及办公面积
            $officeSpaces = GspInvestigationOfficeSpaces::where([
                ['pid', '=', $pid]
            ])->get()->toArray();
            $officeSpacesArea           = array_column($officeSpaces, 'area');
            $main['office_spaces_area'] = array_sum($officeSpacesArea);
            //前五大主要生产设备
            $productionEquipment = GspInvestigationProductionEquipment::where([
                ['pid', '=', $pid]
            ])->get()->toArray();
            $productionEquipmentName                = array_column($productionEquipment, 'name');
            $productionEquipmentNameEn              = array_column($productionEquipment, 'name_en');
            $main['production_equipment_name']      = implode(',', $productionEquipmentName);
            $main['production_equipment_name_en']   = implode(',', $productionEquipmentNameEn);
            //前十主要原材料及来源占比
            $rawMaterials = GspInvestigationRawMaterials::where([
                ['pid', '=', $pid]
            ])->get()->toArray();
            $rawMaterialsCountry    = [];
            $rawMaterialsCountryEn  = [];
            $countriesData          = self::countriesData();
            $rawMaterialsColumn     = array_column($rawMaterials, 'country');
            foreach ($countriesData as $kc => $vc) {
                if (in_array($kc, $rawMaterialsColumn)) {
                    $rawMaterialsCountry[]   = $vc['countryZH'];
                    $rawMaterialsCountryEn[] = $vc['countryEN'];
                    break;
                }
            }
            // $rawMaterialsCountry                    = CountryModel::whereIn('countryID', array_column($rawMaterials, 'country'))->pluck('countryZH')->toArray();
            $main['raw_materials_country']          = implode(',', $rawMaterialsCountry);
            $main['raw_materials_country_en']       = implode(',', $rawMaterialsCountryEn);
            $rawMaterialsSupplier                   = array_column($rawMaterials, 'supplier');
            $rawMaterialsSupplierEn                 = array_column($rawMaterials, 'supplier_en');
            $main['raw_materials_supplier']         = implode(',', $rawMaterialsSupplier);
            $main['raw_materials_supplier_en']      = implode(',', $rawMaterialsSupplierEn);
            // --------- 2. 业务概述 ---------
            // --------- 3. 客户及业绩 ---------
            $selectOpt      = GspInvestigationMain::$select['cna_gsp_investigation_customer_situation']['industry'];
            $selectOptEn    = GspInvestigationMain::$select['cna_gsp_investigation_customer_situation']['industry_en'];
            $selectOpt      = array_column($selectOpt, 'label', 'value');
            $selectOptEn    = array_column($selectOptEn, 'label', 'value');
            //前五大主要客户情况
            $customerSituation = GspInvestigationCustomerSituation::where([
                ['pid', '=', $pid]
            ])->get()->toArray();
            $customerSituationIndustry              = array_column($customerSituation, 'industry');
            $customerSituationIndustryEn            = array_column($customerSituation, 'industry_en');
            foreach ($customerSituationIndustry as $k => $v) {
                $v = "{$selectOpt[$v]}";
                $customerSituationIndustry[$k] = $v;
            }
            foreach ($customerSituationIndustryEn as $k => $v) {
                $v = "{$selectOptEn[$v]}";
                $customerSituationIndustryEn[$k] = $v;
            }
            $main['customer_situation_industry']    = implode(',', $customerSituationIndustry);
            $main['customer_situation_industry_en'] = implode(',', $customerSituationIndustryEn);
            $customerSituationName                  = array_column($customerSituation, 'name');
            $customerSituationNameEn                = array_column($customerSituation, 'name_en');
            $main['customer_situation_name']        = implode(',', $customerSituationName);
            $main['customer_situation_name_en']     = implode(',', $customerSituationNameEn);
            $customerSituationAmount                = array_column($customerSituation, 'amount');
            $main['customer_situation_amount']      = array_sum($customerSituationAmount);
            //产品应用案例
            $productApplicationCases                = GspInvestigationProductApplicationCases::where([
                ['pid', '=', $pid]
            ])->get()->toArray();
            $productApplicationCasesProjectName                 = array_column($productApplicationCases, 'project_name');
            $productApplicationCasesProjectNameEn               = array_column($productApplicationCases, 'project_name_en');
            $main['product_application_cases_project_name']     = implode(',', $productApplicationCasesProjectName);
            $main['product_application_cases_project_name_en']  = implode(',', $productApplicationCasesProjectNameEn);
            $productApplicationCasesCountry     = [];
            $productApplicationCasesCountryEn   = [];
            $countriesData                      = self::countriesData();
            $columnTemp                         = array_column($productApplicationCases, 'country');
            foreach ($countriesData as $kc => $vc) {
                if (in_array($kc, $columnTemp)) {
                    $productApplicationCasesCountry[]   = $vc['countryZH'];
                    $productApplicationCasesCountryEn[] = $vc['countryEN'];
                    break;
                }
            }
            $productApplicationCasesCountry                 = CountryModel::whereIn('countryID', array_column($productApplicationCases, 'country'))->pluck('countryZH')->toArray();
            $main['product_application_cases_country']      = implode(',', $productApplicationCasesCountry);
            $main['product_application_cases_country_en']   = implode(',', $productApplicationCasesCountryEn);
            $productApplicationCasesAmount                  = array_column($customerSituation, 'amount');
            $main['product_application_cases_amount']       = array_sum($productApplicationCasesAmount);
            // --------- 3. 客户及业绩 ---------
            // --------- 4. 法务与合规 ---------
            //资质及认证
            $qualificationsCertificateName = [];
            $qualificationsCertificateNameEn = [];
            if (empty($main['no_cna_gsp_investigation_qualifications'])) {
                $qualifications = GspInvestigationQualifications::where([
                    ['pid', '=', $pid]
                ])->get()->toArray();
                $qualificationsCertificateName                  = array_column($qualifications, 'certificate_name');
                $qualificationsCertificateNameEn                = array_column($qualifications, 'certificate_name_en');
            }
            $main['qualifications_certificate_name']        = implode(',', $qualificationsCertificateName);
            $main['qualifications_certificate_name_en']     = implode(',', $qualificationsCertificateNameEn);
            //知识产权 类型数量
            $intellectualPropertyType   = [];
            $intellectualPropertyTypeEm = [];
            if (empty($main['no_cna_gsp_investigation_intellectual_properties'])) {
                $intellectualPropertyType = GspInvestigationIntellectualProperties::where([
                    ['pid', '=', $pid]
                ])->selectRaw('COUNT(*) as count,intellectual_property_type')->groupBy('intellectual_property_type')->pluck('count', 'intellectual_property_type')->toArray();
                $intellectualPropertyTypeEm = GspInvestigationIntellectualProperties::where([
                    ['pid', '=', $pid]
                ])->selectRaw('COUNT(*) as count,intellectual_property_type_en')->groupBy('intellectual_property_type_en')->pluck('count', 'intellectual_property_type_en')->toArray();
                $selectOpt      = GspInvestigationMain::selectOption()['cna_gsp_investigation_intellectual_properties']['intellectual_property_type'];
                $selectOptEn    = GspInvestigationMain::selectOption()['cna_gsp_investigation_intellectual_properties']['intellectual_property_type_en'];
                $selectOpt      = array_column($selectOpt, 'label', 'value');
                $selectOptEn    = array_column($selectOptEn, 'label', 'value');
                foreach ($intellectualPropertyType as $k => $v) {
                    $v = "{$selectOpt[$k]}{$v}件";
                    $intellectualPropertyType[$k] = $v;
                }
                foreach ($intellectualPropertyTypeEm as $k => $v) {
                    $selectOptEnK = $selectOptEn[$k] ?? '';
                    $v = "{$selectOptEnK} {$v} piece";
                    $intellectualPropertyTypeEm[$k] = $v;
                }
            }
            $main['intellectual_property_type']        = implode(', ', $intellectualPropertyType);
            $main['intellectual_property_type_en']     = implode(', ', $intellectualPropertyTypeEm);

            //重大合同
            $significantContracts = GspInvestigationSignificantContracts::where([
                ['pid', '=', $pid]
            ])->get()->toArray();
            $main['significant_contracts_count']         = count($significantContracts);
            $significantContractsTypeAmount              = array_column($significantContracts, 'amount');
            $main['significant_contracts_amount']        = array_sum($significantContractsTypeAmount);
            //重大诉讼及仲裁信息
            $litigationArbitrationCasesCount  = 0;
            $litigationArbitrationCasesAmount = 0;
            if (empty($main['no_cna_gsp_investigation_litigation_arbitration_cases'])) {
                $litigationArbitrationCases = GspInvestigationLitigationArbitrationCases::where([
                    ['pid', '=', $pid]
                ])->get()->toArray();
                $litigationArbitrationCasesCount                = count($litigationArbitrationCases);
                $litigationArbitrationCasesAmount               = array_sum(array_column($litigationArbitrationCases, 'amount'));
            }
            $main['litigation_arbitration_cases_count']     = $litigationArbitrationCasesCount;
            $main['litigation_arbitration_cases_amount']    = $litigationArbitrationCasesAmount;
            //行政处罚记录
            $administrativePenaltyRecordsCount = 0;
            if (empty($main['no_cna_gsp_investigation_administrative_penalty_records'])) {
                $administrativePenaltyRecords = GspInvestigationAdministrativePenaltyRecords::where([
                    ['pid', '=', $pid]
                ])->get()->toArray();
                $administrativePenaltyRecordsCount           = count($administrativePenaltyRecords);
            }
            $main['administrative_penalty_records_count']    = $administrativePenaltyRecordsCount;
            //失信记录
            $dishonestyRecordsCount = 0;
            if (empty($main['no_cna_gsp_investigation_dishonesty_records'])) {
                $dishonestyRecords = GspInvestigationDishonestyRecords::where([
                    ['pid', '=', $pid]
                ])->get()->toArray();
                $dishonestyRecordsCount = count($dishonestyRecords);
            }
            $main['dishonesty_records_count']    = $dishonestyRecordsCount;
            // --------- 4. 法务与合规 ---------

            // --------- 5. 财务报告 ---------
            //综合损益表
            $comprehensiveIncomes = GspInvestigationComprehensiveIncomes::where([
                ['pid', '=', $pid]
            ])->get()->toArray();
            $yearArr            = array_column($comprehensiveIncomes, 'year');
            $revenueArr         = array_column($comprehensiveIncomes, 'revenue');
            $profitForThePeriodArr = array_column($comprehensiveIncomes, 'profit_for_the_period');
            $main['comprehensive_incomes_year1'] = $yearArr[0] ?? '';
            $main['comprehensive_incomes_year2'] = $yearArr[1] ?? '';
            $main['comprehensive_incomes_revenue1'] = $revenueArr[0] ?? '';
            $main['comprehensive_incomes_revenue2'] = $revenueArr[1] ?? '';
            $main['comprehensive_incomes_profit_for_the_period1'] = (!empty($revenueArr[0]) ? bcdiv($profitForThePeriodArr[0] ?? 0, $revenueArr[0], 4) : 0) * 100;
            $main['comprehensive_incomes_profit_for_the_period2'] = (!empty($revenueArr[1]) ? bcdiv($profitForThePeriodArr[1] ?? 0, $revenueArr[1], 4) : 0) * 100;
            if (($main['comprehensive_incomes_profit_for_the_period1'] * 100) > ($main['comprehensive_incomes_profit_for_the_period2'] * 100)) {
                list($main['comprehensive_incomes_profit_for_the_period2'], $main['comprehensive_incomes_profit_for_the_period1']) = array($main['comprehensive_incomes_profit_for_the_period1'], $main['comprehensive_incomes_profit_for_the_period2']);
            }
            $main['comprehensive_incomes_accounting_name']     = !empty($comprehensiveIncomes[0]['accounting_name']) ? $comprehensiveIncomes[0]['accounting_name'] : '';
            $main['comprehensive_incomes_accounting_name_en']     = !empty($comprehensiveIncomes[0]['accounting_name_en']) ? $comprehensiveIncomes[0]['accounting_name_en'] : '';
            // 资产负债表
            $balanceSheet = GspInvestigationBalanceSheet::where([
                ['pid', '=', $pid]
            ])->get()->toArray();
            $totalLiabilitiesArr    = array_column($balanceSheet, 'total_liabilities');
            $totalAssetsArr         = array_column($balanceSheet, 'total_assets');
            $main['balance_sheet_asset_liability_ratio1'] = (!empty($totalAssetsArr[0]) ? bcdiv($totalLiabilitiesArr[0] ?? 0, $totalAssetsArr[0], 4) : 0) * 100;
            $main['balance_sheet_asset_liability_ratio2'] = (!empty($totalAssetsArr[1]) ? bcdiv($totalLiabilitiesArr[1] ?? 0, $totalAssetsArr[1], 4) : 0) * 100;
            $main['balance_sheet_asset_liability_ratio_all'] = bcadd($main['balance_sheet_asset_liability_ratio1'], $main['balance_sheet_asset_liability_ratio2'], 2);
            // --------- 5. 财务报告 ---------
        }
        return $main;
    }

    //翻译
    public function translate(Request $request)
    {
        $validated = $request->validate([
            'content'         => ['required'],
        ]);
        $res = [];
        // $content    = json_encode($validated['content'], JSON_UNESCAPED_UNICODE);
        foreach ($validated['content'] as $v) {
            $res[]  = $this->sendPostAiIdentification($v);
        }
        return responseSuccess(['original' => $validated['content'], 'translate' => $res]);
    }

    //翻译接口
    protected function sendPostAiIdentification($sendContent)
    {
        $content = "";
        $url = "https://translate.corporate-advisory.cn/api/v2/translate/textTranslate";
        $headers = [];
        $postData = [
            "language" => 'English',
            "content" => $sendContent,
            "type" => 0,
        ];
        $ch = curl_init($url);
        // curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, 1); // 启用POST请求
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData); // POST的数据
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // 返回结果而非直接输出
        // 执行cURL会话并获取返回结果
        $response = curl_exec($ch);

        // 检查是否有错误发生
        if (curl_errno($ch)) {
            $content = "";
        }
        $arr = json_decode($response, true);

        if (!empty($arr['code']) && $arr['code'] == '200' && !empty($arr['data']) && !empty($arr['data']['content'])) {
            $content = $arr['data']['content'];
        }
        // 关闭cURL资源
        curl_close($ch);
        return $content;
    }

    //未来规划优化
    public function futurePlanningBeautify(Request $request)
    {
        $validated = $request->validate([
            'future_planning_detail'         => ['required'],
        ]);
        $content = "";
        $url = 'https://api.testai.corporate-advisory.cn/train/api/v1/ai/chat/completions';
        $postData = [
            "roleId" => '57f52b6f-b893-474c-91c3-03ce2e0c217f',
            "prompt" => $validated['future_planning_detail'],
            "model" => 'deepseek',
        ];
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, 1); // 启用POST请求
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData); // POST的数据
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // 返回结果而非直接输出
        // 执行cURL会话并获取返回结果
        $response = curl_exec($ch);
        // 检查是否有错误发生
        if (curl_errno($ch)) {
            $content = "";
        }
        $arr = json_decode($response, true);

        if (!empty($arr['data']) && !empty($arr['data']['content']) && !empty($arr['data']['content'][0])) {
            $content = [$arr['data']['content']];
        }
        if (!empty($content[0])) {
            $content[0] = str_replace(['“', '”'], '"', $content[0]);
        }
        // 关闭cURL资源
        curl_close($ch);
        return responseSuccess(['future_planning_detail' => $content]);
    }

    //中文连着英文pdf输出
    public function zhAndEnPDF($strZH, $strEN, $isConnect = true)
    {
        if ($isConnect) {
            return $strZH . '<br>' . $strEN;
        }
        return $strZH;
    }

    //全体董事、监事、高级管理人员声明数据查询
    public static function managementStatementSearch($pid)
    {
        $arr        = [];
        $typeArr    = GspInvestigationManagementStatement::$typeToName;
        $managemen = GspInvestigationManagementStatement::select(
            'type',
            'name',
            'pid',
            'id',
        )->where([
            ['pid', '=', $pid],
        ])->get()->toArray();

        foreach ($managemen as $k => $v) {
            $arr[$typeArr[$v['type']]][] = $v;
        }
        return $arr;
    }

    //全体董事、监事、高级管理人员声明
    protected function managementStatementData($request)
    {
        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        $this->createMain($pid);
        $management = self::managementStatementSearch($pid);
        return ['management' => $management, 'pid' => $pid];
    }

    //全体董事、监事、高级管理人员声明 - 获取
    public function managementStatement(Request $request)
    {
        $management = $this->managementStatementData($request);
        return responseSuccess($management['management']);
    }
    //全体董事、监事、高级管理人员声明 - 修改
    public function saveManagementStatement(Request $request)
    {
        $required   = $request->temp ? 'sometimes' : 'required';
        $input      = $request->input();
        $validated = $request->validate([
            'director'      => [$required],
            'supervisor'    => [$required],
            'management'    => [$required],
        ]);

        $user   = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (!$record) throw new DefaultException("找不到主流程");
        $pid    = $record['id'];
        //判断是否可以提交尽调报告，或填写清单
        if (!$this->isWrite($pid, !empty($user))) return responseFail(__('gsp.submit_repeatedly'));
        try {
            // 开启事务
            DB::beginTransaction();
            $configId = 49;
            $version = GspInvestigationVersions::where([
                ['pid', '=', $pid],
                ['cna_gsp_investigation_config_id', '=', $configId],
            ])->first();

            $this->createMain($pid);
            $data = [];
            foreach ($validated['director'] as $key => $value) {
                $value  = $this->removeControlChar($value);
                $data[] = ['id' => $value['id'] ?? 0, 'name' => $value['name'], 'pid' => $pid, 'type' => GspInvestigationManagementStatement::TYPE_DIRECTOR];
            }
            foreach ($validated['supervisor'] as $key => $value) {
                $value  = $this->removeControlChar($value);
                $data[] = ['id' => $value['id'] ?? 0, 'name' => $value['name'], 'pid' => $pid, 'type' => GspInvestigationManagementStatement::TYPE_SUPERVISOR];
            }
            foreach ($validated['management'] as $key => $value) {
                $value  = $this->removeControlChar($value);
                $data[] = ['id' => $value['id'] ?? 0, 'name' => $value['name'], 'pid' => $pid, 'type' => GspInvestigationManagementStatement::TYPE_MANAGEMENT];
            }
            //删除不存的
            $inputId = array_column($data, 'id');
            $exist = GspInvestigationManagementStatement::where([
                ['pid', '=', $pid]
            ])->pluck('id')->toArray();
            $del = array_diff($exist, $inputId);
            GspInvestigationManagementStatement::whereIn('id', $del)->delete();
            foreach ($data as $item) {
                if (!empty($item['id'])) {
                    //修改
                    $model = GspInvestigationManagementStatement::where([
                        ['id', '=', $item['id']],
                        ['pid', '=', $pid]
                    ])->first();
                    if (!$model) throw new DefaultException("找不到扩展表");
                } else {
                    //写入
                    $model = new GspInvestigationManagementStatement();
                    $model->pid = $pid;
                }
                $model->name = $item['name'];
                $model->type = $item['type'];
                $model->save();
            }

            $lastTime = GspInvestigationManagementStatement::where([
                ['pid', '=', $pid],
            ])->select('updated_at')->orderBy('updated_at', 'desc')->first();
            if (empty($version['last_modified_time'])) $version = new GspInvestigationVersions();
            if ($version['last_modified_time'] < $lastTime['updated_at'] && !$request->temp) {
                $version->pid = $pid;
                $version->cna_gsp_investigation_config_id = $configId;
                $version->last_modified_time = $lastTime['updated_at'];
                $version->save();
            }
            DB::commit();
            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DefaultException();
        }
    }

    //全体董事、监事、高级管理人员声明 生成PDF
    public function managementStatementGeneratePdf(Request $request)
    {
        $management         = $this->managementStatementData($request);
        $managementData     = $management['management'];
        $pid                = $management['pid'];
        $fileName           = Lang::get("gsp.cna_gsp_investigation_management_statement.table_name") . '.pdf';
        $title              = Lang::get("gsp.cna_gsp_investigation_management_statement.table_title");
        $column = [
            ['全体董事', '名称', '签名'],
            ['全体监事', '名称', '签名'],
            ['全体高级管理人员', '名称', '签名'],
        ];
        $unit               = ''; //表格单位
        $desc               = Lang::get("gsp.cna_gsp_investigation_management_statement.table_desc"); // 表格描述        
        $bottom             = "";
        $content = [];
        foreach ($managementData[GspInvestigationManagementStatement::$typeToName[GspInvestigationManagementStatement::TYPE_DIRECTOR]] as $k => $v) {
            $content[0][] = [$v['name'], ''];
        }
        foreach ($managementData[GspInvestigationManagementStatement::$typeToName[GspInvestigationManagementStatement::TYPE_SUPERVISOR]] as $k => $v) {
            $content[1][] = [$v['name'], ''];
        }
        foreach ($managementData[GspInvestigationManagementStatement::$typeToName[GspInvestigationManagementStatement::TYPE_MANAGEMENT]] as $k => $v) {
            $content[2][] = [$v['name'], ''];
        }
        $version = GspInvestigationVersions::where([
            ['pid', '=', $pid],
            ['cna_gsp_investigation_config_id', '=', 19],
        ])->first();
        $formModify         = date('Y-m-d', strtotime($version['last_modified_time']));
        $lastModify         = date('Y-m-d H:i:s', strtotime($version['last_modified_time']));
        $token = (new PdfTableService($title, $column,  $content, '', $unit, $desc,  $bottom, '', 0, $fileName, $formModify, $lastModify))->generateLeadPdf();
        return responseSuccess(['token' => $token]);
    }
}
