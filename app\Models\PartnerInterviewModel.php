<?php

namespace App\Models;

use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;

class PartnerInterviewModel extends Model
{
    use HasFactory;

    protected $table = 'cna_interviews_info';

    protected $fillable = [
        'profileID',
        'token',
        'average_score',
        'interview_start_at',
        'interview_end_at',
        'status',
    ];


    public static function checkInterview($profileID)
    {
        try {
            // 查找最新的一条面试记录
            $latestInterview = PartnerInterviewModel::where('profileID', $profileID)
                ->orderBy('created_at', 'desc')
                ->first();
    
            // 统计该面试者的爽约次数
            $absent_count = PartnerInterviewModel::where('profileID', $profileID)
                ->where('status', -2)
                ->count();
    
            // 统计该面试者的面试失败次数
            $fail_count = PartnerInterviewModel::where('profileID', $profileID)
                ->where('status', -1)
                ->count();
    
            return [
                'latestInterview' => $latestInterview,
                'absent_count' => $absent_count,
                'fail_count' => $fail_count,
            ];
        } catch (Exception) {
            return [
                'latestInterview' => false,
                'absent_count' => 0,
                'fail_count' => 0,
            ];
        }
    }


    public static function getQuestions()
    {
        $questions = [];

        for ($module = 1; $module <= 5; $module++) {
            $qs = DB::table('cna_interview_question_base')
                ->select('id', 'module', 'question')
                ->where('module', $module)
                ->inRandomOrder()
                ->limit(3)
                ->get();

            $questions = array_merge($questions, $qs->all());
        }

        return $questions;
    }


    public static function getScore($question, $answer)
    {
        // 设置请求的URL
        $url = 'https://api.testai.corporate-advisory.cn/train/api/v1/ai/chat/completions';
        $prompt = json_encode([
            'question' => $question,
            'answer' => $answer,
        ]);


        $data = [
            'prompt' => $prompt,
            'roleId' => "63e12d57-5dc2-4e96-9dda-a66d6f6aa721",
            'stream' => false,
        ];

        $response = Http::post($url, $data);
        $result = $response->json();
        $contentJson = $result['data']['content'];
        $contentArray = json_decode($contentJson, true);
        return $contentArray;
    }

    public static function getNextQuestion($question, $answer, $score, $audioUrl, $question_id, $interview_id)
    {
        $response = [];

        try {
            $answerData = [
                'question_id' => $question_id,          // 问题 ID
                'question' => $question,                // 问题文本
                'answer_text' => $answer,               // 回答文本
                'answer_url' => $audioUrl,              // 回答音频链接
                'score' => $score,                      // 回答评分
            ];
            // 存储问答情况到 Redis
            Redis::lpush("interview_{$interview_id}_answers", json_encode($answerData));

            $nextQuestionData = Redis::rpop("interview_{$interview_id}_questions");
            if ($nextQuestionData) {
                // 解码下一个问题数据
                $nextQuestion = json_decode($nextQuestionData, true);
                if (json_last_error() === JSON_ERROR_NONE && isset($nextQuestion['question_id'], $nextQuestion['question_text'], $nextQuestion['question_url'])) {
                    $response['nextQuestion'] = [
                        'question_id' => $nextQuestion['question_id'],
                        'question_text' => $nextQuestion['question_text'],
                        'question_url' => $nextQuestion['question_url']
                    ];

                } else {
                    $response['nextQuestion'] = null;
                }
            } else {
                // 如果没有更多问题，面试结束
                $response['nextQuestion'] = null;
            }
        } catch (Exception $e) {
            // 处理异常情况
            $response['nextQuestion'] = null;
        }

        return $response;
    }

    // 获取结束语
    public static function getPeroration()
    {
        // 设置请求的URL
        $url = 'https://api.testai.corporate-advisory.cn/train/api/v1/ai/chat/completions';

        $data = [
            'prompt' => '面试结束',
            'roleId' => "f5548458-8a6d-4d9f-8b80-7603b08f899d",
            'stream' => false,
        ];

        $response = Http::post($url, $data);
        $result = $response->json();
        $content = $result['data']['content'];
        return $content;
    }


    // 定期标记爽约面试
    public function markMissedInterviews()
    {
        $now = Carbon::now();

        // 查出爽约的 ID 集合
        $expiredIds = DB::table('cna_interviews_info')
            ->where('interview_end_at', '<', $now)
            ->where('status', 0)
            ->pluck('id');

        if ($expiredIds->isNotEmpty()) {
            DB::table('cna_interviews_info')
                ->whereIn('id', $expiredIds)
                ->update([
                    'status' => -2,
                    'updated_at' => $now,
                ]);

            foreach ($expiredIds as $id) {
                Redis::del("interview_{$id}_questions");
                Redis::del("interview_{$id}_status");
            }
        }
    }

}
