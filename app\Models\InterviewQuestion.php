<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InterviewQuestion extends Model
{
    use HasFactory;

    // 问题模块
    const MODULE_PERSONALITY = 1;
    const MODULE_LOGIC = 2;
    const MODULE_LONG_TERM = 3;
    const MODULE_LEARNING = 4;
    const MODULE_ACTION = 5;

    const MODULE_MAP = [
        self::MODULE_PERSONALITY => '个人气质与态度',
        self::MODULE_LOGIC => '逻辑思维能力',
        self::MODULE_LONG_TERM => '长远眼光与洞察力',
        self::MODULE_LEARNING => '学习与创新',
        self::MODULE_ACTION => '行动与效率',
    ];

    protected $connection = 'mysql_ai_desk';

    protected $table = 'cna_interview_question_base';
    protected $guarded = [];

    protected $fillable = ['module', 'module_name', 'question'];

    public function nextQuestions()
    {
        return $this->hasMany(InterviewQuestionNext::class, 'base_id');
    }
}
