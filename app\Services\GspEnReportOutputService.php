<?php
namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Storage;
use TCPDF_FONTS;

class GspEnReportOutputService
{
    private $data; // 报告内容
    private $filename; // 报告名称

    private $type; // 是否后台工作人员看( 1 合伙人看; 2 工作人员看)

    private $pdf;


    public function __construct($filename, $data)
    {

        $this->filename = $filename;
        $this->data = $data;
    }

    public function addContent() {

        // 第一页公司图片
        $this->page1();

        // 第二页声明
        $this->page2();

        // 第三页目录
        $this->page3();

        // 开始新的页面组（用于正文）
        $this->pdf->startPageGroup();

        // 第四页概况摘要
        $this->page4();

        // 第五页公司概述
        $this->page5();

        // 第六页第三部分业务概述
        $this->page6();

        // 第七页第四部分客户及业绩
        $this->page7();

        // 第八页第五部分法务与合规
        $this->page8();

        // 第九页财务部分
        $this->page9();

        // 第十页未来规划
        $this->page10();

        // 第十一页附件
        $this->page11();
    }

    /**
     * 生成尽调报告处理
     * @type  1 合伙人看; 2 工作人员看
     * @return string
     */
    public function output($type = 1)
    {

        $this->type = $type;

        // 创建一个新的 TCPDF 实例
        $this->pdf = new MYPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

        // 禁用页眉
        $this->pdf->setPrintHeader(false);

        // 设置字体路径
        $fontFile = storage_path('fonts/times.php');
        $newFile = storage_path('app/public/fonts/times.php');

        // 检查目标目录是否存在，如果不存在则创建
        if (!is_dir(dirname($newFile))) {
            mkdir(dirname($newFile), 0755, true);
        }

        // 检查字体文件是否存在，如果不存在则复制
        if (!file_exists($newFile)) {
            if (!copy($fontFile, $newFile)) {
                die("复制字体文件失败: $fontFile 到 $newFile");
            }
        }

        $fontPath = realpath($newFile); // 替换为你的字体文件路径

        // 添加 TTF 字体
        $fontname = TCPDF_FONTS::addTTFfont($fontPath, 'TrueTypeUnicode', '', 32);

        // 设置字体
        $this->pdf->SetFont($fontname, '', 12);

        // 启用页脚
        $this->pdf->setPrintFooter(true);
        $this->pdf->setFooterData(
            array(0, 0, 0),  // 页脚文本颜色
            array(255, 255, 255)  // 边框颜色设为白色（与背景同色）
        );
        $this->pdf->setFooterMargin(20); // 设置页脚边距


        // 设置内容
        $this->addContent();

        // 获取总页数并设置
        /*$totalPages = $this->pdf->getNumPages();
        $this->pdf->setTotalPages(100);*/

        // 保存文档
        $directory = storage_path('app/public/gsp/report/');
        if (!is_dir($directory)) {
            mkdir($directory, 0777, true); // 递归创建目录
        }
        $file = $directory.$this->filename;

        $this->pdf->Output($file, 'F');

        return 'gsp/report/'.$this->filename;
    }


    /**
     * 第一页公司图片
     * @return void
     */
    public function page1()
    {

        // 添加一个页面
        $this->pdf->AddPage();
        // 提取数组中的键值对为变量
        extract($this->data['page1']);

        $report_img = storage_path('pdf/gsp_template/report_img_en.png');

        // 获取页面尺寸（考虑边距）
        $width = $this->pdf->getPageWidth();
        $height = $this->pdf->getPageHeight();

        // 临时禁用边距和自动分页
        $this->pdf->SetMargins(0, 0, 0);
        $this->pdf->SetAutoPageBreak(false);

        // 插入图片（强制填充）
        $this->pdf->Image(
            $report_img,
            0, 0,
            $width, $height,
            '', '', '',
            false, 300, '',
            false, false, 0, 'T', false
        );

        if ($this->type == 2) {
            $this->pdf->SetTextColor(255, 0, 0); // 红色文本
        }

        $this->pdf->setXY(1, 202);
        $this->pdf->Cell(0, 20, $field1, 0, 1, 'C'); // 目标公司名称

        $this->pdf->setXY(0, 215);
        $this->pdf->Cell(0, 20, $field2, 0, 1, 'C'); // 报告基准日

        $this->pdf->setXY(0, 225);
        $this->pdf->Cell(0, 20, $field3, 0, 1, 'C'); // 报告出具日

        $this->pdf->setXY(10, 236);
        $this->pdf->Cell(0, 20, $field4, 0, 1, 'C'); // 报告编号

        $this->pdf->setXY(0, 247);
        $this->pdf->Cell(0, 20, $field5, 0, 1, 'C'); // 报告有效期

        $this->pdf->setXY(50, 259);
        $this->pdf->Cell(0, 20, 'Clement & Associates International Limited', 0, 1, 'C'); // 出具方


        // 恢复默认设置
        $this->pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
        $this->pdf->SetAutoPageBreak(true, PDF_MARGIN_BOTTOM);
        $this->pdf->SetTextColor(0, 0, 0);

    }

    // 公司声名
    public function page2()
    {

        // 工作人员查看生成增加颜色
        if ($this->data['page2'] && $this->type==2) {
            foreach ($this->data['page2'] as $key => $val) {
                $this->data['page2'][$key] = '<span style="color:red">'.$val.'</span>';
            }
        }

        // 提取数组中的键值对为变量
        extract($this->data['page2']);

        $this->pdf->AddPage();

        $htmlTable = <<<EOD
        <style>
            .text-page2{
                line-height: 20px;
                font-size: 12px;
            }
            .bold {
                font-weight: bold;
            }
        </style>
        <div>
        <div style="font-size: 16px; text-align: center"><b>Declaration from the Issuing Party of the Due Diligence Report</b></div>
        <div>
        <div class="text-page2">
        <span><b>BACKGROUND</b></span><br><br>
        This Due Diligence Report ("<b>Report</b>") has been prepared as part of Green Smart Planet’s commitment to transparency and business reliability. Green Smart Planet is an online ecosystem platform focuses on the industry ecosystem of sustainable and technological urban development, connecting industry stakeholders on business needs and opportunities ("<b>Platform</b>").<br>
        To support the transparency and business reliability on the Platform, Clement & Associates International Limited ("<b>C&A</b>") as the designated due diligence service provider of the Platform, to conduct a due diligence review of the Target Company ("<b>Company</b>"). This review reflects the current status of the Company in terms of basic company information, core business, major clients/customers and track record(s), legal affairs and compliance, financial information and future plan(s).
        </div>
        <div class="text-page2">
        <span><b>NATURE AND SCOPE</b></span><br><br>
        This Report is a factual due diligence report. The legal and financial information provided is based on the applicable Chinese (excluding Hong Kong, Macao and Taiwan) laws and Chinese (excluding Hong Kong, Macao and Taiwan) corporate accounting systems as of the report issuance date. This Report is prepared solely to provide factual and reference-based information on the Company’s operational and service capacity, financial stability, clients/customers and track record(s), legal affairs and compliance and future plan(s). This Report does not contain evaluations, conclusions or recommendations, nor does it assess the overall business viability or investment potential of the Company.
        <br><br>
        <span><b>USE AND TARGET AUDIENCE</b></span><br><br>
        This Report is released exclusively on the Platform and its related local domains for use by registered users of the Platform. It is intended for internal reference only and may not be shared, reproduced or distributed outside of the Platform without the written consent of the Platform, its successors, assigns and/or its associated companies/related parties.
        <br><br>
        <span><b>STATEMENT OF NO GUARANTEE OF ACCURACY AND COMPLETENESS</b></span><br><br>
        This Report is based on information provided by the Company, publicly available sources, and third-party verification if applicable. While reasonable efforts have been made to verify key aspects, C&A is not responsible for the authenticity and validity of the information provided by the Company including brief introduction, key personnel(s), production capacity and delivery capability, quality control measure(s), top 10 raw materials and the proportions, after-sales service and guarantee, major clients, product application case(s), recognition and recommendation letter(s) obtained and future plan(s). C&A does not independently audit or guarantee the accuracy or completeness of any information contained herein either.
        <br><br>
        <span><b>COMPANY REPRESENTATIONS AND WARRANTIES</b></span><br><br>
        The Company has warranted and represented that all documents, statements and data provided for the preparation of this Report are true, accurate, complete and up-to-date as of the date of the Report. If it is later determined/discovered that the Company provided false or misleading information, C&A reserves the right to revoke/withdraw this Report and notify relevant stakeholders. The Company assumes full responsibility for any misrepresentation, omission or falsified information provided.
        <br><br>
        <span><b>STATEMENT OF NO GUARANTEE OF THE COMPANY’S FUTURE PERFORMANCE</b></span><br><br>
        This Report may contain forecasts and future plan(s) provided by the Company. Such statements are forward-looking and are based on the Company’s own expectations, assumptions, and strategic plan(s) at the time of the Report. These forecasts do not constitute a guarantee, commitment, promise or assurance that such outcomes will be realised or achieved. Actual results may differ due to market conditions, operational risks, financial performance, regulatory changes or unforeseen circumstances.
        <br><br>
        <span><b>DISCLAIMER OF C&A</b></span><br><br>
        This Report is provided “as is” and is for reference only. C&A expressly disclaims any and all liability arising from or related to the use of, reliance on or inability to use this Report. Under no circumstances shall C&A, its affiliates, employees or representatives be held liable for any direct, indirect, incidental, consequential, special or punitive damages, including but not limited to loss of profits, investments, business opportunities or goodwill arising from the use of this Report.
        <br><br>
        <span><b>READING TIPS</b></span><br><br>
        Each section of this Report addresses different aspects of C&A’s work. Please read the entire report to gain a comprehensive understanding of C&A's findings. 
        <br><br>
        <span><b>VALIDITY AND UPDATE</b></span><br><br>
        The due diligence work involved in this report started on the date when the company submitted all the required information and was completed on   $field1 /  $field2 /  $field3 . This Report does not take into account of events or circumstances that occurred after  $field4 / $field5 / $field6  and C&A has no obligation to update or revise this Report for such events or circumstances  that occurred after the aforementioned time point. This Report is valid for twelve (12) months from the date of its issuance. If the Company remains a user of the Platform after this period, the Company’s due diligence report should be renewed/updated annually.
        <br><br>
        <span><b>GOVERNING LAW AND JURISDICTION</b></span><br><br>
        This Report shall be governed by and  interpreted in accordance with the laws of England and Wales. Any disputes arising from or related to this Report shall be subject to the exclusive jurisdiction of the competent courts of England and Wales.
        <br><br>
        <span><b>ACKNOWLEDGEMENT BY REGISTERED USERS</b></span><br>
        By accessing this Report, the registered users of the Platform acknowledge that they have read, understood, and agreed to the terms stated in this declaration statement, including the nature of the report,  the use requirement of the report and limitations of liability. Any use of this Report indicates acceptance of these terms.
        </div>
        <div style="text-align: right">Report issuing party: Clement & Associates International Limited</div>
        <div style="text-align: center">Final reviewer: </div>
        </div>
        EOD;



        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

    }

    // 目录
    public function page3()
    {
        $this->pdf->AddPage();

        $htmlTable = <<<EOD
            <style>
            .text-page2{
                text-align: left;
                font-size: 11px;
                line-height: 14px;
            }
            .bold {
                font-weight: bold;
            }

        </style>
        <div style="font-size: 16px; text-align: center"><b>Content</b></div>
        <div class="text-page2">
        <b>Part One: Summary...............................................................................................................................................1</b><Br>
        1. Company Overview............................................................................................................................................1<Br>
        2. Business Overview.............................................................................................................................................1<Br>
        3. Clients/Customers and Track Record(s).............................................................................................................1<Br>
        4. Legal Affairs and Compliance ..........................................................................................................................1<Br>
        5. Financial Information.........................................................................................................................................2<Br>
        6. Future Plan(s).....................................................................................................................................................2<br>
        <b>Part Two: Company Overview............................................................................................................................3</b><Br>
        1. Brief Introduction...............................................................................................................................................3<Br>
        2. Statutory Information.........................................................................................................................................3<Br>
        3. Key Personnel(s) / Top Management.................................................................................................................4<Br>
        4. Ownership ( Top 5 Shareholders ).....................................................................................................................4<br>
        <b>Part Three: Business Overview............................................................................................................................5</b><Br>
        1. Main Product Categories....................................................................................................................................5<Br>
        2. Top 5 Major Production Equipment...................................................................................................................5<Br>
        3. Area Size of Production and Office...................................................................................................................5<Br>
        4. Number of Employees.......................................................................................................................................6<Br>
        5. Production Capacity and Delivery Capability...................................................................................................6<Br>
        6. Quality Control Measure(s)...............................................................................................................................6<Br>
        7. Top 10 Main Raw Materials and the Source Proportions..................................................................................7<Br>
        8. After Sales Service and Guarantee/Warranties..................................................................................................7<br>
        <b>Part Four: Clients/Customers and Track Record(s)..........................................................................................8</b><Br>
        1. Top 5 Major Clients/Customers.........................................................................................................................8<Br>
        2. Product Application/Use Case(s).......................................................................................................................8<Br>
        3. Recognition and Recommendation Letter(s) Received.....................................................................................9<br>
        <b>Part Five: Legal affairs and Compliance...........................................................................................................10</b><Br>
        1. Qualification(s) and Certification.....................................................................................................................10<Br>
        2. Intellectual Property.........................................................................................................................................10<Br>
        3. Major Contract(s).............................................................................................................................................10<Br>
        4. Major Lawsuits and Arbitration.......................................................................................................................11<Br>
        5. Administrative Penalty Record(s)....................................................................................................................11<Br>
        6. Credit Information............................................................................................................................................11<br>
        <b>Part Six: Financial Information.........................................................................................................................12</b><Br>
        1. Statement of Comprehensive Income...............................................................................................................12<Br>
        2. Statement of Financial Position........................................................................................................................13<Br>
        3. Statement of Cash Flows..................................................................................................................................15<Br>
        <b>Part Seven: Future Plan(s)................................................................................................................................18</b><Br>
        <b>Part Eight: Attachment.....................................................................................................................................21</b><Br>
        1. Company Information......................................................................................................................................21<br>
         2. Business Qualifications and Recognition.........................................................................................................21<br>
         3. Certificate of Rights.........................................................................................................................................21<br>
         4. On-site Photos of the Company........................................................................................................................21<br>
         5. Audit Report.....................................................................................................................................................21<br>
         6. Declaration of the Company.............................................................................................................................21<br>
        </div>
        EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');
    }

    // 概要摘要
    public function page4()
    {
        // 工作人员查看生成增加颜色
        if ($this->data['page4'] && $this->type==2) {
            foreach ($this->data['page4'] as $key => $val) {
                $this->data['page4'][$key] = '<span style="color:red">'.$val.'</span>';
            }
        }

        // 提取数组中的键值对为变量
        extract($this->data['page4']);

        $this->pdf->AddPage();


        $htmlTable = <<<EOD
        <style>
            .text-page2{
                line-height: 24px;
                 font-size: 14px;
            }
            .bold {
                font-weight: bold;
            }
        </style>
         <div style="font-size: 16px; text-align: center"><b>Part One: Summary</b></div>
        <div class="text-page2">
        <div><b>1.Company Overview</b><br>
        The Company was established in $field1 it is a member of the $field2 Group, having its headquarter in $field3. As of the Report Reference Date, the registered capital of the Company is $field4, the paid-in capital is $field5. The main shareholders include $field6 (with a shareholding ratio of $field7%). 
        <br>
        $field7a
        </div>
        <div><b>2.Business Overview</b><br>
         The Company's core business focuses on  $filed7b ,  with a production plant(s) and office occupy a total area of $field8 square metres, equipped with key facilities such as $field8a.The main raw materials come from $field8b, such as $field8c. The quality control system is described as $field9. After sales service and  guarantee/warranties are $field9a, to meet customer needs.
        $field9b
        </div>.
        <div><b>3.Clients/Customers and Track Record(s)</b><br>
        The Company's clients/customers and end-users cover $field9c, including $field9d (with a total annual transaction value of CNY $field10 ). Product application case(s) include $field10a, involving in $field10b, recorded total product supply value of CNY $field11. 
        <br>
        $field11a
        </div>
        <div><b>4.Legal Affairs and Compliance</b><br>
        The Company has obtained $field11b and intellectual property such as $field11c. Major contracts involve $field11d with a value exceeding CNY $field12 . There is/are currently $field13 lawsuits and arbitration case(s) involving a total value of CNY $field14 in past three years; The number of unresolved administrative penalty record(s) is/are $field15, is/are mainly due to $field16. There is/are $field17 record(s) of breach of trust.<br>
        </div>
        <div><b>5.Financial Information</b><br>
        Financial data of the Audit Report issued by $field18 entrusted by the Company shows that the Company's achieving revenues from $field18 to $field19 are  CNY $field20 and CNY $field21 respectively , with a net profit margin maintained at  $field22% - $field23%. The debt-to-asset ratio was  $field24a% - $field24c%.<br>
        </div>
        <div><b>6.Future Plan(s)</b><br>
         $field25
        </div>
        EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');


    }

    // 公司概述
    public function page5()
    {
        // 工作人员查看生成增加颜色
        if ($this->data['page5'] && $this->type==2) {
            foreach ($this->data['page5'] as $key => $val) {
                if (!is_array($val)) {
                    $this->data['page5'][$key] = '<span style="color:red">'.$val.'</span>';
                } else {
                    $redTable = [];
                    foreach ($val as  $row) {
                        foreach ($row as $index => $field ) {
                            $row[$index] = '<span style="color:red">'.$field.'</span>';
                        }
                        $redTable[] = $row;
                    }
                    $this->data['page5'][$key] = $redTable;
                }

            }
        }

        // 提取数组中的键值对为变量
        extract($this->data['page5']);

        $this->pdf->AddPage();

        // 管理层内容
        $table1 = '
         <table  border="0">';
        $table1 .= '<tr class="th"><td class="td" style="width: 25%">Name</td><td class="td" style="width: 25%">Position</td><td class="td" style="width: 25%">Nationality</td><td class="td" style="width: 25%">Date of Appointment</td></tr>';
        foreach ($this->data['page5']['field19'] as $row) {
            $table1 .= '<tr class="th">';
            $table1 .= '<td class="td" style="width: 25%">' . $row[0]. '</td>';
            $table1 .= '<td class="td" style="width: 25%">' . $row[1] . '</td>';
            $table1 .= '<td class="td" style="width: 25%">' . $row[2] . '</td>';
            $table1 .= '<td class="td" style="width: 25%">' . $row[3] . '</td>';
            $table1 .= '</tr>';
        }
        $table1 .= '</table>';

        // 股东情况
        $table2 = '<table>';
        $table2 .= '<tr class="th"><td class="td" style="width: 33%">Shareholder Name</td><td class="td" style="width: 33%">Nationality</td><td class="td" style="width: 33%">Shareholding Ratio</td></tr>';
        foreach ($this->data['page5']['field20'] as $row) {
            $table2 .= '<tr class="th">';
            $table2 .= '<td class="td" style="width: 33%">' . $row[0] . '</td>';
            $table2 .= '<td class="td" style="width: 33%">' . $row[1] . '</td>';
            $table2 .= '<td class="td" style="width: 33%">' . $row[2] . '</td>';
            $table2 .= '</tr>';
        }
        $table2 .= '</table>';

        $htmlTable = <<<EOD
         <style>
             tr { page-break-inside:avoid; page-break-after:auto }
             td { page-break-inside:avoid; page-break-after:auto }
             table{
              width: 100%;
              border-collapse: collapse;
              margin: 0 auto; /* 居中显示 */
              page-break-inside:auto
            }
            .th{
                height: 40px;
            }
            .td{
                
                font-size: 14px;
                line-height: 30px;
                text-align: center;
                width: 40%;
                border:1px solid #999;
            }
             .td-left{
                
                font-size: 14px;
                line-height: 30px;
                width: 56%;
                margin-left: 10px;
                border: 1px solid black;
            }
            .td-center{
                text-align: center;
                line-height: 30px;
                width:4%;
                border: 1px solid black;
            }
            .text-page2{
                line-height: 24px;
                text-align: left;
                 font-size: 14px;
            }
            .bold {
                font-weight: bold;
            }

        </style>
         <div style="font-size: 16px; text-align: center"><b>Part Two: Company Overview</b></div>
        <div class="text-page2">
          The Company Overview section provides a summary of the Company’s brief introduction, statutory information, key personnel(s), and ownership structure. The information in this section is based on official documents provided by the Company, public records, and third-party sources if applicable.
        </div>
        <div class="text-page2">
        <b>1.Brief Introduction</b><br>
        The Company established in $field1, is a member of the $field2 group. Its main production location is in $field3. It is an enterprise engaged in   $field4  , has been deeply rooted in $field4a. Its main products include $field5.
        <br>
        $field5a
        </div>
        <div class="text-page2">
        <b>2.Statutory Information</b><br>
        As of the Report Reference Date, the following statutory information has been provided by the Company and/or verified through public records.
        </div>
         <table align="center"  border="0">
            <tr class="th">
                <td class="td">Company Name</td>
                <td class="td-left"> $field6</td>
            </tr>
            <tr  class="th">
                <td class="td">Previous Company Name</td>
                <td class="td-left"> $field7</td>
            </tr>
            <tr  class="th">
                <td class="td">Company Registration Number</td>
                <td class="td-left"> $field8</td>
            </tr>
            <tr  class="th">
                <td class="td">Country of Registration</td>
                <td class="td-left"> $field9</td>
            </tr>
             <tr  class="th">
                <td class="td">Date of Incorporation</td>
                <td class="td-left"> $field10</td>
            </tr>
             <tr  class="th">
                <td class="td">Type of Entity</td>
                <td class="td-left"> $field11</td>
            </tr>
            <tr  class="th">
                <td class="td">Registered Capital</td>
                <td class="td-left"> $field12</td>
            </tr>
             <tr  class="th">
                <td class="td">Paid-in Capital</td>
                <td class="td-left"> $field13</td>
            </tr>
            <tr  class="th">
                <td class="td">Registered Address</td>
                <td class="td-left"> $field14</td>
            </tr>
            <tr  class="th">
                <td class="td">Business Address</td>
                <td class="td-left"> $field15</td>
            </tr>
             <tr  class="th">
                <td class="td">Scope of Business </td>
                <td class="td-left"> $field16</td>
            </tr>
             <tr  class="th">
                <td class="td">Latest Annual Return Date</td>
                <td class="td-left"> $field17 Annual Return</td>
            </tr>
            <tr  class="th">
                <td class="td">Business Period </td>
                <td class="td-left"> $field18</td>
            </tr>
        </table><br></br>
        <div class="text-page2">
         <b>3.Key Personnel(s) / Top Management</b><br>
         As of the Report Reference Date, the following individuals holds executive and management positions in the Company.
         </div>
         $table1
        <br>
         <div class="text-page2">
         <b>4.Ownership ( Top 5 Shareholders )</b><br>
           As of the Report Reference Date, the following shareholder(s) and shareholding ratio have been provided by the Company.
        </div>
        $table2
        <br>
        
        EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');


    }

    public function page6()
    {
        // 工作人员查看生成增加颜色
        if ($this->data['page6'] && $this->type==2) {
            foreach ($this->data['page6'] as $key => $val) {
                if (!is_array($val)) {
                    $this->data['page6'][$key] = '<span style="color:red">'.$val.'</span>';
                } else {
                    $redTable = [];
                    foreach ($val as  $row) {
                        foreach ($row as $index => $field ) {
                            $row[$index] = '<span style="color:red">'.$field.'</span>';
                        }
                        $redTable[] = $row;
                    }
                    $this->data['page6'][$key] = $redTable;
                }

            }
        }

        // 提取数组中的键值对为变量
        extract($this->data['page6']);

        $this->pdf->AddPage();

        // 主要产品类别
        $table1 = '
         <table  border="0">';
        $table1 .= '<tr class="th"><td class="td" style="width: 20%">Product Category</td><td class="td" style="width:60%">Description / Technical Details / Usage</td><td class="td" style="width: 20%">Certification</td></tr>';
        foreach ($this->data['page6']['field1'] as $row) {
            $table1 .= '<tr class="th">';
            $table1 .= '<td class="td" style="width: 20%">' . $row[0] . '</td>';
            $table1 .= '<td class="td" style="width: 60%">' . $row[1] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[2] . '</td>';
            $table1 .= '</tr>';
        }
        $table1 .= '</table>';

        // 2.前五大主要生产设备
        $table2 = '
         <table  border="0">';
        $table2 .= '<tr class="th"><td class="td" style="width:20%">Production Equipment</td><td class="td" style="width:20%">Purchase/First Lease Date</td><td class="td" style="width: 30%">Description / Technical Details / Usage</td><td class="td" style="width: 10%">Quantity</td><td class="td" style="width: 20%">Ownership</td></tr>';
        foreach ($this->data['page6']['field2'] as $row) {
            $table2 .= '<tr class="th">';
            $table2 .= '<td class="td" style="width: 20%">' . $row[0]. '</td>';
            $table2 .= '<td class="td" style="width: 20%">' . $row[1] . '</td>';
            $table2 .= '<td class="td" style="width: 30%">' . $row[2] . '</td>';
            $table2 .= '<td class="td" style="width: 10%">' . $row[3] . '</td>';
            $table2 .= '<td class="td" style="width: 20%">' . $row[4] . '</td>';
            $table2 .= '</tr>';
        }
        $table2 .= '</table>';

        // 3.生产及办公面积
        $table3 = '
         <table  border="0">';
        $table3 .= '<tr class="th"><td class="td" style="width:25%">Location</td><td class="td" style="width:25%">Usage</td><td class="td" style="width: 25%">Size</td><td class="td" style="width: 25%">Ownership</td></tr>';
        foreach ($this->data['page6']['field3'] as $row) {
            $table3 .= '<tr class="th">';
            $table3 .= '<td class="td" style="width: 25%">' . $row[0] . '</td>';
            $table3 .= '<td class="td" style="width: 25%">' . $row[1] . '</td>';
            $table3 .= '<td class="td" style="width: 25%">' . $row[2] . '</td>';
            $table3 .= '<td class="td" style="width: 25%">' . $row[3] . '</td>';
            $table3 .= '</tr>';
        }
        $table3 .= '</table>';

        // 5.生产与交付
        $table4 = '
         <table  border="0">';
        $table4 .= '<tr class="th"><td class="td" style="width:25%">Production Line / Facility</td><td class="td" style="width:25%">Average Production Time</td><td class="td" style="width: 25%">Typical Delivery Time</td><td class="td" style="width: 25%">Remark</td></tr>';
        foreach ($this->data['page6']['field9'] as $row) {
            $table4 .= '<tr class="th">';
            $table4 .= '<td class="td" style="width: 25%">' . $row[0] . '</td>';
            $table4 .= '<td class="td" style="width: 25%">' . $row[1] . '</td>';
            $table4 .= '<td class="td" style="width: 25%">' . $row[2] . '</td>';
            $table4 .= '<td class="td" style="width: 25%">' . $row[3] . '</td>';
            $table4 .= '</tr>';
        }
        $table4 .= '</table>';

        // 6.质量保证与控制
        $table5 = '
         <table  border="0">';
        $table5 .= '<tr class="th"><td class="td" style="width:50%">Quality Control Process</td><td class="td" style="width:50%">Description / Technical Details</td></tr>';
        foreach ($this->data['page6']['field15'] as $row) {
            $table5 .= '<tr class="th">';
            $table5 .= '<td class="td" style="width: 50%">' . $row[0] . '</td>';
            $table5 .= '<td class="td" style="width: 50%">' . $row[1] . '</td>';
            $table5 .= '</tr>';
        }
        $table5 .= '</table>';


        // 7.前十主要原材料及来源占比
        $table6 = '
         <table  border="0">';
        $table6 .= '<tr class="th"><td class="td" style="width:25%">Raw Material</td><td class="td" style="width:25%">Source Country/Area</td><td class="td" style="width: 25%">Proportion in Particular Product</td><td class="td" style="width: 25%">Key Supplier</td></tr>';
        foreach ($this->data['page6']['field16'] as $row) {
            $table6 .= '<tr class="th">';
            $table6 .= '<td class="td" style="width: 25%">' . $row[0] . '</td>';
            $table6 .= '<td class="td" style="width: 25%">' . $row[1] . '</td>';
            $table6 .= '<td class="td" style="width: 25%">' . $row[2] . '</td>';
            $table6 .= '<td class="td" style="width: 25%">' . $row[3] . '</td>';
            $table6 .= '</tr>';
        }
        $table6 .= '</table>';

        // 8.售后服务及保证
        $table7 = '
         <table  border="0">';
        $table7 .= '<tr class="th">';
        $table7 .= '<td class="td" style="width: 50%">After Sales Service</td>';
        $table7 .= '<td class="td" style="width: 50%">Description</td>';
        $table7 .= '</tr>';
        $table7 .= '<tr class="th">';
        $table7 .= '<td class="td" style="width: 50%">Service Standard</td>';
        $table7 .= '<td class="td" style="width: 50%">' . $field17 . '</td>';
        $table7 .= '</tr>';
        $table7 .= '<tr class="th">';
        $table7 .= '<td class="td" style="width: 50%">Service Mode</td>';
        $table7 .= '<td class="td" style="width: 50%">' . $field18 . '</td>';
        $table7 .= '</tr>';
        $table7 .= '<tr class="th">';
        $table7 .= '<td class="td" style="width: 50%">Response Time</td>';
        $table7 .= '<td class="td" style="width: 50%">' . $field19 . '</td>';
        $table7 .= '</tr>';
        $table7 .= '<tr class="th">';
        $table7 .= '<td class="td" style="width: 50%">Coverage Area</td>';
        $table7 .= '<td class="td" style="width: 50%">' . $field20 . '</td>';
        $table7 .= '</tr>';
        $table7 .= '</table>';

        $htmlTable = <<<EOD
         <style>
             tr { page-break-inside:avoid; page-break-after:auto }
             td { page-break-inside:avoid; page-break-after:auto }
             table{
              width: 100%;
              border-collapse: collapse;
              margin: 0 auto; /* 居中显示 */
              page-break-inside:auto
            }
            .th{
                height: 40px;
            }
            .td{
                
                font-size: 14px;
                line-height: 30px;
                text-align: center;
                width: 40%;
                border:1px solid #999;
            }
             .td-left{
                
                font-size: 14px;
                line-height: 30px;
                width: 56%;
                margin-left: 10px;
                border: 1px solid black;
            }
            .td-center{
                text-align: center;
                line-height: 30px;
                width:4%;
                border: 1px solid black;
            }
            .text-page2{
                line-height: 24px;
                text-align: left;
                font-size: 14px;
            }
            .bold {
                font-weight: bold;
            }

        </style>
         <div style="font-size: 16px; text-align: center"><b>Part Three: Business Overview</b></div>
        <div class="text-page2">
        The Business Overview section provides a summary into the Company’s main product categories, major production equipment, area size of production and office, number of employees, production capacity and delivery capability, quality control measure(s), main raw materials and after sales service. This section is based on data provided by the Company and/or verified through available third-party sources if applicable.
        </div>
        <div class="text-page2">
         <b>1.Main Product Categories</b><br>
         As of the Report Reference Date, the Company has reported the following primary product categories and technical specifications.
        </div>
        $table1
        <br>
        <div class="text-page2">
         <b>2.Top 5 Major Production Equipment</b><br>
         As of the Report Reference Date, the following major production equipment are reported to be in use at the Company’s production facilities.
        </div>
        $table2
        <br>
        <div class="text-page2">
        <b>3.Area Size of Production and Office</b><br>
        As of the Report Reference Date, the Company operates from the following production and office site(s).
        </div>
        <div style="text-align: right">Unit:square metre</div>
        $table3
        <br>
        <div class="text-page2">
        <b>4.Number of Employees</b><br>
         As of $field4 / $field5 / $field6, the total number of employees in the Company is $field7 and the actual number covered by social insurance is $field8.
        </div>
        <br>
        <div class="text-page2">
        <b>5.Production Capacity and Delivery Capability </b><br>
        (1)As of the Report Reference Date, the following table outlines the Company’s production capacity and delivery capability.<br>
        </div>
        $table4
        <br>
        <div class="text-page2">
        (2)The annual production capacity calculation formula of the Company is: $field10. According to this calculation of this formula, the annual production capacity of the Company in $field11 is $field12.<br>
        (3)The inventory amount of the Company for the $field13 is CNY $field14.
        </div>
        <br>
        <div class="text-page2">
        <b>6.Quality Control Measure(s)</b><br>
         As of the Report Reference Date, the Company has reported the following Quality Control process(es) for ensuring product consistency and compliance.
        </div>
        $table5
        <br>
        <div class="text-page2">
        <b>7.Top 10 Main Raw Materials and the Source Proportions</b><br>
        As of the Report Reference Date, the following countries and suppliers have been identified as the main sources of raw materials for production.
        </div>
        $table6
        <br>
        <div class="text-page2">
        <b>8.After Sales Service and Guarantee/Warranties</b><br>
        As of the Report Reference Date, the Company has reported the following after sales service and guarantee/warranties.
        </div>
        $table7
        <br>
        EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');


    }

    public function page7()
    {
        // 工作人员查看生成增加颜色
        if ($this->data['page7'] && $this->type==2) {
            foreach ($this->data['page7'] as $key => $val) {
                if (!is_array($val)) {
                    $this->data['page7'][$key] = '<span style="color:red">'.$val.'</span>';
                } else {
                    $redTable = [];
                    foreach ($val as  $row) {
                        foreach ($row as $index => $field ) {
                            $row[$index] = '<span style="color:red">'.$field.'</span>';
                        }
                        $redTable[] = $row;
                    }
                    $this->data['page7'][$key] = $redTable;
                }

            }
        }

        // 提取数组中的键值对为变量
        extract($this->data['page7']);

        $this->pdf->AddPage();

        // 主要产品类别
        $table1 = '
         <table  border="0">';
        $table1 .= '<tr class="th"><td class="td" style="width: 20%">Major Client</td><td class="td" style="width:20%">Country/Area</td><td class="td" style="width: 20%">Industry</td><td class="td" style="width: 20%">Annual Transaction Value</td><td class="td" style="width: 20%">Remark</td></tr>';
        foreach ($this->data['page7']['field1'] as $row) {
            $table1 .= '<tr class="th">';
            $table1 .= '<td class="td" style="width: 20%">' . $row[0] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[1] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[2] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[3] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[4] . '</td>';
            $table1 .= '</tr>';
        }
        $table1 .= '</table>';

        // 2.产品应用案例
        $table2 = '
         <table  border="0">';
        $table2 .= '<tr class="th"><td class="td" style="width: 25%">Project</td><td class="td" style="width:25%">Country/Area</td><td class="td" style="width: 25%">Product Supplied</td><td class="td" style="width: 25%">Supply Value</td></tr>';
        foreach ($this->data['page7']['field2'] as $row) {
            $table2 .= '<tr class="th">';
            $table2 .= '<td class="td" style="width: 25%">' . $row[0] . '</td>';
            $table2 .= '<td class="td" style="width: 25%">' . $row[1] . '</td>';
            $table2 .= '<td class="td" style="width: 25%">' . $row[2] . '</td>';
            $table2 .= '<td class="td" style="width: 25%">' . $row[3] . '</td>';
            $table2 .= '</tr>';
        }
        $table2 .= '</table>';

        //3.所获荣誉及表扬信
        $table3 = '
         <table  border="0">';
        $table3 .= '<tr class="th"><td class="td" style="width: 33%">Recognition Title</td><td class="td" style="width:33%">Date of Received</td><td class="td" style="width: 33%">Issuing Unit</td></tr>';
        foreach ($this->data['page7']['field3'] as $row) {
            $table3 .= '<tr class="th">';
            $table3 .= '<td class="td" style="width: 33%">' . $row[0] . '</td>';
            $table3 .= '<td class="td" style="width: 33%">' . $row[1] . '</td>';
            $table3 .= '<td class="td" style="width: 33%">' . $row[2] . '</td>';
            $table3 .= '</tr>';
        }
        $table3 .= '</table>';

        $table4 = '
         <table  border="0">';
        $table4 .= '<tr class="th"><td class="td" style="width: 33%">Issuing Unit</td><td class="td" style="width:33%">Project Involved</td><td class="td" style="width: 33%"> Date of Issuance</td></tr>';
        foreach ($this->data['page7']['field4'] as $row) {
            $table4 .= '<tr class="th">';
            $table4 .= '<td class="td" style="width: 33%">' . $row[0] . '</td>';
            $table4 .= '<td class="td" style="width: 33%">' . $row[1] . '</td>';
            $table4 .= '<td class="td" style="width: 33%">' . $row[2] . '</td>';
            $table4 .= '</tr>';
        }
        $table4 .= '</table>';


        $htmlTable = <<<EOD
            <style>
             tr { page-break-inside:avoid; page-break-after:auto }
             td { page-break-inside:avoid; page-break-after:auto }
             table{
              width: 100%;
              border-collapse: collapse;
              margin: 0 auto; /* 居中显示 */
              page-break-inside:auto
            }
            .th{
                height: 40px;
            }
            .td{
                
                font-size: 14px;
                line-height: 30px;
                text-align: center;
                width: 40%;
                border:1px solid #999;
            }
             .td-left{
                
                font-size: 14px;
                line-height: 30px;
                width: 56%;
                margin-left: 10px;
                border: 1px solid black;
            }
            .td-center{
                text-align: center;
                line-height: 30px;
                width:4%;
                border: 1px solid black;
            }
            .text-page2{
                line-height: 24px;
                text-align: left;
                font-size: 14px;
            }
            .bold {
                font-weight: bold;
            }

        </style>
         <div style="font-size: 16px; text-align: center"><b>Part Four: Clients/Customers and Track Record(s)</b></div>
        <div class="text-page2">
        The Clients/Customers and Track Record(s) section provides a summary into the Company’s major clients, product application case(s) and recognition. The information in this section is based on data provided by the Company, publicly available sources, and third-party verification if applicable.
        </div>
        <div class="text-page2">
        <b>1.Top 5 Major Clients/Customers</b><br>
        As of the Report Reference Date, the following companies have been identified as the Company’s major clients/customers based on business transactions, supply agreements,  long-term partnerships, etc.
        </div>
        <div style="text-align: right">Currency:CNY</div>
        $table1
        <br>
        <div class="text-page2">
        <b>2.Product Application/Use Case(s)</b><br>
        As of the Report Reference Date, the following products have been supplied for major project(s) or client(s). These cases demonstrate the Company's historical performance and reliability in fulfilling contractual obligations.
        </div>
        <div style="text-align: right">Currency:CNY</div>
        $table2
        <br>
        <div class="text-page2">
        <b>3.Recognition and Recommendation Letter(s) Received</b><br>
        As of the Report Reference Date, the Company has reported the following recognition title(s) and recommendation letter(s).<br>
        (1)Recognition:<br>
         $table3 <br><br>
        (2)Recommendation Letter(s):<br>
         $table4
        </div>
        <br>
        EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');


    }

    public function page8()
    {
        // 工作人员查看生成增加颜色
        if ($this->data['page8'] && $this->type==2) {
            foreach ($this->data['page8'] as $key => $val) {
                if (!is_array($val)) {
                    $this->data['page8'][$key] = '<span style="color:red">'.$val.'</span>';
                } else {
                    $redTable = [];
                    foreach ($val as  $row) {
                        foreach ($row as $index => $field ) {
                            $row[$index] = '<span style="color:red">'.$field.'</span>';
                        }
                        $redTable[] = $row;
                    }
                    $this->data['page8'][$key] = $redTable;
                }

            }
        }

        // 提取数组中的键值对为变量
        extract($this->data['page8']);

        $this->pdf->AddPage();


        // 1.资质及认证
        $table1 = '
         <table  border="0">';
        $table1 .= '<tr class="th"><td class="td" style="width: 20%">Certification</td><td class="td" style="width:20%">Issuing Authority</td><td class="td" style="width: 20%">Issued Date</td><td class="td" style="width: 20%">Expiry Date</td><td class="td" style="width: 20%">Scope of Certification</td></tr>';
        foreach ($this->data['page8']['field1'] as $row) {
            $table1 .= '<tr class="th">';
            $table1 .= '<td class="td" style="width: 20%">' . $row[0] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[1] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[2] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[3] . '</td>';
            $table1 .= '<td class="td" style="width: 20%">' . $row[4] . '</td>';
            $table1 .= '</tr>';
        }
        $table1 .= '</table>';

        // 知识产权
        $table2 = '
         <table  border="0">';
        $table2 .= '<tr class="th"><td class="td" style="width: 50%">Name/Picture of Intellectual Property</td><td class="td" style="width:50%">Type of Intellectual Property</td></tr>';
        foreach ($this->data['page8']['field2'] as $row) {

            $img = public_path('storage/'.$row[0]);
            if (is_file($img) && file_exists($img)) {
                $image = '<img src="'.$img.'" height="50">';
            } else {
                $image = $row[0];
            }
            $table2 .= '<tr class="th">';
            $table2 .= '<td class="td" style="width: 50%;">'. $image .'</td>';
            $table2 .= '<td class="td" style="width: 50%">' . $row[1] . '</td>';
            /*$table2 .= '<td class="td" style="width: 20%">' . htmlspecialchars($row[3]) . '</td>';
            $table2 .= '<td class="td" style="width: 20%">' . htmlspecialchars($row[4]) . '</td>';
            $table2 .= '<td class="td" style="width: 20%">' . htmlspecialchars($row[2]) . '</td>';*/
            $table2 .= '</tr>';
        }
        $table2 .= '</table>';

        // 3.重大合同
        $table3 = '
         <table  border="0">';
        $table3 .= '<tr class="th"><td class="td" style="width: 20%">Contract Type</td><td class="td" style="width:20%">Main Content</td><td class="td" style="width: 20%">Contract Value</td><td class="td" style="width: 20%">Signing Date</td><td class="td" style="width: 20%">Status</td></tr>';
        foreach ($this->data['page8']['field3'] as $row) {

            $table3 .= '<tr class="th">';
            $table3 .= '<td class="td" style="width: 20%;">'.$row[0].'</td>';
            $table3 .= '<td class="td" style="width: 20%">' . $row[1] . '</td>';
            $table3 .= '<td class="td" style="width: 20%">' . $row[2] . '</td>';
            $table3 .= '<td class="td" style="width: 20%">' . $row[3] . '</td>';
            $table3 .= '<td class="td" style="width: 20%">' . $row[4] . '</td>';
            $table3 .= '</tr>';
        }
        $table3 .= '</table>';

        //4.重大诉讼及仲裁信息
        $table4 = '
         <table  border="0">';
        $table4 .= '<tr class="th"><td class="td" style="width: 20%">Case Name</td><td class="td" style="width:20%">Type of Case</td><td class="td" style="width: 20%">Filing Date</td><td class="td" style="width: 20%">Accepting Institution</td><td class="td" style="width: 20%">Value in Dispute</td></tr>';
        foreach ($this->data['page8']['field4'] as $row) {

            $table4 .= '<tr class="th">';
            $table4 .= '<td class="td" style="width: 20%;">'.$row[0].'</td>';
            $table4 .= '<td class="td" style="width: 20%">' . $row[1] . '</td>';
            $table4 .= '<td class="td" style="width: 20%">' . $row[2] . '</td>';
            $table4 .= '<td class="td" style="width: 20%">' . $row[3] . '</td>';
            $table4 .= '<td class="td" style="width: 20%">' . $row[4] . '</td>';
            $table4 .= '</tr>';
        }
        $table4 .= '</table>';

        //5.行政处罚记录
        $table5 = '
         <table  border="0">';
        $table5 .= '<tr class="th"><td class="td" style="width: 25%">Reason of Penalty</td><td class="td" style="width:25%">Penalty Sentence</td><td class="td" style="width: 25%">Effective Date</td><td class="td" style="width: 25%">Administrative Authority</td></tr>';
        foreach ($this->data['page8']['field5'] as $row) {

            $table5 .= '<tr class="th">';
            $table5 .= '<td class="td" style="width: 25%;">'.$row[0].'</td>';
            $table5 .= '<td class="td" style="width: 25%">' . $row[1]. '</td>';
            $table5 .= '<td class="td" style="width: 25%">' . $row[2] . '</td>';
            $table5 .= '<td class="td" style="width: 25%">' . $row[3] . '</td>';
            $table5 .= '</tr>';
        }
        $table5 .= '</table>';

        //6.失信记录
        $table6 = '
         <table  border="0">';
        $table6 .= '<tr class="th"><td class="td" style="width: 50%">Incident</td><td class="td" style="width:50%">Status</td></tr>';
        foreach ($this->data['page8']['field6'] as $row) {

            $table6 .= '<tr class="th">';
            $table6 .= '<td class="td" style="width: 50%;">'.$row[0].'</td>';
            $table6 .= '<td class="td" style="width: 50%">' . $row[1] . '</td>';
/*            $table6 .= '<td class="td" style="width: 20%">' . htmlspecialchars($row[3]) . '</td>';
            $table6 .= '<td class="td" style="width: 20%">' . htmlspecialchars($row[4]) . '</td>';
            $table6 .= '<td class="td" style="width: 20%">' . htmlspecialchars($row[2]) . '</td>';*/
            $table6 .= '</tr>';
        }
        $table6 .= '</table>';


        $htmlTable = <<<EOD
            <style>
              tr { page-break-inside:avoid; page-break-after:auto }
             td { page-break-inside:avoid; page-break-after:auto }
             table{
              width: 100%;
              border-collapse: collapse;
              margin: 0 auto; /* 居中显示 */
              page-break-inside:auto
            }
            .th{
                height: 40px;
            }
            .td{
                
                font-size: 14px;
                line-height: 30px;
                text-align: center;
                width: 40%;
                border:1px solid #999;
            }
             .td-left{
                
                font-size: 14px;
                line-height: 30px;
                width: 56%;
                margin-left: 10px;
                border: 1px solid black;
            }
            .td-center{
                text-align: center;
                line-height: 30px;
                width:4%;
                border: 1px solid black;
            }
            .text-page2{
                line-height: 24px;
                text-align: left;
                font-size: 14px;
            }
            .bold {
                font-weight: bold;
            }

        </style>
         <div style="font-size: 16px; text-align: center"><b>Part Five: Legal affairs and Compliance</b></div>
        <div class="text-page2">
        The Legal Affairs and Compliance section provides insights into the Company’s certification, intellectual property assets, major contracts, major lawsuits and arbitration, administrative penalty records and credit information. The information presented in this section is based on data provided by the Company, publicly available legal records, and third-party verification if applicable.
        </div>
        <div class="text-page2">
         <b>1.Qualification(s) and Certification</b><br>
         As of the Report Reference Date, the following qualification(s) and certification approvals have been obtained by the Company.
        </div>
        $table1
        <br>
        <div class="text-page2">
         <b>2.Intellectual Property</b><br>
         As of the Report Reference Date, the following intellectual property right(s) is/are held by the Company.
        </div>
        $table2
        <br>
        <div class="text-page2">
         <b>3.Major Contract(s)</b><br>
        As of the Report Reference Date, contract(s) that have a significant impact on the company's business activities, financial condition or future development are currently being fulfilled or will be fulfilled. The criteria or standard for determining major contracts is: contracts or annual framework agreements that account for 10% or more of the Company's estimated annual transaction value. 
        </div>
        <div style="text-align: right">Currency:CNY</div>
        $table3
        <br>
        <div class="text-page2">
        <b>4.Major Lawsuits and Arbitration</b><br>
        As of the Report Reference Date, the following lawsuit(s) and arbitration case(s) involving the  Company and its holding subsidiaries with a value in dispute exceeding 20% of the Company's total assets in the past three years (cases involving shareholders of the Company are not within the scope of this investigation).
        </div>
        <div style="text-align: right">Currency:CNY</div>
        $table4
        <br>
         <div class="text-page2">
         <b>5.Administrative Penalty Record(s)</b><br>
         As of the Report Reference Date, the Company has the following unresolved administrative penalty record(s).
        </div>
        $table5
        <br>
        <div class="text-page2">
        <b>6.Credit Information</b><br>
        As of the Report Reference Date, the Company has the following breach of trust incident(s).
        </div>
         <div style="text-align: right">Currency:CNY</div>
         $table6
        <br>
        EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

    }

    public function page9()
    {

        // 提取数组中的键值对为变量

        $this->pdf->AddPage();
        $field4 = $this->data['page9']['field4'];
        if ($this->type == 2) {
            $field4 = '<span style="color:red">'.$field4.'<span/>';
        }

        $table1 = $this->incomeStatement();
        $table2 = $this->cashFlow();
        $table3 = $this->balanceSheet();

        $htmlTable = <<<EOD
         <style>
             tr { page-break-inside:avoid; page-break-after:auto }
             td { page-break-inside:avoid; page-break-after:auto }
             table{
              width: 100%;
              border-collapse: collapse;
              margin: 0 auto; /* 居中显示 */
              page-break-inside:auto
            }
            .th{
                height: 40px;
            }
            .td{
                
                font-size: 14px;
                line-height: 30px;
                text-align: center;
                width: 40%;
                border:1px solid #999;
            }
             .td-left{
                
                font-size: 14px;
                line-height: 30px;
                width: 56%;
                margin-left: 10px;
                border: 1px solid black;
            }
            .td-center{
                text-align: center;
                line-height: 30px;
                width:4%;
                border: 1px solid black;
            }
            .text-page2{
                line-height: 24px;
                text-align: left;
                font-size: 14px;
            }
            .bold {
                font-weight: bold;
            }

        </style>
         <div style="font-size: 16px; text-align: center"><b>Part Six: Financial Information</b></div>
        <div class="text-page2">
        The Financial Information provides insights into the Company’s Statement of Comprehensive Income, Statement of Financial Position and Statement of Cash Flows. The information presented in this section is based on data provided by the Company and third-party verification if applicable.
        </div>
        <div class="text-page2">
         <b>1.Statement of Comprehensive Income </b><br>
         As of the Report Reference Date, the following data have been excerpted from the Audit Report issued by $field4  entrusted by the Company.
        </div>
        <div style="text-align: right">Currency:CNY</div>
        $table1
        <br>
        <div class="text-page2">
         <b>2.Statement of Financial Position</b><br>
         As of the Report Reference Date, the following data have been excerpted from the Audit Report issued by [Name of Accounting Firm] entrusted by the Company.
        </div>
        <div style="text-align: right">Currency:CNY</div>
        $table3
        <br>
        <div class="text-page2">
        <b>3.Statement of Cash Flows </b><br>
        As of the Report Reference Date, the following data have been excerpted from the Audit Report issued by [Name of Accounting Firm] entrusted by the Company.
        </div>
        <div style="text-align: right">Currency:CNY</div>
        $table2
        <br>
        EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

    }

    public function page10()
    {
        // 提取数组中的键值对为变量
        extract($this->data['page10']);

        $this->pdf->AddPage();

        if ($this->type == 2) {
            $field1 = '<span style="color:red">'.$field1.'</span>';
        }

        $htmlTable = <<<EOD
        <style>
            .text-page2{
                line-height: 24px;
                text-align: left;
                font-size: 14px;
            }
            .bold {
                font-weight: bold;
            }

        </style>
         <div style="font-size: 16px; text-align: center"><b>Part Seven: Future Plan(s)</b></div>
        <div class="text-page2">
            The Future Plan(s) section provides insights into the Company’s development strategies, implementation effects and specific measures taken. The following information is  provided by the Company. This report only contains excerpts.
        </div>
        <div class="text-page2">
         $field1
        </div>
        EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

    }

    public function page11()
    {
        $this->pdf->AddPage();

        // 收集临时文件路径
        $tmpFile = [];

        foreach ($this->data['page11'] as $key => $value) {
            $$key = '<div style="text-align: left">';
            if ($value) {
                foreach ($value as $file) {
                    // 获取文件名的后缀
                    $extension = strtolower(substr($file, -4));
                    if ($extension === '.pdf') {
                        if (Storage::disk('public')->exists($file)) {
                            $pdfFile = viewFileUrl($file);
                        } else {
                            $pdfFile = OssService::link($file);
                        }
                        // $pdfFile =  storage_path('app/public/'.$file);
                        $outPutPath = storage_path('app/public/gsp/report/tmp/');
                        $imgArr = $this->convertPdfToImages($pdfFile, $outPutPath);
                        if ($imgArr) {
                            foreach ($imgArr as $img) {
                                $file = public_path('storage/').$img;
                                if (is_file($file) && file_exists($file)) {
                                    $$key .= '<Br><img src="'.$file.'" width="300">';
                                    $tmpFile[] = storage_path('app/public/').$img;;
                                }
                            }
                        }
                    } else if (in_array($extension, ['.jpg','.jpeg', '.png'])){
                        if (Storage::disk('public')->exists($file)) {
                            $file = viewFileUrl($file);
                        } else {
                            $file = OssService::link($file);
                        }
                        //$file = public_path('storage/').$file;
                        // 获取 HTTP 响应头
                        $headers = @get_headers($file);
                        // 检查是否成功获取头信息
                        if ($headers && strpos($headers[0], '200 OK') !== false) {
                            $$key .= '<img src="'.$file.'" width="300">';
                        }
                    }

                }
            }

            $$key .= '</div>';
        }



        $htmlTable = <<<EOD
        <style>
            .text-page2{
                line-height: 24px;
                text-align: left;
                font-size: 14px;
            }
            .bold {
                font-weight: bold;
            }

        </style>
         <div style="font-size: 16px; text-align: center"><b>Part Eight: Attachment</b></div>
        <div class="text-page2">
        <b>1.Company Information</b><br>
        (1)Business License<br>
        $field1<br>
        (2)Enterprise Credit Information Disclosure Report<br>
        $field2<br>
        (3)Social Insurance Employer Registration Certificate<br>
        $field3<br>
        (4)Credit Information Report<br>
        $field4<br>
        </div>
        <div class="text-page2">
        <b>2.Business Qualifications and Recognition</b><br>
        (1)Qualification(s) and Certification<Br>
        $field5<br>
        (2)Recognition and Recommendation Letter(s) Received<Br>
        $field6<br>
        </div>
        <div class="text-page2">
        <b>3.Certificate of Rights</b><br>
        (1)Registered Trademark Certificate(s)<br>
        $field7<br>
        (2)Patent Certificate(s)<br>
        $field8<br>
        (3)Copyright/Software Copyright Registration Certificate(s)<br>
        $field9<br>
        (4)Real Estate Certificate(s)/Lease Contract for Office and Production Sites<br>
        $field10<br>
        </div>
        <div class="text-page2">
        <b>4.On-site Photos of the Company</b><br>
        (1)Photos of Main Products<br>
        $field11<br>
        (2)Photos of Major Production Equipment<br>
        $field12<br>
        (3)Photos of Production and Office Site(s)<br>
        $field13<br>
        </div>
        <div class="text-page2">
        <b>5.Audit Report</b><br>
        $field14<br>
        </div>
        <div class="text-page2">
        <b>6.Declaration of the Company</b><br>
        (1)Declaration of the Company's Controlling Shareholder and Actual Controller<br>
        $field15<br>
        (2)Declaration of All Directors, Supervisors, and Senior Executives of the Company<br>
        $field16<br>
        </div>
        EOD;

        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

        // 清空临时图片
        if ($tmpFile) {
            foreach ($tmpFile as $item) {
                unlink($item);
            }
        }
    }

    /**
     * 利润表
     * @return string
     */
    public function incomeStatement()
    {

        // 利润表
        foreach ($this->data['page9']['field1'][0] as $key => $value) {
            $newKey = $key . '1';
            if (is_numeric($value)) {
                if ($value < 0) {
                    $value = abs($value);
                    $$newKey = number_format($value, 2);
                    if ($this->type == 2) {
                        $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                    }
                } else {
                    if ($key != 'year') {
                        $$newKey = number_format($value, 2);
                    } else {
                        $$newKey = $value;
                    }
                    if ($this->type == 2) {
                        $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                    }
                }
            }

        }

        foreach ($this->data['page9']['field1'][1] as $key => $value) {
            $newKey = $key . '2';
            if (is_numeric($value)) {
                if ($value < 0) {
                    $value = abs($value);
                    $$newKey = number_format($value, 2);
                    if ($this->type == 2) {
                        $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                    }
                } else {
                    if ($key != 'year') {
                        $$newKey = number_format($value, 2);
                    } else {
                        $$newKey = $value;
                    }
                    if ($this->type == 2) {
                        $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                    }
                }
            }

        }




        // 合并单元格
        $html = <<<EOD
        <table border="1" cellpadding="2">
           <tr>
                <td align="center">Item</td>
                <td align="center">$year1</td>
                <td align="center">$year2</td>
           </tr>
           <tr>
                <td></td>
                <td align="center">YUAN</td>
                <td align="center">YUAN</td>
           </tr>
           <tr>
                <td>REVENUE</td>
                <td align="right">$revenue1</td>
                <td align="right">$revenue2</td>
           </tr>
           <tr>
                <td>Cost of Goods Sold (COGS) / Cost of Sales (COS)</td>
                <td align="right">$cogs1</td>
                <td align="right">$cogs2</td>
           </tr>
           <tr>
                <td>GROSS PROFIT</td>
                <td align="right">$gross_profit1</td>
                <td align="right">$gross_profit2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td>Research and Development Expense</td>
                <td align="right">$research_and_development_expense1</td>
                <td align="right">$research_and_development_expense2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>Selling, General and Administrative Expense</td>
                <td align="right">$general_and_administrative_expense1</td>
                <td align="right">$general_and_administrative_expense2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>Other Operating Expenses </td>
                <td align="right">$other_operating_expenses1</td>
                <td align="right">$other_operating_expenses2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>Other Operating Income</td>
                <td align="right">$other_operating_income1</td>
                <td align="right">$other_operating_income2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>OPRATING PROFIT</td>
                <td align="right">$oprating_profit1</td>
                <td align="right">$oprating_profit2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>Finance Income</td>
                <td align="right">$finance_income1</td>
                <td align="right">$finance_income2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>Finance Expenses</td>
                <td align="right">$finance_expenses1</td>
                <td align="right">$finance_expenses2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>Share of Net Profit/(Loss) of Joint Ventures & Associates</td>
                <td align="right">$share_of_net_profit1</td>
                <td align="right">$share_of_net_profit2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>PROFIT BEFORE TAX</td>
                <td align="right">$profit_before_tax1</td>
                <td align="right">$profit_before_tax2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>Less:Taxation</td>
                <td align="right">$taxation1</td>
                <td align="right">$taxation2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>PROFIT FOR THE PERIOD</td>
                <td align="right">$profit_for_the_period1</td>
                <td align="right">$profit_for_the_period2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>Other Comprehensive Income</td>
                <td align="right">$other_comprehensive_income1</td>
                <td align="right">$other_comprehensive_income2</td>
            </tr>
             <tr>
                <td>Items that will not be Reclassified to Profit and Loss:</td>
                <td align="right">$items_that_will_not_be_reclassified_to_profit_and_loss1</td>
                <td align="right">$items_that_will_not_be_reclassified_to_profit_and_loss2</td>
            </tr>
            <tr>
                <td>Other Comprehensive Income</td>
                <td align="right">$other_comprehensive_income_all1</td>
                <td align="right">$other_comprehensive_income_all2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>Items that may be Reclassified Subsequently to Profit and Loss:</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>Gains/(Losses) on Cash Flow Hedges</td>
                <td align="right">$gains_losses_on_cash_flow_hedges1</td>
                <td align="right">$gains_losses_on_cash_flow_hedges2</td>
            </tr>
            <tr>
                <td>Currency Revaluation Gains/(Losses)</td>
                <td align="right">$currency_retranslation_gains1</td>
                <td align="right">$currency_retranslation_gains2</td>
            </tr>
            <tr>
                <td>Other Comprehensive (Expense)/Income for the Period, Net of Tax</td>
                <td align="right">$other_comprehensive_expense1</td>
                <td align="right">$other_comprehensive_expense2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>Total Comprehensive Income for the Period</td>
                <td align="right">$total_comprehensive_income_for_the_period1</td>
                <td align="right">$total_comprehensive_income_for_the_period2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
        </table>'
        EOD;

        return $html;

    }

    /**
     * 现金流量表
     * @return void
     */
    public function cashFlow()
    {

        foreach ($this->data['page9']['field2'][0] as $key => $value) {
            $newKey = $key . '1';
            if ($value < 0) {
                $value = abs($value);
                $$newKey = number_format($value, 2);
                if ($this->type ==  2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            } else {
                if ($key != 'year') {
                    $$newKey = number_format($value, 2);
                } else {
                    $$newKey = $value;
                }

                if ($this->type ==  2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            }
        }

        foreach ($this->data['page9']['field2'][1] as $key => $value) {
            $newKey = $key . '2';
            if ($value < 0) {
                $value = abs($value);
                $$newKey = number_format($value, 2);
                if ($this->type ==  2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            } else {
                if ($key != 'year') {
                    $$newKey = number_format($value, 2);
                } else {
                    $$newKey = $value;
                }

                if ($this->type ==  2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            }
        }


        // 合并单元格
        $html = <<<EOD
        <table border="1" cellpadding="2">
           <tr>
                <td align="center">Item</td>
                <td align="center">$year1</td>
                <td align="center">$year2</td>
           </tr>
           <tr>
                <td></td>
                <td  align="center">YUAN</td>
                <td  align="center">YUAN</td>
           </tr>
            <tr>
                <td>CASH FLOW FROM OPERATING ACTIVITIES</td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td>Cash Received from the Sale of Goods and the Provision of Services</td>
                <td align="right">$cash_received_from_the_sale1</td>
                <td align="right">$cash_received_from_the_sale2</td>
           </tr>
            <tr>
                <td>Tax Refunds Received</td>
                <td align="right">$tax_refunds_received1</td>
                <td align="right">$tax_refunds_received2</td>
           </tr>
            <tr>
                <td>Other Cash Received in connection with Operating Activities</td>
                <td align="right">$other_cash_received_in_connection_with_operating_activities1</td>
                <td align="right">$other_cash_received_in_connection_with_operating_activities2</td>
           </tr>
           <tr>
                <td>Subtotal Cash Inflows from Operating Activities</td>
                <td align="right">$subtotal_cash_inflows_from_operating_activities1</td>
                <td align="right">$subtotal_cash_inflows_from_operating_activities2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>Cash for the Purchase of Goods and Payment for Services</td>
                <td align="right">$cash_for_the_purchase1</td>
                <td align="right">$cash_for_the_purchase2</td>
           </tr>
           <tr>
                <td>Cash Paid to and on Behalf of Employees</td>
                <td align="right">$cash_paid_to_and_on_behalf1</td>
                <td align="right">$cash_paid_to_and_on_behalf2</td>
           </tr>
           <tr>
                <td>Taxes and Fees Paid</td>
                <td align="right">$taxes_and_fees_paid1</td>
                <td align="right">$taxes_and_fees_paid2</td>
           </tr>
           <tr>
                <td>Payment of other Cash Related to Operating Activities</td>
                <td align="right">$payment_of_other_cash_related_to_operating_activities1</td>
                <td align="right">$payment_of_other_cash_related_to_operating_activities2</td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td><b>Subtotal Cash Outflows from Operating Activities</b></td>
                <td align="right">$subtotal_cash_otflows_from_operating_activities1</td>
                <td align="right">$subtotal_cash_otflows_from_operating_activities2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td><b>CASH FLOW FROM INVESTING ACTIVITIES</b></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td><b>Proceeds from Disposal of Fixed Assets:</b></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>Proceeds from Sale of Plant, property and equipment and proceeds from long term investment</td>
                <td align="right">$proceeds_from_sale_of_plant1</td>
                <td align="right">$proceeds_from_sale_of_plant2</td>
           </tr>
            <tr>
                <td>Proceeds from Sale of Investments/Financial assets</td>
                <td align="right">$proceeds_from_sale_of_investments1</td>
                <td align="right">$proceeds_from_sale_of_investments2</td>
           </tr>
           <tr>
                <td>Proceeds from Sale of Goodwill, Patent Rights, Trade Marks, etc.</td>
                <td align="right">$proceeds_from_sale_of_goodwill1</td>
                <td align="right">$proceeds_from_sale_of_goodwill2</td>
           </tr>
           <tr>
                <td>Net cash received from sales of subsidiaries and other business units</td>
                <td align="right">$net_cash_received_from_sales_of_subsidiaries1</td>
                <td align="right">$net_cash_received_from_sales_of_subsidiaries2</td>
           </tr>
           <tr>
                <td>Other Proceeds from Investments</td>
                <td align="right">$other_proceeds_from_investments1</td>
                <td align="right">$other_proceeds_from_investments2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td>Add: Non-Operating Incomes from Investments:</td>
                <td align="right">$operating_incomes_from_investments1</td>
                <td align="right">$operating_incomes_from_investments2</td>
           </tr>
            <tr>
                <td>Dividends Received</td>
                <td align="right">$dividends_received1</td>
                <td align="right">$dividends_received2</td>
           </tr>
           <tr>
                <td>Interest Received</td>
                <td align="right">$interest_received1</td>
                <td align="right">$interest_received2</td>
           </tr>
           <tr>
                <td>Rent on Property Received</td>
                <td align="right">$rent_on_property_received1</td>
                <td align="right">$rent_on_property_received2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td><b>Less: Purchase of Non-Current Assets:</b></td>
                <td align="right"></td>
                <td align="right"></td>
           </tr>
            <tr>
                <td>Purchase of Plant, Property and Equipment</td>
                <td align="right">$purchase_of_plant1</td>
                <td align="right">$purchase_of_plant2</td>
           </tr>
           <tr>
                <td>Purchase of Investments/Financial Assets </td>
                <td align="right">$purchase_of_investments1</td>
                <td align="right">$purchase_of_investments2</td>
           </tr>
           <tr>
                <td>Purchase of Joint Ventures and Associates</td>
                <td align="right">$purchase_of_joint_ventures_and_associates1</td>
                <td align="right">$purchase_of_joint_ventures_and_associates2</td>
           </tr>
           <tr>
                <td>Purchase of intangible assets - Goodwill, Patent Rights, Trade Marks, etc. </td>
                <td align="right">$purchase_of_intangible_assets1</td>
                <td align="right">$purchase_of_intangible_assets2</td>
           </tr>
            <tr>
                <td>Other Payments for Investing Activities</td>
                <td align="right">$other_payments_for_investing_activities1</td>
                <td align="right">$other_payments_for_investing_activities2</td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td><b>Net Cash (used in)/from Investing Activities</b></td>
                <td align="right">$net_cash_from_investing_activities1</td>
                <td align="right">$net_cash_from_investing_activities2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td><b>CASH FLOW FROM FINANCING ACTIVITIES</b></td>
                <td align="right"></td>
                <td align="right"></td>
           </tr>
           <tr>
                <td><b>Proceeds from Issue of Share Capital and Borrowings:</b></td>
                <td align="right">$proceeds_from_issue_of_share_capital_and_borrowings1</td>
                <td align="right">$proceeds_from_issue_of_share_capital_and_borrowings2</td>
           </tr>
            <tr>
                <td>Proceeds from Issue of Ordinary Shares Capital</td>
                <td align="right">$proceeds_from_issue_of_ordinary_shares_capital1</td>
                <td align="right">$proceeds_from_issue_of_ordinary_shares_capital2</td>
           </tr>
            <tr>
                <td>Proceeds from Issue of Preference Shares Capital</td>
                <td align="right">$proceeds_from_issue_of_preference_shares_capital1</td>
                <td align="right">$proceeds_from_issue_of_preference_shares_capital2</td>
           </tr>
           <tr>
                <td>Proceeds from Loans and Borrowings etc. </td>
                <td align="right">$proceeds_from_loans_and_borrowings_etc1</td>
                <td align="right">$proceeds_from_loans_and_borrowings_etc2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>(-) Buy-back of Equity Shares</td>
                <td align="right">$buy_back_of_equity_shares1</td>
                <td align="right">$buy_back_of_equity_shares2</td>
           </tr>
            <tr>
                <td>(-) Redemption of Preference Shares</td>
                <td align="right">$redemption_of_preference_shares1</td>
                <td align="right">$redemption_of_preference_shares2</td>
           </tr>
           <tr>
                <td>(-) Repayment of Loans and Borrowings</td>
                <td align="right">$repayment_of_loans_and_borrowings1</td>
                <td align="right">$repayment_of_loans_and_borrowings2</td>
           </tr>
            <tr>
                <td>(-) Redemption of Debentures</td>
                <td align="right">$redemption_of_debentures1</td>
                <td align="right">$redemption_of_debentures2</td>
           </tr>
           <tr>
                <td>(-) Dividends Paid on Equity Shares</td>
                <td align="right">$dividends_paid_on_equity_shares1</td>
                <td align="right">$dividends_paid_on_equity_shares2</td>
           </tr>
           <tr>
                <td>(-) Dividend on Preference Shares</td>
                <td align="right">$dividend_on_preference_shares1</td>
                <td align="right">$dividend_on_preference_shares2</td>
           </tr>
           <tr>
                <td>(-) Repayment of obligations under leases</td>
                <td align="right">$repayment_of_obligations_under_leases1</td>
                <td align="right">$repayment_of_obligations_under_leases2</td>
           </tr>
            <tr>
                <td>Other payments for financing activities</td>
                <td align="right">$other_payments_for_financing_activities1</td>
                <td align="right">$other_payments_for_financing_activities2</td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td><b>Net Cash (used in)/From Financing Activities</b></td>
                <td align="right">$from_financing_activities1</td>
                <td align="right">$from_financing_activities2</td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td><b>Net Increase/(Decrease) in Cash and Cash Equivalents</b></td>
                <td align="right">$net_increase1</td>
                <td align="right">$net_increase2</td>
           </tr>
           <tr>
                <td><b>Cash and Cash Equivalents at the beginning of the Period</b></td>
                <td align="right">$cash_equivalents_at_the_beginning_of_the_period1</td>
                <td align="right">$cash_equivalents_at_the_beginning_of_the_period2</td>
           </tr>
            <tr>
                <td>Effect of Foreign Exchange Rate Changes</td>
                <td align="right">$effect_of_foreign_exchange_rate_changes1</td>
                <td align="right">$effect_of_foreign_exchange_rate_changes2</td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td><b>CASH AND CASH EQUIVALENTS AT THE END OF THE YEAR</b></td>
                <td align="right">$cash_equivalents_at_the_end_of_the_year1</td>
                <td align="right">$cash_equivalents_at_the_end_of_the_year2</td>
           </tr>
        </table>
        EOD;

       return $html;

    }

    /**
     * 资产负债表
     * @return void
     */
    public function balanceSheet()
    {
        foreach ($this->data['page9']['field3'][0] as $key => $value) {
            $newKey = $key . '1';
            if ($value < 0) {
                $value = abs($value);
                $$newKey = number_format($value, 2);
                if ($this->type == 2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            } else {
                if ($key != 'year') {
                    $$newKey = number_format($value, 2);
                } else {
                    $$newKey = $value;
                }
                if ($this->type == 2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            }
        }

        foreach ($this->data['page9']['field3'][1] as $key => $value) {
            $newKey = $key . '2';
            if ($value < 0) {
                $value = abs($value);
                $$newKey = number_format($value, 2);
                if ($this->type == 2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            } else {
                if ($key != 'year') {
                    $$newKey = number_format($value, 2);
                } else {
                    $$newKey = $value;
                }
                if ($this->type == 2) {
                    $$newKey = '<span style="color:red">'.$$newKey.'</span>';
                }
            }
        }


        // 合并单元格
        $html = <<<EOD
        <table border="1" cellpadding="2">
           <tr>
                <td align="center">Item</td>
                <td align="center">$year1</td>
                <td align="center">$year2</td>
           </tr>
           <tr>
                <td></td>
                <td  align="center">YUAN</td>
                <td  align="center">YUAN</td>
           </tr>
           <tr>
                <td>ASSETS</td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td>NON-CURRENT ASSETS</td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>Property, Plant and Equipment</td>
                <td align="right">$property_plant_and_equipment1</td>
                <td align="right">$property_plant_and_equipment2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Goodwill</td>
                <td  align="right">$goodwill1</td>
                <td  align="right">$goodwill2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Intangible Assets</td>
                <td  align="right">$intangible_assets1</td>
                <td  align="right">$intangible_assets2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Right-of-Use Assets</td>
                <td  align="right">$right_of_use_assets1</td>
                <td  align="right">$right_of_use_assets2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Investments Properties</td>
                <td  align="right">$investments_properties1</td>
                <td  align="right">$investments_properties2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Other Investments</td>
                <td  align="right">$other_investments_all1</td>
                <td  align="right">$other_investments_all2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Other Receivables</td>
                <td align="right">$long_term_receivables1</td>
                <td align="right">$long_term_receivables2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Deferred Tax Assets</td>
                <td align="right">$deferred_tax_assets1</td>
                <td align="right">$deferred_tax_assets2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Other Non-Current Assets</td>
                <td align="right">$other_non_current_assets_all1</td>
                <td align="right">$other_non_current_assets_all2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>TOTAL NON-CURRENT ASSETS</td>
                <td  align="right">$total_non_current_assets1</td>
                <td  align="right">$total_non_current_assets2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>CURRENT ASSETS</td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Inventories</td>
                <td  align="right">$inventories1</td>
                <td  align="right">$inventories2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Trade and Other Receivables</td>
                <td  align="right">$trade_and_other_receivables1</td>
                <td  align="right">$trade_and_other_receivables2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Other Investments</td>
                <td  align="right">$other_investments1</td>
                <td  align="right">$other_investments2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Other Current Assets</td>
                <td  align="right">$other_current_assets_all1</td>
                <td  align="right">$other_current_assets_all2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Income Tax Receivables</td>
                <td  align="right">$income_tax_receivables1</td>
                <td  align="right">$income_tax_receivables2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Cash and Cash Equivalents</td>
                <td  align="right">$cash_and_cash_equivalents1</td>
                <td  align="right">$cash_and_cash_equivalents2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>TOTAL CURRENT ASSETS</td>
                <td  align="right">$total_current_assets1</td>
                <td  align="right">$total_current_assets2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>TOTAL ASSETS</td>
                <td  align="right">$total_assets1</td>
                <td  align="right">$total_assets2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>LIABILITIES</td>
                <td  align="right"></td>
                <td  align="right"></td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>CURRENT LIABILITIES</td>
                <td  align="right"></td>
                <td  align="right"></td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Interest-bearing loans and borrowings</td>
                <td  align="right">$interest_bearing_loans_and_borrowings_short1</td>
                <td  align="right">$interest_bearing_loans_and_borrowings_short2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Lease Liabilities</td>
                <td  align="right">$lease_liabilities_short1</td>
                <td  align="right">$lease_liabilities_short2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Trade and other payables</td>
                <td  align="right">$trade_and_other_payables1</td>
                <td  align="right">$trade_and_other_payables2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Provisions</td>
                <td  align="right">$provisions_short1</td>
                <td  align="right">$provisions_short2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Income Tax Payables</td>
                <td  align="right">$income_tax_payables_short1</td>
                <td  align="right">$income_tax_payables_short2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Other Current Liabilities</td>
                <td  align="right">$other_current_liabilities_all1</td>
                <td  align="right">$other_current_liabilities_all2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
               <tr>
                <td>TOTAL CURRENT LIABILITIES </td>
                <td  align="right">$total_current_liabilities1</td>
                <td  align="right">$total_current_liabilities2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>NON CURRENT LIABILITIES </td>
                <td  align="right"></td>
                <td  align="right"></td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Interest-bearing loans and borrowings</td>
                <td  align="right">$interest_bearing_loans_and_borrowings_long1</td>
                <td  align="right">$interest_bearing_loans_and_borrowings_long2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Lease Liabilities</td>
                <td  align="right">$lease_liabilities_long1</td>
                <td  align="right">$lease_liabilities_long2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Deferred Tax Liabilities</td>
                <td  align="right">$deferred_tax_liabilities1</td>
                <td  align="right">$deferred_tax_liabilities2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Provisions</td>
                <td  align="right">$provisions_long1</td>
                <td  align="right">$provisions_long2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Income Tax Payables</td>
                <td  align="right">$income_tax_payables_long1</td>
                <td  align="right">$income_tax_payables_long2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Other Payables</td>
                <td  align="right">$other_paybales1</td>
                <td  align="right">$other_paybales2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Other Non-Current Liabilities</td>
                <td  align="right">$other_non_current_liabilities_all1</td>
                <td  align="right">$other_non_current_liabilities_all2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>TOTAL NON CURRENT LIABILITIES</td>
                <td  align="right">$total_non_current_liabilities1</td>
                <td  align="right">$total_non_current_liabilities2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>TOTAL LIABILITIES </td>
                <td  align="right">$total_liabilities1</td>
                <td  align="right">$total_liabilities2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>TOTAL EQUITY</td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Capital and reserves attributable to equity holders of the Company</td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Ordinary Share Capital</td>
                <td  align="right">$ordinary_share_capital1</td>
                <td  align="right">$ordinary_share_capital2</td>
             </tr>
              <tr>
                <td>Less: Treasury Stocks</td>
                <td  align="right">$preferred_share_holders1</td>
                <td  align="right">$preferred_share_holders2</td>
             </tr>
             <tr>
                <td>Preferred Share Holders</td>
                <td  align="right">$share_premium1</td>
                <td  align="right">$share_premium2</td>
             </tr>
             <tr>
                <td>Share Premium</td>
                <td  align="right">$share_capital1</td>
                <td  align="right">$share_capital1</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td rowspan="3">Other Reserves</td>
                <td  align="right">$other_comprehensive_income1</td>
                <td  align="right">$other_comprehensive_income2</td>
             </tr>
             <tr>
                <td  align="right">$special_reserves1</td>
                <td  align="right">$special_reserves2</td>
             </tr>
             <tr>
                <td  align="right">$surplus_reserves1</td>
                <td  align="right">$surplus_reserves2</td>
             </tr>
              <tr>
                <td></td>
                <td  align="right">$other_reserves1</td>
                <td  align="right">$other_reserves2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Retained Earnings</td>
                <td  align="right">$retained_eranings1</td>
                <td  align="right">$retained_eranings2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>TOTAL EQUITY</td>
                <td  align="right">$total_equity1</td>
                <td  align="right">$total_equity2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>TOTAL EQUITY AND LIABILITIES</td>
                <td  align="right">$total_equity_and_liabilities1</td>
                <td  align="right">$total_equity_and_liabilities2</td>
             </tr>
        </table>
        EOD;

        return $html;

    }


    /**
     * Method mbConvertEncoding
     * 中文转编码
     *
     * @param $text $text [explicite description]
     */
    private function mbConvertEncoding($text)
    {
        return mb_convert_encoding($text, 'GBK', 'UTF-8');
    }

    /**
     * 将 PDF 文件转换为图片
     *
     * @param string $pdfPath PDF 文件路径
     * @param string $outputDir 输出图片的目录
     * @return array 返回包含图片路径的数组
     */
    function convertPdfToImages($pdfFile, $outputDir) {
        // 确保输出目录存在
        if (!file_exists($outputDir)) {
            mkdir($outputDir, 0777, true);
        }

        // 本地临时文件路径
        $tempPdfFile = $outputDir . '/temp_file'.time().'.pdf';
        // 下载 PDF 文件到本地
        if (!copy($pdfFile, $tempPdfFile)) {
            throw new Exception("无法下载 PDF 文件。");
        }


        // 定义输入 PDF 文件路径和输出 JPG 文件路径
        $uniquePrefix = uniqid('output_', true);

        // 构建 Ghostscript 命令
        $outputFile = $outputDir . '/' . $uniquePrefix . '_%03d.jpg'; // 输出文件名格

        // 构建 Ghostscript 命令
        $command = 'gs -sDEVICE=jpeg -r200 -o '.$outputFile.' -dJPEGQ=100 '.$tempPdfFile;
        // 调用系统命令
        exec($command, $output, $returnVar);

        // 检查命令执行是否成功
        if ($returnVar !== 0) {
            throw new Exception("PDF 转换为 JPG 失败，错误信息：\n" . implode("\n", $output) . "\n返回状态码: $returnVar");
        }

        // 获取输出目录中的所有 JPG 文件
        $jpgFiles = glob($outputDir . '/*.jpg');

        // 确保返回的文件路径是本次转换生成的文件
        $convertedFiles = [];
        foreach ($jpgFiles as $jpgFile) {
            if (strpos(basename($jpgFile), $uniquePrefix) === 0) {
                $file = substr($jpgFile, strpos($jpgFile, 'gsp'));
                $convertedFiles[] = $file;
            }
        }

        unlink($tempPdfFile);

        return $convertedFiles;

    }
}