<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Api\v1\Company\GspInvestigationController;
use App\Http\Controllers\Controller;
use App\Models\AiManFile;
use App\Models\CompanyGsp;
use App\Models\CompanyGspReport;
use App\Models\CountryModel;
use App\Models\GSP\Extra\GspInvestigationAdministrativePenaltyRecords;
use App\Models\GSP\Extra\GspInvestigationBalanceSheet;
use App\Models\GSP\Extra\GspInvestigationCashFlow;
use App\Models\GSP\Extra\GspInvestigationCommendatoryLetters;
use App\Models\GSP\Extra\GspInvestigationComprehensiveIncomes;
use App\Models\GSP\Extra\GspInvestigationCustomerSituation;
use App\Models\GSP\Extra\GspInvestigationDishonestyRecords;
use App\Models\GSP\Extra\GspInvestigationHonors;
use App\Models\GSP\Extra\GspInvestigationIntellectualProperties;
use App\Models\GSP\Extra\GspInvestigationLitigationArbitrationCases;
use App\Models\GSP\Extra\GspInvestigationMainProductCategories;
use App\Models\GSP\Extra\GspInvestigationManagementIntroductions;
use App\Models\GSP\Extra\GspInvestigationOfficeSpaces;
use App\Models\GSP\Extra\GspInvestigationProductApplicationCases;
use App\Models\GSP\Extra\GspInvestigationProductionDeliveries;
use App\Models\GSP\Extra\GspInvestigationProductionEquipment;
use App\Models\GSP\Extra\GspInvestigationQualifications;
use App\Models\GSP\Extra\GspInvestigationQualityAssurances;
use App\Models\GSP\Extra\GspInvestigationRawMaterials;
use App\Models\GSP\Extra\GspInvestigationShareholders;
use App\Models\GSP\Extra\GspInvestigationSignificantContracts;
use App\Models\GSP\GspInvestigationConfig;
use App\Models\GSP\GspInvestigationMain;
use App\Models\GspOption;
use App\Services\DocService;
use App\Services\GspEnReportOutputService;
use App\Services\GspReportOutputService;
use App\Services\OssService;
use App\Services\WordService;
use App\Services\ZipService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class GspController extends Controller
{
    const ACTION = 'gsp_form'; //场景

    /**
     * 绿智地球入驻申请列表
     * @param Request $request
     * @return null
     */
    public function index(Request $request)
    {
        $keyword = $request->get('keyword', '');
        $status = $request->get('status', '');
        $pageSize = $request->get('page_size', 10);

        $list = CompanyGsp::with(['profile:profileID,profileName'])
            ->when($keyword != '', function ($q) use ($keyword) {
                $q->whereHas('profile', function ($qu) use ($keyword) {
                    $qu->where('profileName', 'like', '%' . $keyword . '%');
                })->orWhere('name', 'like', '%' . $keyword . '%')
                    ->orWhere('contact_name', 'like', '%' . $keyword . '%')
                    ->orWhere('phone', $keyword)
                    ->orWhere('email', $keyword)
                    ->orWhere('credit_code', $keyword);
            })
            ->when($status != '', function ($q) use ($status) {
                $q->where('status', $status);
            })->orderBy('id', 'desc')
            ->paginate($pageSize);

        $items = $list->items();

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    // 生成用户上传预审表格token
    public function generateGspFileToken(Request $request)
    {
        $body = $request->validate([
            'id'    => ['required', 'integer', 'min:1'],
            'type'  => ['required', 'integer'],
        ]);

        $id = $body['id'];
        $type = $body['type'];
        $record = CompanyGsp::find($id);
        if (empty($record)) {
            return responseFail();
        }

        if ($type == 1) { // 用户盖章上传预审表格
            if (empty($record['form_complete'])) return responseFail();
            $file = $record['form_complete'];
            $filename = '资格预审表格(已签署盖章).pdf';
        } else if ($type == 2) { // 签约合同
            if (empty($record['contract_url'])) return responseFail();
            $file = $record['contract_url'];
            $filename = '绿智地球用户合同.pdf';
        } else if ($type == 3) { // 尽调合成文件(工作人员查看红色字体版本)
            $path = $record['report_url'];

            // 获取去掉扩展名后的文件名
            $filenameWithoutExtension = pathinfo(basename($path), PATHINFO_FILENAME);
            // 获取目录路径
            $directory = dirname($path);
            // 组合完整路径
            $file = "$directory/$filenameWithoutExtension".'_admin.pdf';

            $filename = basename($file);
        } else if ($type == 4) { // 英文版尽调合成文件(工作人员查看红色字体版本)
            $path = $record['report_url'];

            // 获取去掉扩展名后的文件名
            $filenameWithoutExtension = pathinfo(basename($path), PATHINFO_FILENAME);
            // 获取目录路径
            $directory = dirname($path);
            // 组合完整路径
            $file = "$directory/$filenameWithoutExtension".'_en_admin.pdf';

            $filename = basename($file);
        }

        $token = DocService::generateToken($file, self::ACTION, 3600, $filename);

        return responseSuccess($token);
    }

    /**
     * 预览文件
     * @param Request $request
     * @return never|\Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function previewFile(Request $request)
    {
        $token = $request->route('token');

        return DocService::preview($token, self::ACTION);
    }

    /**
     * 下载文件
     * @param Request $request
     * @return never|\Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function downloadForm(Request $request)
    {
        $token = $request->route('token');

        return DocService::download($token, self::ACTION);
    }

    /**
     * 审核预审表格
     * @return void
     */
    public function checkForm(Request $request)
    {
        $body = $request->validate([
            'id'                 => ['required', 'integer', 'min:1'],
            'status'             => ['required', 'integer',  Rule::in([CompanyGsp::STATUS_FORM_SUCCESS, CompanyGsp::STATUS_FORM_FAIL])],
            'form_reject_reason' => ['required_if:status,'. CompanyGsp::STATUS_FORM_FAIL],
        ]);
        $id = $body['id'];
        $status = $body['status'];
        $form_reject_reason = $body['form_reject_reason'];
        // 获取详情
        $info = CompanyGsp::find($id);
        if (empty($info)) { // 信息不存在
            return responseFail(__('info no exist'));
        }
        if ($info['status'] != CompanyGsp::STATUS_UPLOAD_FORM) {
            return responseFail(__('param error', ['param' => 'status']));
        }

        // 更新
        $info->status = $status;
        if ($status == CompanyGsp::STATUS_FORM_FAIL) {  // 审核驳回填写原因
            $info->form_reject_reason = $form_reject_reason;
            $info->form_complete = '';
        }
        $info->save();

        // todo 通知用户
        return responseSuccess();
    }

    /**
     * 审核合同
     * @return void
     */
    public function checkContract(Request $request)
    {
        $body = $request->validate([
            'id'                 => ['required', 'integer', 'min:1'],
            'status'             => ['required', 'integer',  Rule::in([CompanyGsp::STATUS_CONTRACT_SUCCESS, CompanyGsp::STATUS_CONTRACT_FAIL])],
            'contract_reject_reason' => ['required_if:status,'. CompanyGsp::STATUS_CONTRACT_FAIL],
        ]);
        $id = $body['id'];
        $status = $body['status'];
        $contract_reject_reason = $body['contract_reject_reason'];
        // 获取详情
        $info = CompanyGsp::find($id);
        if (empty($info)) { // 信息不存在
            return responseFail(__('info no exist'));
        }
        if (empty($info['contract_url'])) {
            return responseFail(__('missing parameter', ['param' => 'contract file']));
        }
        if ($info['status'] != CompanyGsp::STATUS_UPLOAD_CONTRACT) {
            return responseFail(__('param error', ['param' => 'status']));
        }


        // 更新
        $info->status = $status;
        if ($status ==  CompanyGsp::STATUS_CONTRACT_FAIL) {  // 审核驳回填写原因
            $info->contract_reject_reason = $contract_reject_reason;
            $info->contract_url = '';
        }
        $info->save();

        // todo 通知用户
        return responseSuccess();
    }


    /**
     * 查看尽调报告
     * @param Request $request
     * @return void
     */
    public function reportDetail(Request $request)
    {
        $body = $request->validate([
            'id' => ['required', 'integer', 'min:1'],
        ]);
        $gsp_id = $body['id'];
        // 查看尽调报告文件
        $data = GspInvestigationConfig::with([
            'upload' => function ($query) use ($gsp_id){
                // 只加载标题中包含 "important" 的帖子
                $query->where('gsp_id', $gsp_id);
            }
        ])->select('id', 'category_zh_cn', 'description_zh_cn', 'allow_online_form', 'allow_upload_file', 'allow_template_download', 'sort', 'pid', 'level', 'db_name', 'type', 'template_file')
            ->where('allow_upload_file', 1)
            ->orderBy('level', 'asc')
            ->orderBy('sort', 'asc')
            ->get();

        return responseSuccess($data);
    }

    // 审核尽调文件
    public function checkReportFile(Request $request)
    {
        $body = $request->validate([
            'id'        => ['required', 'integer', 'min:1'],
            'status'    => ['required', 'integer',  Rule::in([CompanyGspReport::STATUS_FILE_SUCCESS, CompanyGspReport::STATUS_FILE_FAIL])],
            'file_id'   => ['required', 'integer', 'min:1'],
        ]);
        $gsp_id = $body['id'];
        $status = $body['status'];
        $file_id = $body['file_id'];

        CompanyGspReport::where('id', $file_id)->where('gsp_id', $gsp_id)->update(['status' => $status]);

        return responseSuccess();

    }

    /**
     * 审核材料
     * @return void
     */
    public function checkReport(Request $request)
    {
        $body = $request->validate([
            'id'        => ['required', 'integer', 'min:1'],
            'status'    => ['required', 'integer',  Rule::in([CompanyGsp::STATUS_REPORT_SUCCESS, CompanyGsp::STATUS_REPORT_FAIL])],
            'report_reject_reason'   => ['required_if:status,'. CompanyGsp::STATUS_REPORT_FAIL],
            'file'      => ['required_if:status,'. CompanyGsp::STATUS_REPORT_SUCCESS],
        ]);

        $id = $body['id'];
        $status = $body['status'];
        $report_reject_reason = $body['report_reject_reason'];
        // 获取详情
        $info = CompanyGsp::find($id);
        if (empty($info)) { // 信息不存在
            return responseFail(__('info no exist'));
        }
        if ($info['status'] != CompanyGsp::STATUS_POST_REPORT) {
            return responseFail(__('param error', ['param' => 'status']));
        }
        $file = $request->file('file');
        if ($file) {
            $filePath = $file->store('gsp', 'public');
            $info->company_public_report = $filePath ?: '';
        }

        // 查看尽调报告文件是否审核通过
        $count = CompanyGspReport::where('gsp_id', $id)->whereIn('status', [CompanyGspReport::STATUS_FILE_NO])->where('file_id','<>', 123)->count();
        if ($count >0 ) {
            return responseFail('尽调报告文件还有未审核的文件!');
        }

        // 更新
        $info->status = $status;
        if ($status == CompanyGsp::STATUS_REPORT_FAIL) {  // 审核驳回填写原因
            $info->report_reject_reason = $report_reject_reason;
        }

        if ($status == CompanyGsp::STATUS_REPORT_SUCCESS) { // 合成尽调报告文件
            // 查看尽调报告文件是否审核通过
            $count = CompanyGspReport::where('gsp_id', $id)->whereIn('status', [CompanyGspReport::STATUS_FILE_FAIL])->where('file_id','<>', 123)->count();
            if ($count >0 ) {
                return responseFail('尽调报告文件还有未审核的文件!');
            }
            $this->generateResport($id);
        }

        // 尽调审核意见修改为未提交
        $info->is_report_partner_post = 0;

        $info->save();

        // todo 通知用户
        return responseSuccess();
    }

    /**
     * 审核尽调报告
     * @return void
     */
    public function caCheckReport(Request $request)
    {
        $body = $request->validate([
            'id'        => ['required', 'integer', 'min:1'],
            'status'    => ['required', 'integer',  Rule::in([CompanyGsp::STATUS_REPORT_CA_SUCCESS, CompanyGsp::STATUS_REPORT_CA_FAIL])],
            'report_ca_review'   => ['required_if:status,'. CompanyGsp::STATUS_REPORT_CA_FAIL],
        ]);
        $id = $body['id'];
        $status = $body['status'];
        $report_ca_review = $body['report_ca_review'];
        // 获取详情
        $info = CompanyGsp::find($id);
        if (empty($info)) { // 信息不存在
            return responseFail(__('info no exist'));
        }

        if ($info['is_report_partner_post']  == 0) {
            return responseFail(__('param error', ['param' => 'status']));
        }

        // 更新
        if ($status == CompanyGsp::STATUS_REPORT_CA_FAIL) {  // 审核驳回填写原因
            $info->report_ca_review = $report_ca_review;
            // 合伙人尽调审核意见修改为未提交
            $info->is_report_partner_post = 0;
            $info->status = 5; // 返回到才料提交页
        } else {
            $info->status = $status;
        }

        $info->save();

        // todo 通知用户
        return responseSuccess();
    }

    /**
     * 打包资料
     * @return void
     */
    public function zipFile(Request $request)
    {
        $body = $request->validate([
            'id' => ['required', 'integer', 'min:1'],
        ]);

        $user = $request->attributes->get('user');
        $id = $body['id'];
        // 获取详情
        $info = CompanyGsp::where('id', $id)->first();
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }
        // 尽调文件批量下载
        $reportFiles = CompanyGspReport::where('gsp_id', $id)->get();

        // 打包文件
        $files = [];
        if ($reportFiles) {
            foreach ($reportFiles as $file) {
                if (Storage::disk('public')->exists($file['file_path'])) {
                    $file_path = viewFileUrl($file['file_path']);
                } else {
                    $file_path = OssService::link($file['file_path']);
                }
                $files[] = [
                    'file_path' => $file_path,
                    'file_name' => $file['file_name']
                ];
            }
        }

        if ($files) {
            $fileName = $info['name'].'_尽调文件下载_'.date('Y-m-d').'.zip';
            $path = ZipService::createZip($files,   $fileName);

            return responseSuccess($path);
        }

        return responseFail();
    }

    /**
     * 下载用户上传的数字人文件
     */
    public function download(Request $request)
    {
        $request->validate([
            'type' => 'required|string|in:local,oss',
            'file_path' => 'required|string',
        ]);

        if ($request->type == 'local') {
            $fileUrl = env('APP_URL') . '/storage/' . $request->file_path; // local 上传文件的basename
        } else {
            $fileUrl = OssService::link($request->file_path);
        }

        // 获取文件名
        $fileName = basename($fileUrl);

        // 读取 OSS 文件
        $client = new \GuzzleHttp\Client();
        $response = $client->get($fileUrl, ['stream' => true]);

        // 流式返回文件内容
        return response()->streamDownload(function () use ($response) {
            $body = $response->getBody();
            while (!$body->eof()) {
                echo $body->read(1024);
            }
        }, $fileName);
    }

    /**
     * 绿智地球申请表选项
     */
    public function selectOptions()
    {
        $gspOptions = GspOption::all();
        // 经济行为
        $economics = $gspOptions->where('type', GspOption::TYPE_ECONOMIC_BEHAVIOR)->select('title', 'id')->values();
        // 行业群体
        $industries = $gspOptions->where('type', GspOption::TYPE_INDUSTRY_GROUP)->select('title', 'id')->values();
        // 目标国家
        $countries = (new CountryModel())->getData();

        return responseSuccess(compact('economics', 'industries', 'countries'));
    }


    // 合成尽调报告文件
    public function generateResport($id = 33)
    {
        // 申请入驻表
        $gspData            = CompanyGsp::where('id', $id)->first();
        $reportNumber       = CompanyGsp::reportNumber($id);  // 报告编码
        $submitTime         = date('Y-m-d', strtotime($gspData['report_post_date']));   //报告基准日
        $reportTime         = date('Y-m-d');   //报告出具日

        $expireData = strtotime("+365 days", strtotime($reportTime));        // 报告有效期
        $expireData = date('Y-m-d', $expireData);

        //查主表
        $iMian = GspInvestigationMain::where('pid',$id)->first();
        //概况摘要
        $summaryData                = GspInvestigationController::summaryDataSearch($id);
        $summaryDataDate            = new \DateTime($summaryData['establishment_date']);
        $establishmentYear          = $summaryDataDate->format('Y'); // 获取年份
        // 1. 公司简介
        $companyProfileData         = GspInvestigationController::companyProfileDataSearch($id);
        $companyProfileOtherData    = GspInvestigationController::companyProfileOtherDataSearch($id);
        $establishmentDate          = new \DateTime($companyProfileData['establishment_date']);
        $establishmentDateYear      = $establishmentDate->format('Y'); // 获取年份
        $countryModel               = GspInvestigationController::countriesData()[$companyProfileData['main_producer_nation']] ?? [];
        // $countryModel            = CountryModel::where('countryID', $companyProfileData['main_producer_nation'])->first();
        $companyProfileData['main_producer'] = $countryModel['countryZH'] ?? $companyProfileData['main_producer_nation'];
        $companyProfileData['main_producer'] = $companyProfileData['main_producer'] . ' ' . $companyProfileData['main_producer_region'];
        $companyProfileData['main_producer_en'] = $countryModel['countryEN'] ?? $companyProfileData['main_producer_nation_en'];
        $companyProfileData['main_producer_en'] = $companyProfileData['main_producer_en'] . ' ' . $companyProfileData['main_producer_region_en'];
        //2. 公司工商情况
        $companyBusinessesData          = GspInvestigationController::companyBusinessesDataSearch($id);
        $industryName                   = array_column(GspInvestigationMain::selectOption()['industry_name'], 'label', 'value');
        $industryNameEn                 = array_column(GspInvestigationMain::selectOption()['industry_name_en'], 'label', 'value');
        $countryModel                   = GspInvestigationController::countriesData()[$companyBusinessesData['registered_country']] ?? [];
        $registeredCountry              = $countryModel['countryZH'] ?? $companyBusinessesData['registered_country'];
        $registeredCountryEn            = $countryModel['countryEN'] ?? $companyBusinessesData['registered_country_en'];
        $selectOptionCT                 = array_column(GspInvestigationMain::selectOption()['company_type'], 'label', 'value');
        $selectOptionCTEn               = array_column(GspInvestigationMain::selectOption()['company_type_en'], 'label', 'value');
        $selectOptionMU                 = array_column(GspInvestigationMain::selectOption()['monetary_unit'], 'label', 'value');        
        $companyBusinessesData['registered_country']     = "{$registeredCountry} ";
        $companyBusinessesData['registered_country_en']  = "{$registeredCountryEn} ";  
        $companyBusinessesData['company_type']       = $selectOptionCT[$companyBusinessesData['company_type']] ?? '';
        $companyBusinessesData['company_type_en']    = $selectOptionCTEn[$companyBusinessesData['company_type_en']] ?? '';         
        $companyBusinessesData['register_capital']   = $companyBusinessesData['register_capital'] . $selectOptionMU[$companyBusinessesData['register_capital_units']] ?? '';
        $companyBusinessesData['paid_capital']       = $companyBusinessesData['paid_capital'] . $selectOptionMU[$companyBusinessesData['paid_capital_units']] ?? '';        
        //3. 管理层介绍
        $managementIntroductions    = GspInvestigationManagementIntroductions::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $managementIntroductionsEn  = $this->extraTableHandle($managementIntroductions, 'cna_gsp_investigation_management_introductions', $id, true);
        $managementIntroductions    = $this->extraTableHandle($managementIntroductions, 'cna_gsp_investigation_management_introductions', $id);
        //4. 股东情况
        $shareholders               = GspInvestigationShareholders::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $shareholdersEn             = $this->extraTableHandle($shareholders, 'cna_gsp_investigation_shareholders', $id, true);
        $shareholders               = $this->extraTableHandle($shareholders, 'cna_gsp_investigation_shareholders', $id);
        //1. 主要产品类别
        $mainProductCategories      = GspInvestigationMainProductCategories::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $mainProductCategoriesEn    = $this->extraTableHandle($mainProductCategories, 'cna_gsp_investigation_main_product_categories', $id, true);
        $mainProductCategories      = $this->extraTableHandle($mainProductCategories, 'cna_gsp_investigation_main_product_categories', $id);
        //2. 前五大主要生产设备
        $productionEquipment        = GspInvestigationProductionEquipment::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $productionEquipmentEn      = $this->extraTableHandle($productionEquipment, 'cna_gsp_investigation_production_equipment', $id, true);
        $productionEquipment        = $this->extraTableHandle($productionEquipment, 'cna_gsp_investigation_production_equipment', $id);
        //3. 生产及办公面积
        $officeSpaces               = GspInvestigationOfficeSpaces::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $officeSpacesEn             = $this->extraTableHandle($officeSpaces, 'cna_gsp_investigation_office_spaces', $id, true);
        $officeSpaces               = $this->extraTableHandle($officeSpaces, 'cna_gsp_investigation_office_spaces', $id);
        //4. 员工人数
        $employeesData              = GspInvestigationController::employeesDataSearch($id);
        $employeesDataDate          = new \DateTime($employeesData['employee_quantity_statistical_date']);
        $employeesDataYear          = $employeesDataDate->format('Y'); // 获取年份
        $employeesDataMonth         = $employeesDataDate->format('m'); // 获取月份
        $employeesDataDay           = $employeesDataDate->format('d'); // 获取日期   
        //5. 生产与交付
        $roductionDeliveries        = GspInvestigationProductionDeliveries::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $roductionDeliveriesEn      = $this->extraTableHandle($roductionDeliveries, 'cna_gsp_investigation_production_deliveries', $id, true);
        $roductionDeliveries        = $this->extraTableHandle($roductionDeliveries, 'cna_gsp_investigation_production_deliveries', $id);
        //6. 质量保证与控制
        $qualityAssurances          = GspInvestigationQualityAssurances::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $qualityAssurancesEn        = $this->extraTableHandle($qualityAssurances, 'cna_gsp_investigation_quality_assurances', $id, true);
        $qualityAssurances          = $this->extraTableHandle($qualityAssurances, 'cna_gsp_investigation_quality_assurances', $id);
        //7. 前十主要原材料及来源占比
        $rawMaterials               = GspInvestigationRawMaterials::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $rawMaterialsEn             = $this->extraTableHandle($rawMaterials  , 'cna_gsp_investigation_raw_materials', $id, true);
        $rawMaterials               = $this->extraTableHandle($rawMaterials  , 'cna_gsp_investigation_raw_materials', $id);
        // 8. 售后服务及保证
        $afterSalesData             = GspInvestigationController::afterSalesDataSearch($id);
        //1. 前五大主要客户情况
        $customerSituation          = GspInvestigationCustomerSituation::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $customerSituationEn        = $this->extraTableHandle($customerSituation, 'cna_gsp_investigation_customer_situation', $id, true);
        $customerSituation          = $this->extraTableHandle($customerSituation, 'cna_gsp_investigation_customer_situation', $id);
        // 2. 产品应用案例
        $productApplicationCases    = GspInvestigationProductApplicationCases::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $productApplicationCasesEn  = $this->extraTableHandle($productApplicationCases, 'cna_gsp_investigation_product_application_cases', $id, true);
        $productApplicationCases    = $this->extraTableHandle($productApplicationCases, 'cna_gsp_investigation_product_application_cases', $id);
        // 3. 所获荣誉
        $honors                     = GspInvestigationHonors::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $honorsEn                   = $this->extraTableHandle($honors, 'cna_gsp_investigation_honors', $id, true);
        $honors                     = $this->extraTableHandle($honors, 'cna_gsp_investigation_honors', $id);
        // 3. 所获表扬信
        $commendatoryLetters        = GspInvestigationCommendatoryLetters::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $commendatoryLettersEn      = $this->extraTableHandle($commendatoryLetters, 'cna_gsp_investigation_commendatory_letters', $id, true);
        $commendatoryLetters        = $this->extraTableHandle($commendatoryLetters, 'cna_gsp_investigation_commendatory_letters', $id);
        // 1. 资质及认证
        $qualifications             = GspInvestigationQualifications::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $qualificationsEn           = $this->extraTableHandle($qualifications, 'cna_gsp_investigation_qualifications', $id, true);
        $qualifications             = $this->extraTableHandle($qualifications, 'cna_gsp_investigation_qualifications', $id);
        // 2. 知识产权
        $intellectualProperties     = GspInvestigationIntellectualProperties::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $intellectualPropertiesEn   = $this->extraTableHandle($intellectualProperties, 'cna_gsp_investigation_intellectual_properties', $id, true);
        $intellectualProperties     = $this->extraTableHandle($intellectualProperties, 'cna_gsp_investigation_intellectual_properties', $id);
        // 3. 重大合同
        $significantContracts           = GspInvestigationSignificantContracts::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $significantContractsEn         = $this->extraTableHandle($significantContracts, 'cna_gsp_investigation_significant_contracts', $id, true);
        $significantContracts           = $this->extraTableHandle($significantContracts, 'cna_gsp_investigation_significant_contracts', $id);
        // 4. 重大诉讼及仲裁信息
        $litigationArbitrationCases     = GspInvestigationLitigationArbitrationCases::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $litigationArbitrationCasesEn   = $this->extraTableHandle($litigationArbitrationCases, 'cna_gsp_investigation_litigation_arbitration_cases', $id, true);
        $litigationArbitrationCases     = $this->extraTableHandle($litigationArbitrationCases, 'cna_gsp_investigation_litigation_arbitration_cases', $id);
        // 5. 行政处罚记录
        $administrativePenaltyRecords   = GspInvestigationAdministrativePenaltyRecords::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $administrativePenaltyRecordsEn = $this->extraTableHandle($administrativePenaltyRecords, 'cna_gsp_investigation_administrative_penalty_records', $id, true);
        $administrativePenaltyRecords   = $this->extraTableHandle($administrativePenaltyRecords, 'cna_gsp_investigation_administrative_penalty_records', $id);
        // 6. 失信记录
        $dishonestyRecords              = GspInvestigationDishonestyRecords::where('pid',$id)->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();
        $dishonestyRecordsEn            = $this->extraTableHandle($dishonestyRecords, 'cna_gsp_investigation_dishonesty_records', $id, true);
        $dishonestyRecords              = $this->extraTableHandle($dishonestyRecords, 'cna_gsp_investigation_dishonesty_records', $id);
        // 1. 综合损益表
        $comprehensiveIncomes = GspInvestigationComprehensiveIncomes::where([
            ['pid', '=', $id]
        ])->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();                
        // 2. 资产负债表
        $balanceSheet = GspInvestigationBalanceSheet::where([
            ['pid', '=', $id]
        ])->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();        
        // 3. 现金流量表
        $cashFlow = GspInvestigationCashFlow::where([
            ['pid', '=', $id]
        ])->get()->makeHidden(['id', 'created_at', 'updated_at', 'pid'])->toArray();        


        $data = [
            'page1' => [  // 公司信息
                'field1' => $iMian['company_name'],
                'field2' => $submitTime,
                'field3' => $reportTime,
                'field4' => $reportNumber,
                'field5' => $expireData,
            ],
            'page2' => [ // 声明文件
                'field1' => date('Y', strtotime($reportTime)),
                'field2' => date('m', strtotime($reportTime)),
                'field3' => date('d', strtotime($reportTime)),
                'field4' => date('Y', strtotime($expireData)),
                'field5' => date('m', strtotime($expireData)),
                'field6' => date('d', strtotime($expireData)),
            ],
            'page4' => [ // 概况摘要
                'field1' => $establishmentYear,
                'field2' => $summaryData['group_name'],
                'field3' => $summaryData['group_headquarters_address'],
                'field4' => $summaryData['register_capital'] . $summaryData['register_capital_units'],
                'field5' => $summaryData['paid_capital'] . $summaryData['paid_capital_units'],
                'field6' => $summaryData['shareholder_name'],
                'field7' => $summaryData['shareholder_ratio'],
                'field7a'=> $summaryData['company_overview'],
                'filed7b'=> $summaryData['business_overview_core_product'], //业务概述
                'field8' => $summaryData['office_spaces_area'],
                'field8a'=> $summaryData['production_equipment_name'],
                'field8b'=> $summaryData['raw_materials_country'],
                'field8c'=> $summaryData['raw_materials_supplier'],                
                'field9' => $summaryData['business_overview_quality_control'],
                'field9a'=> $summaryData['after_sales_response'].'/'.$summaryData['after_sales_coverage'],
                'field9b'=> $summaryData['business_overview'],
                'field9c'=> $summaryData['customer_situation_industry'],                           //客户及业绩
                'field9d'=> $summaryData['customer_situation_name'],
                'field10'=> $summaryData['customer_situation_amount'], 
                'field10a'=> $summaryData['product_application_cases_project_name'],
                'field10b'=> $summaryData['product_application_cases_country'],
                'field11'=> $summaryData['product_application_cases_amount'],
                'field11a'=> $summaryData['customers_performance'],
                'field11b' => $summaryData['qualifications_certificate_name'],                             // 法务与合规
                'field11c' => $summaryData['intellectual_property_type'],
                'field11d' => $summaryData['significant_contracts_count'],
                'field12'=> $summaryData['significant_contracts_amount'], 
                'field13'=> $summaryData['litigation_arbitration_cases_count'],
                'field14'=> $summaryData['litigation_arbitration_cases_amount'],
                'field15'=> $summaryData['administrative_penalty_records_count'],
                'field16'=> $summaryData['untreated_administrative_penalty_reason'],
                'field17'=> $summaryData['dishonesty_records_count'],
                'field18'=> $summaryData['comprehensive_incomes_year1'], // 财务报告
                'field19'=> $summaryData['comprehensive_incomes_year2'],
                'field20'=> $summaryData['comprehensive_incomes_revenue1'],
                'field21'=> $summaryData['comprehensive_incomes_revenue2'],
                'field22'=> $summaryData['comprehensive_incomes_profit_for_the_period1'],
                'field23'=> $summaryData['comprehensive_incomes_profit_for_the_period2'],
                'field24'=> $summaryData['comprehensive_incomes_year1'],
                'field24a'=> $summaryData['balance_sheet_asset_liability_ratio1'],
                'field24b'=> $summaryData['comprehensive_incomes_year2'],
                'field24c'=> $summaryData['balance_sheet_asset_liability_ratio2'],
                'field25'=> $summaryData['future_planning'] // 未来规划概况
            ],
            'page5' => [  // 公司概述
                'field1' => $establishmentDateYear,
                'field2' => $companyProfileData['group_name'],
                'field3' => $companyProfileData['main_producer'],
                'field4' => $industryName[$companyProfileData['industry_name']],
                'field4a' => $companyProfileData['main_industry_direction'],
                'field5' => $companyProfileData['main_product'],
                'field5a' => $companyProfileOtherData['summary_of_company_profile'],
                'field6' => $companyBusinessesData['company_name'], // 公司工商情况
                'field7' => $companyBusinessesData['used_name'],
                'field8' => $companyBusinessesData['credit_code'],
                'field9' => $companyBusinessesData['registered_country'],
                'field10' => $companyBusinessesData['establishment_date'],
                'field11' => $companyBusinessesData['company_type'],
                'field12' => $companyBusinessesData['register_capital'],
                'field13' => $companyBusinessesData['paid_capital'],
                'field14' => $companyBusinessesData['registered_address'],
                'field15' => $companyBusinessesData['operating_address'],
                'field16' => $companyBusinessesData['business_scope'],
                'field17' => $companyBusinessesData['annual_inspection'],
                'field18' => $companyBusinessesData['operating_period'],
                'field19' => $managementIntroductions, // 管理层介绍
                'field20' => $shareholders,  // 股东情况（仅罗列前五大股东）
            ],
            'page6' => [ //业务概述
                'field1' => $mainProductCategories, // 1.主要产品类别
                'field2' => $productionEquipment, // 2.前五大主要生产设备
                'field3' => $officeSpaces, // 3.生产及办公面积
                'field4' => $employeesDataYear, // // 员工人数
                'field5' => $employeesDataMonth,
                'field6' => $employeesDataDay,
                'field7' => $employeesData['employee_quantity_total'],
                'field8' => $employeesData['employee_quantity_social_insurance'],
                'field9' => $roductionDeliveries, // 5. 生产与交付
                'field10' => $iMian['capacity_calculation_formula'],
                'field11' => $iMian['capacity_year'],
                'field12' => $iMian['capacity_quantity'],
                'field13' => $iMian['capacity_year'],
                'field14' => $iMian['capacity_savings_account'],
                'field15' => $qualityAssurances, //质量保证与控制
                'field16' => $rawMaterials, // 7.前十主要原材料及来源占比
                'field17' => $afterSalesData['after_sales_standard'],// 8.售后服务及保证
                'field18' => $afterSalesData['after_sales_mode'],
                'field19' => $afterSalesData['after_sales_response'],
                'field20' => $afterSalesData['after_sales_coverage'],
            ],
            'page7' => [
                'field1' => $customerSituation, // 1.前五大主要客户情况
                'field2' => $productApplicationCases, // 2.产品应用案例
                'field3' => $honors, // 3.所获荣誉
                'field4' => $commendatoryLetters, // 4.司所获表扬信
            ],
            'page8' => [
                'field1' => $qualifications, // 1.资质及认证
                'field2' => $intellectualProperties, // 2.知识产权
                'field3' => $significantContracts, // 3.重大合同
                'field4' => $litigationArbitrationCases, // 4.重大诉讼及仲裁信息
                'field5' => $administrativePenaltyRecords, // 5.行政处罚记录
                'field6' => $dishonestyRecords, // 6.失信记录
            ],
            'page9' => [ // 财务部分
                'field4'=>  $comprehensiveIncomes[0]['accounting_name'],
                'field1' => $comprehensiveIncomes,  //利润表
                'field2' => $cashFlow, // 现金流量表
                'field3' => $balanceSheet// 资产负债表
            ],
            'page10'=> [ // 未来规划
                'field1'=> $iMian['future_planning_detail']
            ],
            'page11'=> [ // 公司附件
                'field1'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 56)->pluck('file_path')->toArray(),// 营业执照
                'field2'=> [$gspData['company_public_report']],// 企业信息公示
                'field3'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 51)->pluck('file_path')->toArray(), // 参保证明
                'field4'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 57)->pluck('file_path')->toArray(), // 企业征信
                'field5'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 17)->pluck('file_path')->toArray(), // 资质与认证证书
                'field6'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->whereIn('file_id', [53,54])->pluck('file_path')->toArray(), // 荣誉证书与表扬信
                'field7'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 52)->pluck('file_path')->toArray(), //注册商标证书
                'field8'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 62)->pluck('file_path')->toArray(), //专利证书
                'field9'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 63)->pluck('file_path')->toArray(), //著作权登记证书
                'field10'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 50)->pluck('file_path')->toArray(), //租赁合同/不动产权证/不动产查册
                'field11'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 41)->pluck('file_path')->toArray(), // 主要产品图片
                'field12'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 42)->pluck('file_path')->toArray(), // 生产设备
                'field13'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 43)->pluck('file_path')->toArray(), // 生产及办公面积
                'field14'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 61)->pluck('file_path')->toArray(), // 审计报告
                'field15'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 48)->pluck('file_path')->toArray(), // 控股股东、实际控制人声明
                'field16'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 49)->pluck('file_path')->toArray(), // 公司全体董事、监事、高级管理人员声明
            ]
        ];

        $data_en = [
            'page1' => [  // 公司信息
                'field1' => $iMian['company_name_en'],
                'field2' => $submitTime,
                'field3' => $reportTime,
                'field4' => $reportNumber,
                'field5' => $expireData,
            ],
            'page2' => [ // 声明文件
                'field1' => date('Y', strtotime($reportTime)),
                'field2' => date('m', strtotime($reportTime)),
                'field3' => date('d', strtotime($reportTime)),
                'field4' => date('Y', strtotime($expireData)),
                'field5' => date('m', strtotime($expireData)),
                'field6' => date('d', strtotime($expireData)),
            ],
            'page4' => [ // 概况摘要
                'field1' => $establishmentYear,
                'field2' => $summaryData['group_name_en'],
                'field3' => $summaryData['group_headquarters_address_en'],
                'field4' => $summaryData['register_capital'] . $summaryData['register_capital_units'],
                'field5' => $summaryData['paid_capital'] . $summaryData['paid_capital_units'],
                'field6' => $summaryData['shareholder_name_en'],
                'field7' => $summaryData['shareholder_ratio'],
                'field7a'=> $summaryData['company_overview_en'],
                'filed7b'=> $summaryData['business_overview_core_product_en'], //业务概述
                'field8' => $summaryData['office_spaces_area'],
                'field8a'=> $summaryData['production_equipment_name_en'],
                'field8b'=> $summaryData['raw_materials_country_en'],
                'field8c'=> $summaryData['raw_materials_supplier_en'],
                'field9' => $summaryData['business_overview_quality_control_en'],
                'field9a'=> $summaryData['business_overview_after_sales_en'],
                'field9b'=> $summaryData['business_overview_en'],
                'field9c'=> $summaryData['customer_situation_industry_en'],                           //客户及业绩
                'field9d'=> $summaryData['customer_situation_name_en'],
                'field10'=> $summaryData['customer_situation_amount'],
                'field10a'=> $summaryData['product_application_cases_project_name_en'],
                'field10b'=> $summaryData['product_application_cases_country_en'],
                'field11'=> $summaryData['product_application_cases_amount'],
                'field11a'=> $summaryData['customers_performance_en'],
                'field11b' => $summaryData['qualifications_certificate_name_en'],                             // 法务与合规
                'field11c' => $summaryData['intellectual_property_type_en'],
                'field11d' => $summaryData['significant_contracts_count'],
                'field12'=> $summaryData['significant_contracts_amount'],
                'field13'=> $summaryData['litigation_arbitration_cases_count'],
                'field14'=> $summaryData['litigation_arbitration_cases_amount'],
                'field15'=> $summaryData['administrative_penalty_records_count'],
                'field16'=> $summaryData['untreated_administrative_penalty_reason_en'],
                'field17'=> $summaryData['dishonesty_records_count'],
                'field18'=> $summaryData['comprehensive_incomes_year1'], // 财务报告
                'field19'=> $summaryData['comprehensive_incomes_year2'],
                'field20'=> $summaryData['comprehensive_incomes_revenue1'],
                'field21'=> $summaryData['comprehensive_incomes_revenue2'],
                'field22'=> $summaryData['comprehensive_incomes_profit_for_the_period1'],
                'field23'=> $summaryData['comprehensive_incomes_profit_for_the_period2'],
                'field24'=> $summaryData['comprehensive_incomes_year1'],
                'field24a'=> $summaryData['balance_sheet_asset_liability_ratio1'],
                'field24b'=> $summaryData['comprehensive_incomes_year2'],
                'field24c'=> $summaryData['balance_sheet_asset_liability_ratio2'],
                'field25'=> $summaryData['future_planning_en'] // 未来规划概况
            ],
            'page5' => [  // 公司概述
                'field1' => $establishmentDateYear,
                'field2' => $companyProfileData['group_name_en'],
                'field3' => $companyProfileData['main_producer_en'],
                'field4' => $industryNameEn[$companyProfileData['industry_name_en']],
                'field4a' => $companyProfileData['main_industry_direction_en'],
                'field5' => $companyProfileData['main_product_en'],
                'field5a' => $companyProfileOtherData['summary_of_company_profile_en'],
                'field6' => $companyBusinessesData['company_name_en'], // 公司工商情况
                'field7' => $companyBusinessesData['used_name_en'],
                'field8' => $companyBusinessesData['credit_code'],
                'field9' => $companyBusinessesData['registered_country_en'],
                'field10' => $companyBusinessesData['establishment_date'],
                'field11' => $companyBusinessesData['company_type_en'],
                'field12' => $companyBusinessesData['register_capital'],
                'field13' => $companyBusinessesData['paid_capital'],
                'field14' => $companyBusinessesData['registered_address_en'],
                'field15' => $companyBusinessesData['operating_address_en'],
                'field16' => $companyBusinessesData['business_scope_en'],
                'field17' => $companyBusinessesData['annual_inspection'],
                'field18' => $companyBusinessesData['operating_period_en'],
                'field19' => $managementIntroductionsEn, // 管理层介绍
                'field20' => $shareholdersEn,  // 股东情况（仅罗列前五大股东）
            ],
            'page6' => [ //业务概述
                'field1' => $mainProductCategoriesEn, // 1.主要产品类别
                'field2' => $productionEquipmentEn, // 2.前五大主要生产设备
                'field3' => $officeSpacesEn, // 3.生产及办公面积
                'field4' => $employeesDataYear, // // 员工人数
                'field5' => $employeesDataMonth,
                'field6' => $employeesDataDay,
                'field7' => $employeesData['employee_quantity_total'],
                'field8' => $employeesData['employee_quantity_social_insurance'],
                'field9' => $roductionDeliveriesEn, // 5. 生产与交付
                'field10' => $iMian['capacity_calculation_formula'],
                'field11' => $iMian['capacity_year'],
                'field12' => $iMian['capacity_quantity'],
                'field13' => $iMian['capacity_year'],
                'field14' => $iMian['capacity_savings_account'],
                'field15' => $qualityAssurancesEn, //质量保证与控制
                'field16' => $rawMaterialsEn, // 7.前十主要原材料及来源占比
                'field17' => $afterSalesData['after_sales_standard_en'],// 8.售后服务及保证
                'field18' => $afterSalesData['after_sales_mode_en'],
                'field19' => $afterSalesData['after_sales_response_en'],
                'field20' => $afterSalesData['after_sales_coverage_en'],
            ],
            'page7' => [
                'field1' => $customerSituationEn, // 1.前五大主要客户情况
                'field2' => $productApplicationCasesEn, // 2.产品应用案例
                'field3' => $honorsEn, // 3.所获荣誉
                'field4' => $commendatoryLettersEn, // 4.司所获表扬信
            ],
            'page8' => [
                'field1' => $qualificationsEn, // 1.资质及认证
                'field2' => $intellectualPropertiesEn, // 2.知识产权
                'field3' => $significantContractsEn, // 3.重大合同
                'field4' => $litigationArbitrationCasesEn, // 4.重大诉讼及仲裁信息
                'field5' => $administrativePenaltyRecordsEn, // 5.行政处罚记录
                'field6' => $dishonestyRecordsEn, // 6.失信记录
            ],
            'page9' => [ // 财务部分
                'field4'=>  $comprehensiveIncomes[0]['accounting_name_en'],
                'field1' => $comprehensiveIncomes,  //利润表
                'field2' => $cashFlow, // 现金流量表
                'field3' => $balanceSheet// 资产负债表
            ],
            'page10'=> [ // 未来规划
                'field1'=> $iMian['future_planning_detail_en']
            ],
            'page11'=> [ // 公司附件
                'field1'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 56)->pluck('file_path')->toArray(),// 营业执照
                'field2'=> [$gspData['company_public_report']],// 企业信息公示
                'field3'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 51)->pluck('file_path')->toArray(), // 参保证明
                'field4'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 57)->pluck('file_path')->toArray(), // 企业征信
                'field5'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 17)->pluck('file_path')->toArray(), // 资质与认证证书
                'field6'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->whereIn('file_id', [53,54])->pluck('file_path')->toArray(), // 荣誉证书与表扬信
                'field7'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 52)->pluck('file_path')->toArray(), //注册商标证书
                'field8'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 62)->pluck('file_path')->toArray(), //专利证书
                'field9'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 63)->pluck('file_path')->toArray(), //著作权登记证书
                'field10'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 50)->pluck('file_path')->toArray(), //租赁合同/不动产权证/不动产查册
                'field11'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 41)->pluck('file_path')->toArray(), // 主要产品图片
                'field12'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 42)->pluck('file_path')->toArray(), // 生产设备
                'field13'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 43)->pluck('file_path')->toArray(), // 生产及办公面积
                'field14'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 61)->pluck('file_path')->toArray(), // 审计报告
                'field15'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 48)->pluck('file_path')->toArray(), // 控股股东、实际控制人声明
                'field16'=> CompanyGspReport::where('gsp_id', $id)->where('status', 1)->where('file_id', 49)->pluck('file_path')->toArray(), // 公司全体董事、监事、高级管理人员声明
            ]
        ];


        // ==========中文版===============
        // 合伙人查看
        $filename = $gspData['name'].'_'.$reportNumber.'_'.$reportTime.'.pdf';
        $url = (new GspReportOutputService($filename, $data))->output(1);
        // 工作人员查看
        $filename2 = $gspData['name'].'_'.$reportNumber.'_'.$reportTime.'_admin.pdf';
        (new GspReportOutputService($filename2, $data))->output(2);

        // ==========英文版===============
        $filename_en = $gspData['name'].'_'.$reportNumber.'_'.$reportTime.'_en.pdf';
        (new GspEnReportOutputService($filename_en, $data_en))->output(1);
        $filename_en2 = $gspData['name'].'_'.$reportNumber.'_'.$reportTime.'_en_admin.pdf';
        (new GspEnReportOutputService($filename_en2, $data_en))->output(2);

        CompanyGsp::where('id', $id)->update([
            'report_url' => $url,
            'report_number'=> $reportNumber,
            'report_time'  => $reportTime
        ]);
    }

    protected function extraTableHandle($tableData,$tableName, $pid, $isEn = false){
        // 制作表头
        $columns = DB::select(
            "SELECT 
                COLUMN_NAME AS name, 
                COLUMN_TYPE AS type, 
                COLUMN_COMMENT AS comment 
            FROM information_schema.COLUMNS 
            WHERE TABLE_NAME = ? 
            AND TABLE_SCHEMA = DATABASE() 
            AND COLUMN_NAME <> 'created_at'
            AND COLUMN_NAME <> 'updated_at'
            AND COLUMN_NAME <> 'id'
            AND COLUMN_NAME <> 'pid'
            ORDER BY 
                ORDINAL_POSITION",
            [$tableName]
        );
        $columnType         = array_column($columns, 'type', 'name');
        //勾选了无记录
        $main         =  GspInvestigationMain::where([
            ['pid', '=', $pid],
        ])->first();
        $checkValue         = $main["no_{$tableName}"] ?? 0;
        if (!empty($checkValue)) $tableData = [];

        foreach ($tableData as $key => $value) {
            foreach ($value as $k => $v) {
                if (str_ends_with($k, "_en") && !$isEn) {    //保留中文 删除英文
                    unset($value[$k]);
                    continue;
                }else if(!str_ends_with($k, "_en") && $isEn){  //保留英文 删除中文
                    unset($value[$k]);
                    continue;
                }                
                if (!empty(GspInvestigationMain::$select[$tableName])) {
                    if (!empty(GspInvestigationMain::$select[$tableName][$k])) {
                        $arr = array_column(GspInvestigationMain::$select[$tableName][$k], 'label', 'value');
                        $value[$k] = $arr[$v] ?? '';
                    }
                }
                //转换国家 和地区
                if (!empty($columnType[$k]) && $k == 'country') {
                    $countryModel = GspInvestigationController::countriesData()[$v] ?? [];
                    // $countryModel = CountryModel::where('countryID', $v)->first();
                    $value[$k] = $countryModel['countryZH'] ?? $v;
                    if (!empty($value['region'])) {
                        $value[$k] = $value[$k] . ' - ' . $value['region'];
                        unset($value['region']);
                    }
                }
               //转换国家 和地区 英文
               if (!empty($columnType[$k]) && $k == 'country_en') {
                    $countryModel = GspInvestigationController::countriesData()[$v] ?? [];
                    // $countryModel = CountryModel::where('countryID', $v)->first();
                    $value[$k] = $countryModel['countryEN'] ?? $v;
                    if (!empty($value['region_en'])) {
                        $value[$k] = $value[$k] . ' - ' . $value['region_en'];
                        unset($value['region_en']);
                    }
                }
                //转换日期
                if (!empty($columnType[$k]) && $columnType[$k] == 'date') {
                    $date = new \DateTime($v);
                    // $value[$k] = $date->format('Y年m月d日');
                    $value[$k] = $date->format('Y-m-d');
                }
                //百分比处理
                if (!empty(GspInvestigationMain::$percent_type[$tableName])) {
                    if (!empty(GspInvestigationMain::$percent_type[$tableName][$k])) {
                        $value[$k] = $v . '%';
                    }
                }
                //处理知识产权图片
                if($tableName == 'cna_gsp_investigation_intellectual_properties'){
                    if($k == 'intellectual_property_type' && $v == 40){
                        $brand = CompanyGspReport::where([
                            ['id', '=', $value['name']]
                        ])->select('id', 'file_name', 'file_path')->first();
                        $value['name'] = $brand['file_path'];
                    }
                    if($k == 'intellectual_property_type'){
                        $arr = array_column(GspInvestigationMain::selectOption()[$tableName][$k], 'label', 'value');
                        $value[$k] = $arr[$v] ?? '';                        
                    }
                }
            }
            $tableData[$key] = $value;
        }
        foreach ($tableData as $k => $v) {
            $tableData[$k] = array_values($v);
        }
        if (empty($tableData)) {  //处理空数据
            $tableData = [];
            $tableDataNull = [];
            foreach ($columnType as $tableHeaderValue) {
                $tableDataNull[] = '\\';
            }
            $tableData[] = $tableDataNull;
        }
        return $tableData;
    }
}
