<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\InterviewQuestion;
use App\Models\InterviewQuestionNext;
use Illuminate\Http\Request;

class InterviewQuestionController extends Controller
{
    public function index(Request $request)
    {
        $pageSize = $request->get('page_size', 10);
        $keyword = $request->get('keyword', '');
        $module = $request->get('module', '');

        $list = InterviewQuestion::with('nextQuestions')
            ->when($keyword != '', function ($query) use ($keyword) {
                $query->where('question', 'like', '%' . $keyword . '%')
                    ->orWhereHas('nextQuestions', function ($q) use ($keyword) {
                        $q->where('next', 'like', '%' . $keyword . '%');
                    });
            })
            ->when($module != '', function ($q) use ($module) {
                $q->where('module', $module);
            })
            ->paginate($pageSize);

        $items = $list->items();

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    public function modules()
    {
        $modules = InterviewQuestion::MODULE_MAP;
        $formatModules = [];
        foreach ($modules as $k => $m) {
            $formatModules[] = [
                'value' => $k,
                'label' => $m,
            ];
        }
        return responseSuccess($formatModules);
    }

    public function store(Request $request)
    {
        $request->validate([
            'module' => 'required|integer|in:' . implode(',', array_keys(InterviewQuestion::MODULE_MAP)),
            'question' => 'required|string',
        ]);

        InterviewQuestion::create([
            'module' => $request->module,
            'module_name' => InterviewQuestion::MODULE_MAP[$request->module],
            'question' => $request->question,
        ]);

        return responseSuccess();
    }

    public function update(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
            'module' => 'required|integer|in:' . implode(',', array_keys(InterviewQuestion::MODULE_MAP)),
            'question' => 'required|string',
        ]);

        InterviewQuestion::where('id', $request->id)->update([
            'module' => $request->module,
            'module_name' => InterviewQuestion::MODULE_MAP[$request->module],
            'question' => $request->question,
        ]);

        return responseSuccess();
    }

    public function delete(Request $request)
    {
        $request->validate([
            'ids' => 'required|string',
        ]);

        $ids = explode(',', $request->ids);

        $existedCount = InterviewQuestion::whereIn('id', $ids)->count();
        if ($existedCount != count($ids)) {
            return responseFail('数据有误，删除失败');
        }

        InterviewQuestion::whereIn('id', $ids)->delete();

        return responseSuccess();
    }

    // 基础问题的追问
    public function nextQuestions(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
            'keyword' => 'nullable|string',
        ]);

        $interviewQuestion = InterviewQuestion::find($request->id);

        $nextQuestions = $interviewQuestion->nextQuestions()
            ->when($request->keyword != null, function ($query) use ($request) {
                $query->where('next', 'like', '%' . $request->keyword . '%');
            })
            ->get();

        return responseSuccess($nextQuestions);
    }

    public function storeNextQuestions(Request $request)
    {
        $request->validate([
            'id' => 'required|integer', // base_id
            'next' => 'required|string',
        ]);

        $baseQuestion = InterviewQuestion::find($request->id);

        $baseQuestion->nextQuestions()->create([
            'next' => $request->next,
        ]);

        return responseSuccess();
    }

    public function updateNextQuestions(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
            'next' => 'required|string',
        ]);

        InterviewQuestionNext::where('id', $request->id)->update([
            'next' => $request->next,
        ]);

        return responseSuccess();
    }

    public function deleteNextQuestions(Request $request)
    {
        $request->validate([
            'ids' => 'required|string',
        ]);

        $ids = explode(',', $request->ids);

        $existedCount = InterviewQuestionNext::whereIn('id', $ids)->count();
        if ($existedCount != count($ids)) {
            return responseFail('数据有误，删除失败');
        }

        InterviewQuestionNext::whereIn('id', $ids)->delete();

        return responseSuccess();
    }
}
