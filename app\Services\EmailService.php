<?php

namespace App\Services;

use App\Models\EmailTemplateModel;
use App\Models\ProfileInfoModel;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Dm\V20151123\Dm;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use AlibabaCloud\SDK\Dm\V20151123\Models\SingleSendMailRequest;
use App\Exceptions\DefaultException;
use App\Models\EmailDataModel;
use App\Models\Setting;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class EmailService {

    private $emailDataModel;

    public function __construct(EmailDataModel $emailDataModel)
    {
        $this->emailDataModel = $emailDataModel;
    }
    
    /**
     * Method 初始化阿里Dm客户端
     *
     * @return void
     */
    private function initAliDmClient()
    {
        $config = new Config([
            "accessKeyId" => env('ALI_ACCESS_KEY_ID'),
            "accessKeySecret" => env('ALI_ACCESS_KEY_SECRET')
        ]);
        $config->endpoint = "dm.aliyuncs.com";
        $client = new Dm($config);

        return $client;
    }

    /**
     * Method 发送验证码邮件
     *
     * @param $toAddress $toAddress [邮箱地址]
     * @param $subject $subject [主题]
     * @param $body $body [内容]
     * @param $scene $scene [场景]
     *
     * @return void
     */
    public function sendVerificationCodeEmail($toAddress, $scene, $lang = 'zh', $profileId = 0)
    {
        try {

            $this->checkSendEmailLimit($toAddress, $scene);
            $this->checkSendEmailMinLimit($toAddress, $scene);

            $client = $this->initAliDmClient();

            // 获取邮箱模板
            $templateData  = $this->getTemplateData($toAddress, 'VerificationCode', $profileId);
            if (empty($templateData)) {
                throw new DefaultException('邮件发送失败');
            }
            $template = $templateData['htmlBody'];
            $subject = $templateData['subject'];
            $code = rand(100000, 999999);
            $body = str_replace('{{code}}', $code, $template);
            $data = [
                'accountName' => config('email.account_name'),
                'addressType' => config('email.address_type'),
                'replyToAddress' => config('email.reply_to_address'),
                'toAddress' => $toAddress,
                'subject' => $subject.$code,
                'htmlBody' => $body,
                'fromAlias' => config('email.from_alias')
            ];

            $singleSendMailRequest = new SingleSendMailRequest($data);
            $runtime = new RuntimeOptions();
            $sendResult = $client->singleSendMailWithOptions($singleSendMailRequest, $runtime);
            $sendResult = json_decode(json_encode($sendResult), true);
            if (isset($sendResult) && $sendResult['statusCode'] == 200) {
                $this->emailDataModel::create([
                    'emailProfileID' => $profileId,
                    'emailAddress' => $toAddress,
                    'emailCode' => $code,
                    'emailState' => 0,
                    'emailExpirationTime' => time() + config('email.email_exp_time'),
                    'emailScene' => $scene,
                ]);

                return responseSuccess();
            } else {
                throw new DefaultException('邮件发送失败');
            }

        } catch(\Exception $e) {
            throw new DefaultException($e->getMessage());
        }
    }
    
    /**
     * Method 验证邮件
     *
     * @param $email $email [邮箱地址]
     * @param $code $code [验证码]
     * @param $scene $scene [场景]
     *
     * @return void
     */
    public function verifyEmail($email, $code, $scene, $profileId = 0)
    {
        $data = $this->emailDataModel::where('emailAddress', $email)
                                        ->where('emailProfileID', $profileId)
                                        ->where('emailCode', $code)
                                        ->where('emailState', 0)
                                        ->where('emailExpirationTime', '>', time())
                                        ->where('emailScene', $scene)
                                        ->orderByDesc('created_at')
                                        ->first();

        if (!$data) {
            throw new DefaultException('邮件验证码错误');
        }

        $data->emailState = 1;
        $data->save();
    }
    
    /**
     * Method 检验发送邮件频次
     *
     * @param $email $email [邮箱]
     * @param $scene $scene [场景]
     *
     * @return void
     */
    public function checkSendEmailLimit($email, $scene)
    {
        $data = $this->emailDataModel::where('emailAddress', $email)
                                        ->where('emailScene', $scene)
                                        ->whereDate('created_at', Carbon::now()->format('Y-m-d'))
                                        ->count();

        if ($data >= env('EMAIL_SCENE_SEND_LIMIT')) {
            throw new DefaultException('发送邮件频次过高，请稍等');
        }                                 
    }
    
    /**
     * Method 检验发送邮件一分钟内频次
     *
     * @param $email $email [explicite description]
     * @param $scene $scene [explicite description]
     *
     * @return void
     */
    public function checkSendEmailMinLimit($email, $scene)
    {
        $data = $this->emailDataModel::where('emailAddress', $email)
                                        ->where('emailState', 0)
                                        ->where('created_at', '>=', Carbon::now()->subSecond(env('EMAIL_SEND_TIME_LIMIT')))
                                        ->where('emailScene', $scene)
                                        ->orderByDesc('created_at')
                                        ->first();

        if ($data) {
            throw new DefaultException('请在60秒后再发送邮件');
        }  
    }

    public function sendPasswordEmail($email)
    {
        try {

            $client = $this->initAliDmClient();

            // 获取邮箱模板
            $templateData  = $this->getTemplateData($email, 'PartnerSuccess');
            if (empty($templateData)) {
                throw new DefaultException('邮件发送失败');
            }
            $template = $templateData['htmlBody'];
            $subject = $templateData['subject'];
            //$body = str_replace('{{password}}', $password, $template);
            $body = $template;

            $data = [
                'accountName' => config('email.account_name'),
                'addressType' => config('email.address_type'),
                'replyToAddress' => config('email.reply_to_address'),
                'toAddress' => $email,
                'subject' => $subject,
                'htmlBody' => $body,
                'fromAlias' => config('email.from_alias')
            ];

            $singleSendMailRequest = new SingleSendMailRequest($data);
            $runtime = new RuntimeOptions();
            $sendResult = $client->singleSendMailWithOptions($singleSendMailRequest, $runtime);
            $sendResult = json_decode(json_encode($sendResult), true);

        } catch (\Exception $e) {

        }
    }

    public function sendPasswordCompanyEmail($email, $password)
    {
        try {

            $client = $this->initAliDmClient();

            // 获取邮箱模板
            $templateData  = $this->getTemplateData($email, 'CompanySuccess');
            if (empty($templateData)) {
                throw new DefaultException('邮件发送失败');
            }
            $template = $templateData['htmlBody'];
            $subject = $templateData['subject'];
            $body = str_replace('{{password}}', $password, $template);

            $data = [
                'accountName' => config('email.account_name'),
                'addressType' => config('email.address_type'),
                'replyToAddress' => config('email.reply_to_address'),
                'toAddress' => $email,
                'subject' => $subject,
                'htmlBody' => $body,
                'fromAlias' => config('email.from_alias')
            ];

            $singleSendMailRequest = new SingleSendMailRequest($data);
            $runtime = new RuntimeOptions();
            $sendResult = $client->singleSendMailWithOptions($singleSendMailRequest, $runtime);
            $sendResult = json_decode(json_encode($sendResult), true);

        } catch (\Exception $e) {

        }
    }

    /**
     * 根据当前语言和场景查询邮箱模板
     * @param $email
     * @param $code
     * @param $profileId  用户ID存在优先从这查询
     * @return array
     */
    public function getTemplateData($email, $code, $profileId = 0)
    {
        if (!$profileId) {
            $profileId = ProfileInfoModel::where('profileEmail', $email)->value('profileID');
        }
        $lang = Setting::where('profile_id', $profileId)->value('language');
        $lang = empty($lang) ? 'ZH': strtoupper($lang);
        $data = [];
        // 查询邮箱模板
        $templateData = EmailTemplateModel::where('eventCode', $code)->first();
        if ($templateData) {
            $data = [
                'subject' => $templateData['emailTitle'.$lang],
                'htmlBody' => $templateData['emailDescription'.$lang],
            ];
        }

        return $data;
    }

    public function sendEmail($toEmail, $subject, $body)
    {
        try {

            $client = $this->initAliDmClient();
            $data = [
                'accountName' => config('email.account_name'),
                'addressType' => config('email.address_type'),
                'replyToAddress' => config('email.reply_to_address'),
                'toAddress' => $toEmail,
                'subject' => $subject,
                'htmlBody' => $body,
                'fromAlias' => config('email.from_alias')
            ];


            $singleSendMailRequest = new SingleSendMailRequest($data);
            $runtime = new RuntimeOptions();
            $sendResult = $client->singleSendMailWithOptions($singleSendMailRequest, $runtime);
            $sendResult = json_decode(json_encode($sendResult), true);

        } catch (\Exception $e) {

        }
    }


    /**
     * 根据电邮模板发邮件
     * @param $email 用户邮箱
     * @param $eventCode 用户场景
     * @param $param 动态替换参数
     * @return void
     */
    public function sendCommonEmail($email, $eventCode, $param = [])
    {
        try {

            $client = $this->initAliDmClient();

            // 获取邮箱模板
            $templateData  = $this->getTemplateData($email, $eventCode);
            if (empty($templateData)) {
                throw new DefaultException('邮件发送失败');
            }
            $template = $templateData['htmlBody'];
            $subject = $templateData['subject'];
            $body = $template;

            // 替换动态参数
            if ($param) {
                foreach ($param as $key => $val) {
                    $body = str_replace('{{'.$key.'}}', $val, $body);
                }
            }

            $data = [
                'accountName' => config('email.account_name'),
                'addressType' => config('email.address_type'),
                'replyToAddress' => config('email.reply_to_address'),
                'toAddress' => $email,
                'subject' => $subject,
                'htmlBody' => $body,
                'fromAlias' => config('email.from_alias')
            ];

            $singleSendMailRequest = new SingleSendMailRequest($data);
            $runtime = new RuntimeOptions();
            $sendResult = $client->singleSendMailWithOptions($singleSendMailRequest, $runtime);
            $sendResult = json_decode(json_encode($sendResult), true);

        } catch (\Exception $e) {
            Log::info($e->getMessage());
        }
    }

}