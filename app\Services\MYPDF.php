<?php
namespace App\Services;

use Exception;
use setasign\Fpdi\Tcpdf\Fpdi as TCPDF1;

// 重写 Footer 方法
class MYPDF extends TCPDF1 {



    // 重写Chapter方法，记录章节信息用于目录
    public function Chapter( $title,$content) {
        $this->AddPage();

        // 记录章节信息（标题和当前页码）
        $this->toc[] = array(
            'title' => $title,
            'page' => $this->getPage(),
            'level' => 1
        );

        // 章节内容
        $this->writeHTML($content, true, false, true, false, '');

    }

    // 生成目录
    public function createTOC() {
        $this->AddPage();

        foreach ($this->toc as $item) {
            // 计算点线长度
            $dots = str_repeat('.', 100 - (strlen($item['title']) + strlen((string)$item['page'])));

            // 目录项
            $this->Cell(0, 10, $item['title'] . $dots . $item['page'], 0, 1, 'L');
        }
    }

    /**
     * 重写页码样式
     * @return void
     */
    public function Footer() {


        if ($this->getPage() > 5) {

            $this->SetY(-15);
            $this->SetFont('helvetica', '', 8);

            $pageN = $this->getPageNumGroupAlias();
            $totalPages = $this->getPageGroupAlias();

            $this->Cell(0, 10,
                $pageN.' / '.$totalPages ,
                0, false, 'C'
            );
        }
    }
}
