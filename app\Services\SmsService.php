<?php

namespace App\Services;

use AlibabaCloud\SDK\Dysmsapi\V20170525\Dysmsapi;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Dysmsapi\V20170525\Models\SendSmsRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use App\Models\PhoneDataModel;
use App\Exceptions\DefaultException;
use App\Models\CountryModel;
use Illuminate\Support\Carbon;

class SmsService
{
    private $phoneDataModel;
    const VERIFY_CODE_MIN = 100000;
    const VERIFY_CODE_MAX = 999999;

    public function __construct(PhoneDataModel $phoneDataModel)
    {
        $this->phoneDataModel = $phoneDataModel;
    }

    public function sendVerificationCodeSms($phone, $scene, $countryCode, $profileId = 0)
    {
        try {

            $this->checkSendSmsLimit($phone, $scene);
            $this->checkSendEmailMinLimit($phone, $scene);

            $code = rand(self::VERIFY_CODE_MIN, self::VERIFY_CODE_MAX);
            $config = new Config([
                'accessKeyId' => config('sms.accessKeyId'),
                'accessKeySecret' => config('sms.accessKeySecret')
            ]);
            $config->endpoint = "dysmsapi.aliyuncs.com";
            $client =  new Dysmsapi($config);
            $sendSmsRequest = new SendSmsRequest([
                'phoneNumbers' => $countryCode != '86' ? $countryCode.$phone : $phone,
                'signName' => config('sms.signName'),
                'templateCode' => $countryCode != '86' ? config('sms.globalTemplateCode') : config('sms.templateCode'),
                'templateParam' => '{code:'.$code.'}'
            ]);
            $runtime = new RuntimeOptions([]);
            $result = $client->sendSmsWithOptions($sendSmsRequest, $runtime);
            $sendResult = json_decode(json_encode($result), true);
            if (isset($sendResult) && $sendResult['statusCode'] == 200) {
                $this->phoneDataModel::create([
                    'phoneProfileID' => $profileId,
                    'phone' => $phone,
                    'phoneCode' => $code,
                    'phoneState' => 0,
                    'phoneExpirationTime' => time() + 10 * 60,
                    'phoneScene' => $scene,
                    'countryCode' => $countryCode
                ]);

                return responseSuccess();
            } else {
                throw new DefaultException('短信发送失败');
            }

        } catch (\Exception $e) {
            throw new DefaultException('短信发送失败');
        }
    }

    /**
     * Method 验证邮件
     *
     * @param $email $email [邮箱地址]
     * @param $code $code [验证码]
     * @param $scene $scene [场景]
     *
     * @return void
     */
    public function verifySms($phone, $code, $scene, $profileId = 0)
    {
        $data = $this->phoneDataModel::where('phone', $phone)
                                        ->where('phoneProfileID', $profileId)
                                        ->where('phoneCode', $code)
                                        ->where('phoneState', 0)
                                        ->where('phoneExpirationTime', '>', time())
                                        ->where('phoneScene', $scene)
                                        ->orderByDesc('created_at')
                                        ->first();

        if (!$data) {
            throw new DefaultException('短信验证码错误');
        }

        $data->phoneState = 1;
        $data->save();
    }
    
    /**
     * Method 检验发送邮件频次
     *
     * @param $email $email [邮箱]
     * @param $scene $scene [场景]
     *
     * @return void
     */
    public function checkSendSmsLimit($phone, $scene)
    {
        $data = $this->phoneDataModel::where('phone', $phone)
                                        ->where('phoneScene', $scene)
                                        ->whereDate('created_at', Carbon::now()->format('Y-m-d'))
                                        ->count();

        if ($data >= config('sms.scene_send_limit')) {
            throw new DefaultException('发送短信频次过高，请稍等');
        }                                 
    }
    
    /**
     * Method 检验发送邮件一分钟内频次
     *
     * @param $email $email [explicite description]
     * @param $scene $scene [explicite description]
     *
     * @return void
     */
    public function checkSendEmailMinLimit($phone, $scene)
    {
        $data = $this->phoneDataModel::where('phone', $phone)
                                        ->where('phoneState', 0)
                                        ->where('created_at', '>=', Carbon::now()->subSecond(config('sms.send_time_limit')))
                                        ->where('phoneScene', $scene)
                                        ->orderByDesc('created_at')
                                        ->first();

        if ($data) {
            throw new DefaultException('请在60秒后再发送短信');
        }  
    }

    public function sendSms($prefixID, $phone, $templateCode, $templateParam)
    {
        try {
            $config = new Config([
                'accessKeyId' => config('sms.accessKeyId'),
                'accessKeySecret' => config('sms.accessKeySecret')
            ]);
            $config->endpoint = "dysmsapi.aliyuncs.com";
            $client =  new Dysmsapi($config);
            $resetPhoneData = (new CountryModel())->resetPhone($prefixID, $phone);
            $countryCode = $resetPhoneData['countryCode'];
            $sendSmsRequest = new SendSmsRequest([
                'phoneNumbers' => $countryCode != '86' ? $countryCode.$phone : $phone,
                'signName' => config('sms.signName'),
                'templateCode' => $templateCode,
                'templateParam' => $templateParam,
            ]); //todo
            $runtime = new RuntimeOptions([]);
            $result = $client->sendSmsWithOptions($sendSmsRequest, $runtime);
        } catch (\Exception $e) {

        }
    }
}