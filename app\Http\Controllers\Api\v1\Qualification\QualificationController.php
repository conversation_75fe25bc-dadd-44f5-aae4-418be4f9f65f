<?php

namespace App\Http\Controllers\Api\v1\Qualification;

use App\Http\Controllers\Controller;
use App\Models\Qualification;
use App\Services\OssService;
use Illuminate\Http\Request;

class QualificationController extends Controller
{
    public function info(Request $request)
    {
        $user = $request->attributes->get('user');
        $data = Qualification::where('profileID', $user['profileID'])->first();
        if ($data) {
            $data['education'] = OssService::link($data['education']);
            $data['certificate'] = OssService::link($data['certificate']);
        }

        return responseSuccess($data);
    }

    public function edit(Request $request)
    {
        $body = $request->validate([
            'education' => ['file', 'mimes:jpg,jpeg,png,pdf', 'max:' . (env('ALLOW_FILE_SIZE') * 1024)],
            'certificate' => ['file', 'mimes:jpg,jpeg,png,pdf', 'max:' . (env('ALLOW_FILE_SIZE') * 1024)],
        ]);
        if (empty($body)) {
            return responseSuccess();
        }
        $educationPath = OssService::upload($request->file('education'));
        $certificatePath = OssService::upload($request->file('certificate'));
        $user = $request->attributes->get('user');
        Qualification::updateOrCreate(['profileID' => $user['profileID']], [
            'education' => $educationPath ?: '',
            'certificate' => $certificatePath ?: '',
        ]);

        return responseSuccess();
    }
}
