<?php

namespace App\Jobs;

use App\Models\AiCheck;
use App\Models\PartnerInterviewModel;
use App\Models\PartnerInterviewQuestionsModel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
class SetPartnerInterviewQuestions implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $interview;

    /**
     * Create a new job instance.
     */
    public function __construct(PartnerInterviewModel $interview)
    {
        $this->interview = $interview;

    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Questions resume for interview ID: " . $this->interview->id);

        try {
            Log::info('Starting ProcessQuestions for Interview ID: ' . $this->interview->id);

            // 从数据库中动态抽题
            $questions = PartnerInterviewModel::getQuestions();

            foreach ($questions as $question) {
                // 存入数据库(面试问题表)
                PartnerInterviewQuestionsModel::create([
                    'interview_id' => $this->interview->id,
                    'module' => $question->module,
                    'question' => $question->question,
                ]);
            }
            Log::info("Questions processing completed for interview ID: " . $this->interview->id);
        } catch (\Exception $e) {
            // 记录异常信息
            Log::error('Error in ProcessQuestions: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
        }
    }
}
