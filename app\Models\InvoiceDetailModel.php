<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvoiceDetailModel extends Model
{
    use HasFactory;

    protected $table = 'cna_invoice_detail';
    protected $guarded = [];

    const TYPE_REGISTER = 1; // 加盟商入驻
    const TYPE_GSP_REGISTER = 2; // 绿智入驻

    public static $typeList = [
        '加盟商入驻',
        '绿智入驻',
    ];

    public static $statusList = [
        '待开票',
        '已申请',
        '已开票'
    ];


    const STATUS_NO = 0; // 待开票
    const STATUS_APPLY = 1; // 已申请
    const STATUS_SUCCESS = 2; // 已开票

    public function profileInfo()
    {
        return $this->belongsTo(ProfileInfoModel::class,'profile_id');
    }

    /**
     * 查看是否开票
     * @param $type int 平台项目
     * @param $project string 支付的项目
     * @param $obj_id int 对应业务表的ID
     * @return mixed
     */
    public static function checkInvoice($type, $project, $obj_id)
    {
        $record  = self::where('type', $type)
            ->where('project', $project)
            ->where('obj_id', $obj_id)
            ->first();

        return $record;
    }

    /**
     * 开发票的相关业务
     * @param $type
     * @param $obj_id
     * @return string
     */
    public static function objName($type, $obj_id) {
        if ($type == self::TYPE_GSP_REGISTER) { // 绿智入驻
            return CompanyGsp::where('id', $obj_id)->value('name');
        }

        return '';
    }

}