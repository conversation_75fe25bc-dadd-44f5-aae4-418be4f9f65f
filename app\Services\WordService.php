<?php
namespace App\Services;

use Mpdf\Mpdf;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Html;
use PhpOffice\PhpWord\Style\Paragraph;
use setasign\Fpdi\Tcpdf\Fpdi as TCPDF1;
use TCPDF_FONTS;


class WordService
{
    public $phpWord;
    public $section;

    public $font;

    public function __construct()
    {
        // 创建一个新的 PHPWord 实例
        $this->phpWord = new PhpWord();
        // 添加一个文档部分
        $this->section = $this->phpWord->addSection();

        // 设置默认字体样式为宋体
        $this->font = 'SimSun';
    }

    /**
     * 自定义字体
     * @return void
     */
    public function setFont($fontName)
    {
        // 设置字体样式为宋体
        $this->font = $fontName;

    }

    /**
     * 添加文本
     * @param $text 内容
     * @param $size 字号
     * @param $bold 是否加粗
     * @param $align 对齐方式
     * @return void
     */
    public function addTitle($text, $size = 14, $bold = true, $align = 'center')
    {
        // 设置段落样式
        $paragraphStyle = new Paragraph();
        $paragraphStyle->setAlignment($align); // 设置对齐方式为左对齐

        // 添加文本
        $this->section->addText($text, ['name' => $this->font, 'size' => $size, 'bold' => $bold ], $paragraphStyle);
    }

    /**
     * 添加表格
     * @param $htmlTable
     * @return void
     */
    public function addTable($htmlTable)
    {
        // 将 HTML 表格插入到文档中
        Html::addHtml($this->section, $htmlTable);
    }

    /**
     * 换行符
     * @param $count
     * @return void
     */
    public function addTextBreak($count = 1)
    {
        // 添加换行符
        for ($i = 0; $i < $count; $i++) {
            $this->section->addTextBreak();
        }
    }


    /**
     * 保存文档
     * @param $filename
     * @return void
     * @throws \PhpOffice\PhpWord\Exception\Exception
     */
    public function save($filename)
    {
        // 保存文档
        //$objWriter = IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter = IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter->save($filename);

    }

    /**
     * 保存转pdf格式
     * @return void
     */
    public function savePdf($filename)
    {

        $tempHtmlFile = dirname($filename).'/'.time().'.html';
        IOFactory::createWriter($this->phpWord, 'HTML')->save($tempHtmlFile);

        // 读取 HTML 文件内容
        $htmlContent = file_get_contents($tempHtmlFile);

        // 创建一个新的 TCPDF 实例
        $pdf = new TCPDF1(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

        // 添加一个页面
        $pdf->AddPage();

        // 设置字体路径
        $fontFile = storage_path('fonts/simfang.ttf');
        $newFile = storage_path('app/public/fonts/simfang.ttf');

        // 检查目标目录是否存在，如果不存在则创建
        if (!is_dir(dirname($newFile))) {
            mkdir(dirname($newFile), 0755, true);
        }

        // 检查字体文件是否存在，如果不存在则复制
        if (!file_exists($newFile)) {
            if (!copy($fontFile, $newFile)) {
                die("复制字体文件失败: $fontFile 到 $newFile");
            }
        }

        // 添加 TTF 字体
        $fontname = TCPDF_FONTS::addTTFfont($newFile, 'TrueTypeUnicode', '', 32);

        // 设置字体
        $pdf->SetFont($fontname, '', 12, '', true);

        $htmlContent = file_get_contents($tempHtmlFile);

        $pdf->writeHTML($htmlContent, true, false, true, false, '');
        $pdf->Output($filename, 'F');

        unlink($tempHtmlFile);
    }
}
