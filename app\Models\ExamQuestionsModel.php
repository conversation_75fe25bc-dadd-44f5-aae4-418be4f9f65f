<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExamQuestionsModel extends Model
{
    use SoftDeletes;

    protected $connection = 'mysql_ai_desk';
    protected $table = 'cna_exam_questions';

    const TYPE_RADIO = 0;
    const TYPE_CHECKBOX = 1;

    const TYPE_TEXT = 2;

    const TYPE_ALL = [self::TYPE_RADIO, self::TYPE_CHECKBOX,self::TYPE_TEXT];

    const TYPE_REMARK = [
        self::TYPE_RADIO => '单选题',
        self::TYPE_CHECKBOX => '多选题',
        self::TYPE_TEXT => '文本填写',
    ];

    protected $fillable = ['exam_id','type', 'question', 'option'];

    public function exam(){
        return $this->belongsTo(ExamTypeModel::class, 'exam_id');
    }

}
