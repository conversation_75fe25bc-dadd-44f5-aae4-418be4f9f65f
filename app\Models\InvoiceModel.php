<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvoiceModel extends Model
{
    use HasFactory;

    protected $table = 'cna_invoice';
    protected $guarded = [];

    const TYPE_REGISTER = 1; // 加盟商入驻
    const TYPE_GSP_REGISTER = 2; // 绿智入驻

    public static $typeList = [
        '加盟商入驻',
        '绿智入驻',
    ];

    public function profileInfo()
    {
        return $this->belongsTo(ProfileInfoModel::class,'profile_id');
    }

    /**
     * 获取发票信息
     * @param $type int 平台项目
     * @param $project string 支付的项目
     * @param $obj_id int 对应业务表的ID
     * @return mixed
     */
    public static function getInvoice($type, $obj_id)
    {
        $record  = self::where('type', $type)
            ->where('obj_id', $obj_id)
            ->first();

        return $record;
    }

}