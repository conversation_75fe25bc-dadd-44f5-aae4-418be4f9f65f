<?php
namespace App\Services;

use Fpdi\Fpdi;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpWord\Writer\PDF\DomPDF;
use setasign\Fpdi\Tcpdf\Fpdi as TCPDF1;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use TCPDF_FONTS;

class PdfTableService
{
    private $pdf;
    public $title;  // 表格标题
    public $column; // 表格列
    public $content; // 表格内容
    public $table; // 表数据名称
    public $unit; // 表格单位
    public $desc; // 表格描述
    public $statement; // 表格底部公司声明
    public $bottom; // 底部静态变量内容
    public $fileName = ''; // 文件名
    public $datetime = ''; // 填表日期年月日
    public $lastModifyDate = ''; // 最后修改日期
    public $isChkStatement; // 是否勾选声明
    public $leftMargin = 20; // 左边距
    public $rightMargin = 20; // 右边距
    public $lineHeight = 12; // 行高
    public $columnWidth; // 列宽


    /**
     * @param $title // 表格标题
     * @param $column // 表格列
     * @param $content // 表格内容
     * @param $table // 表数据名称
     * @param $unit // 表格单位
     * @param $desc // 表格描述
     * @param $bottom  // 底部静态变量内容
     * @param $statement // 表格底部公司声明
     * @param $isChkStatement  // 是否勾选声明
     * @param $fileName // 文件名
     * @param $datetime // 填表日期年月日
     * @param $lastModifyDate // 最后修改日期
     */
    public function __construct($title, $column, $content, $table, $unit='', $desc='',  $bottom='',$statement='', $isChkStatement=0, $fileName='', $datetime='', $lastModifyDate='') {

        // 表格相关参数
        $this->title = $title;
        $this->column = $column;
        $this->content = $content;
        $this->table = $table;
        $this->unit = $unit;
        $this->desc = $desc;
        $this->bottom = $bottom;
        $this->statement = $statement;
        $this->isChkStatement = $isChkStatement;
        $this->fileName = $fileName;
        if ($datetime) {
            $this->datetime = $datetime;
        } else {
            $this->datetime = date('Y-m-d H:i:s');
        }
        $this->lastModifyDate = $lastModifyDate;


        // 创建TCPDF实例
        define('K_PATH_FONTS', storage_path('app/public/fonts/'));

        // 创建一个新的 TCPDF 实例
        $this->pdf = new TCPDF1(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
        // 禁用页眉
        $this->pdf->setPrintHeader(false);

        // 添加一个页面
        $this->pdf->AddPage();

        // 需要相关字体移到可访问目录
        $fontFile = ['fonts/simfang.ttf', 'fonts/simhei.ttf','fonts/zapfdingbats.php', 'fonts/helvetica.php'];
        $newFile = ['app/public/fonts/simfang.ttf', 'app/public/fonts/simhei.ttf','app/public/fonts/zapfdingbats.php', 'app/public/fonts/helvetica.php'];

        // 检查目标目录是否存在，如果不存在则创建
        if (!is_dir(dirname(storage_path('app/public/fonts')))) {
            mkdir(dirname(storage_path('app/public/fonts')), 0755, true);
        }

        if ($fontFile) {
            foreach ($fontFile as $key => $val) {
                if (!file_exists(storage_path($newFile[$key]))) {
                    copy(storage_path($val), storage_path($newFile[$key]));
                }
            }
        }

        $fontPath = realpath(storage_path($newFile[0])); // 替换为你的字体文件路径
        $blodFontPath = realpath(storage_path($newFile[1])); // 替换为你的字体文件路径

        // 注册支持粗体的中文字体（如 SimHei.ttf 和 SimHeiBold.ttf）
        $blodFont = TCPDF_FONTS::addTTFfont($blodFontPath, 'TrueTypeUnicode', '', 32);
        $regularFont = TCPDF_FONTS::addTTFfont($fontPath, 'TrueTypeUnicode', '', 32);

        // 设置字体
        $this->pdf->SetFont($blodFont, '', 12);
        $this->pdf->SetFont($regularFont, '', 12);

    }

    /**
     * 生成表格PDF
     * @throws \Fpdi\PdfParser\CrossReference\CrossReferenceException
     * @throws \Fpdi\PdfParser\Filter\FilterException
     * @throws \Fpdi\PdfParser\PdfParserException
     * @throws \Fpdi\PdfParser\Type\PdfTypeException
     * @throws \Fpdi\PdfReader\PdfReaderException
     */
    public  function generatePdf()
    {

        $title = $this->title;

        // 表格单位
        $unit = $this->setTableUnit();

        // 表格底部声明
        $statement = $this->setTableFooter();

        // 底部静态内容
        $bottom = $this->setBottom();

        // 表格宽度
        if ($this->column) {
            $width = ceil( 100 / count($this->column));
        } else {
            $width = ceil( 100 / count($this->content[0]));
        }


        // 表格列
        $column = '';
        if ($this->column) {
            $column = '<tr class="th">';
            foreach ($this->column as $val) {
                $column .= '<td class="td" style="width:'.$width.'%">'.$val.'</td>';
            }
            $column .='</tr>';
        }


        // 表格内容
        $table = '<table  border="0">';
        $table .= $column;
        foreach ($this->content as $row) {
            $table .= '<tr class="th">';
            foreach ($row as $field) {
                // 是否图片插入
                if ($images = $this->isImage($field)) {
                    $imgUrl =  public_path('storage/').$images[0];
                    $table .= '<td class="td" style="width:'.$width.'%;"><img src="' . $imgUrl . '"></td>';
                } else {
                    $table .= '<td class="td" style="width:'.$width.'%">' . $field . '</td>';
                }

            }
            $table .= '</tr>';
        }
        $table .= '</table>';

        $htmlTable = <<<EOD
                <style>
             tr { page-break-inside:avoid; page-break-after:auto }
             td { page-break-inside:avoid; page-break-after:auto }
             table{
              width: 100%;
              border-collapse: collapse;
              margin: 0 auto; /* 居中显示 */
              page-break-inside:auto
            }
            .th{
                height: 40px;
            }
            .td{
                
                font-size: 14px;
                line-height: 30px;
                text-align: center;
                width: 40%;
                border:1px solid #999;
            }
             .td-left{
                
                font-size: 14px;
                line-height: 30px;
                width: 56%;
                margin-left: 10px;
                border: 1px solid black;
            }
            .td-center{
                text-align: center;
                line-height: 30px;
                width:4%;
                border: 1px solid black;
            }
            .text-page2{
                line-height: 24px;
                text-align: left;
                 font-size: 14px;
            }
            .bold {
                font-weight: bold;
            }

        </style>
         <div style="font-size: 18px; text-align: center; ">$title</div><br>
         $unit<br>
        $table<br><br>
        $statement<br>
        $bottom<br><br><br>
        EOD;


        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

        // 底部签名盖章
        $this->signature();

        // 输出 PDF
        $filename = time().Str::random(16).'.pdf';
        $directory = storage_path('app/public/gsp/report/');
        if (!is_dir($directory)) {
            mkdir($directory, 0777, true); // 递归创建目录
        }
        $file = $directory.$filename;
        $this->pdf->Output( $file, 'F');

        // 生成文件验签
        $token = DocService::generateToken('/gsp/report/'.$filename, 'gsp_form', 3600, $this->fileName);
        return $token;
    }


    /**
     * 生成静态表格
     * @return void
     */
    public function generateStaticPdf()
    {

        $title = $this->title;

        // 表格单位
        $unit = $this->setTableUnit();

        // 表格底部声明
        $statement = $this->setTableFooter();

        // 底部静态内容
        $bottom = $this->setBottom();

        $content = '';
        foreach ($this->content as $val) {
            $title1 = $val['title'];
            $text1 = $val['text'];
            if ($title1) {
                $content.='<div style="text-align: left; font-size: 15px">'.$title1.'</div>';
            }
            if ($text1) {
                $content.='<div style="text-align: left; font-size: 13px">'.$text1.'</div><br><br>';
            }

        }

        $htmlTable = <<<EOD
                <style>
             table{
              width: 100%;
              border-collapse: collapse;
              margin: 0 auto; /* 居中显示 */
            }
            .th{
                height: 40px;
            }
            .td{
                
                font-size: 14px;
                line-height: 30px;
                text-align: center;
                width: 40%;
                border:1px solid #999;
            }
             .td-left{
                
                font-size: 14px;
                line-height: 30px;
                width: 56%;
                margin-left: 10px;
                border: 1px solid black;
            }
            .td-center{
                text-align: center;
                line-height: 30px;
                width:4%;
                border: 1px solid black;
            }
            .text-page2{
                line-height: 24px;
                text-align: left;
                 font-size: 14px;
            }
            .bold {
                font-weight: bold;
            }

        </style>
        <div style="font-size: 18px; text-align: center; ">$title</div><br>
        $unit<br>
        $content<br>
        $statement<br>
        $bottom<br><br><br>
        EOD;


        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

        // 底部签名盖章
        $this->signature();

        // 输出 PDF
        $filename = time().Str::random(16).'.pdf';
        $directory = storage_path('app/public/gsp/report/');
        if (!is_dir($directory)) {
            mkdir($directory, 0777, true); // 递归创建目录
        }
        $file = $directory.$filename;
        $this->pdf->Output( $file, 'F');

        // 生成文件验签
        $token = DocService::generateToken('/gsp/report/'.$filename, 'gsp_form', 3600, $this->fileName);
        return $token;

        // 输出 PDF
        $filename = time().Str::random(16).'.pdf';
        $directory = storage_path('app/public/gsp/report/');
        if (!is_dir($directory)) {
            mkdir($directory, 0777, true); // 递归创建目录
        }
        $file = $directory.$filename;
        $this->pdf->Output('F', $file);

        // 生成文件验签

        $token = DocService::generateToken('/gsp/report/'.$filename, 'gsp_form', 3600, $this->fileName);
        return $token;
    }

    /**
     * 全体董事、监事、高级管理人员声明
     * @return void
     */
    public function generateLeadPdf()
    {


        $title = $this->title;

        // 表格单位
        $unit = $this->setTableUnit();

        // 表格底部声明
        $statement = $this->setTableFooter();

        // 底部静态内容
        $bottom = $this->setBottom();

        // 全体监视
        $column1 = $this->column[0];
        $content1 = $this->content[0];
        $rowCount = count($content1)+1;
        $lineheight = $rowCount * 40;
        $table1 = '
         <table  border="0" align="center" >';
        $table1 .= '<tr class="th">'
            .'<td class="td" style="width: 33%;line-height:'.$lineheight.'px;" rowspan="'.$rowCount.'">'.$column1[0].'</td>'
            .'<td class="td" style="width:33%">'.$column1[1].'</td><td class="td" style="width: 33%">'.$column1[2].'</td></tr>';
        foreach ($content1 as $row) {

            $table1 .= '<tr class="th">';
            $table1 .= '<td class="td" style="width: 33%;">'. $row[0].'</td>';
            $table1 .= '<td class="td" style="width: 33%">' . $row[1] . '</td>';
            $table1 .= '</tr>';
        }
        $table1 .= '</table>';



        // 全体监事
        $column2 = $this->column[1];
        $content2 = $this->content[1];
        $rowCount = count($content2)+1;
        $lineheight = $rowCount * 40;
        $table2 = '
         <table  border="0">';
        $table2 .= '<tr class="th"><td class="td" style="width: 33%;line-height:'.$lineheight.'px;" rowspan="'.$rowCount.'">'.$column2[0].'</td><td class="td" style="width:33%">'.$column2[1].'</td><td class="td" style="width: 33%">'.$column2[2].'</td></tr>';
        foreach ($content2 as $row) {

            $table2 .= '<tr class="th">';
            $table2 .= '<td class="td" style="width: 33%;">'. $row[0].'</td>';
            $table2 .= '<td class="td" style="width: 33%">' . $row[1] . '</td>';
            $table2 .= '</tr>';
        }
        $table2 .= '</table>';

        // 全体高级管理人员
        $column3 = $this->column[2];
        $content3 = $this->content[2];
        $rowCount = count($content3)+1;
        $lineheight = $rowCount * 40;
        $table3 = '
         <table  border="0">';
        $table3 .= '<tr class="th"><td class="td" style="width: 33%;line-height:'.$lineheight.'px;" rowspan="'.$rowCount.'">'.$column3[0].'</td><td class="td" style="width:33%">'.$column3[1].'</td><td class="td" style="width: 33%">'.$column3[2].'</td></tr>';
        foreach ($content3 as $row) {

            $table3 .= '<tr class="th">';
            $table3 .= '<td class="td" style="width: 33%;">'. $row[0].'</td>';
            $table3 .= '<td class="td" style="width: 33%">' . $row[1] . '</td>';
            $table3 .= '</tr>';
        }
        $table3 .= '</table>';

        $htmlTable = <<<EOD
                <style>
             tr { page-break-inside:avoid; page-break-after:auto }
             td { page-break-inside:avoid; page-break-after:auto }
             table{
              width: 100%;
              border-collapse: collapse;
              margin: 0 auto; /* 居中显示 */
              page-break-inside:auto
            }
            .th{
                height: 40px;
            }
            .td{
                font-size: 14px;
                line-height: 40px;
                text-align: center;
                border:1px solid #999;
            }
            .text-page2{
                line-height: 24px;
                text-align: left;
                font-size: 14px;
            }
        </style>
        <div style="font-size: 18px; text-align: center;">$title</div>
        <div>$unit</div>
        <div>$table1</div>
        <div>$table2</div>
        <div>$table3</div>
        $statement<br>
        $bottom<br><br><br>
        EOD;


        $this->pdf->writeHTML($htmlTable, true, false, true, false, '');

        // 底部签名盖章
        $this->signature();

        // 输出 PDF
        $filename = time().Str::random(16).'.pdf';
        $directory = storage_path('app/public/gsp/report/');
        if (!is_dir($directory)) {
            mkdir($directory, 0777, true); // 递归创建目录
        }
        $file = $directory.$filename;
        $this->pdf->Output( $file, 'F');

        // 生成文件验签
        $token = DocService::generateToken('/gsp/report/'.$filename, 'gsp_form', 3600, $this->fileName);
        return $token;
    }


    /**
     * 生成利润表
     * @return string
     */
    public function incomeStatement()
    {
        $title = $this->title;
        $accounting_name = $this->content[0]['accounting_name'];
        $accounting_name_en = $this->content[0]['accounting_name_en'];

        foreach ($this->content[0] as $key => $value) {
            $newKey = $key . '1';
            if (is_numeric($value)) {
                if ($value < 0) {
                    $value = abs($value);
                    $$newKey = '('.number_format($value, 2).')';
                } else {
                    if ($key != 'year') {
                        $$newKey = number_format($value, 2);
                    } else {
                        $$newKey = $value;
                    }
                }
            }


        }

        foreach ($this->content[1] as $key => $value) {
            $newKey = $key . '2';
            if (is_numeric($value)) {
                if ($value < 0) {
                    $value = abs($value);
                    $$newKey = '('.number_format($value, 2).')';
                } else {
                    if ($key != 'year') {
                        $$newKey = number_format($value, 2);
                    } else {
                        $$newKey = $value;
                    }
                }
            }

        }

        // 合并单元格
        $html = <<<EOD
        <div style="text-align:center;font-size:18px">$title</div><br>
        <div style="text-align:left;font-size:16px">截至报告基准日，以下数据摘自目标公司委托 $accounting_name 出具的审计报告。</div>
         <div style="text-align:left;font-size:16px">As of the Report Reference Date, the following data have been excerpted from the Audit Report issued by $accounting_name_en entrusted by the Company.</div><br>
        <table border="1" cellpadding="2">
           <tr>
                <td align="center"><strong>项目</strong></td>
                <td align="center">项目</td>
                <td align="center">$year1</td>
                <td align="center">$year2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td>Revenue</td>
                <td>营业收入</td>
                <td align="right">$revenue1</td>
                <td align="right">$revenue2</td>
           </tr>
           <tr>
                <td>Cost of Goods Sold (COGS) / Cost of Sales (COS)</td>
                <td>营业成本</td>
                <td align="right" >$cogs1</td>
                <td align="right" >$cogs2</td>
           </tr>
           <tr>
                <td>GROSS PROFIT</td>
                <td >毛利</td>
                <td align="right" >$gross_profit1</td>
                <td align="right" >$gross_profit2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td>Research and Development Expense</td>
                <td style="font-family:SimHei;font-weight:bold;">研发费用</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$research_and_development_expense1</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$research_and_development_expense2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td rowspan="3">Selling, General and Administrative Expense</td>
                <td style="font-family:SimHei;font-weight:bold;">销售及管理费用</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>销售费用</td>
                <td align="right">$general_expenses1</td>
                <td align="right">$general_expenses2</td>
            </tr>
            <tr>
                <td>管理费用</td>
                <td align="right">$administrative_expenses1</td>
                <td align="right">$administrative_expenses2</td>
            </tr>
             <tr>
                <td></td>
                <td style="font-family:SimHei;font-weight:bold;">销售及管理费用</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$general_and_administrative_expense1</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$general_and_administrative_expense2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td rowspan="7">Other Operating Expenses 营业外支出</td>
                <td style="font-family:SimHei;font-weight:bold;">营业外支出</td>
                <td></td>
            </tr>
            <tr>
                <td>净敞口套期损失(损失以“-”号填列)</td>
                <td align="right">$net_exposure_hedging_loss1</td>
                <td align="right">$net_exposure_hedging_loss2</td>
            </tr>
             <tr>
                <td>公允价值变动损失(损失以“-”号填列)</td>
                <td align="right">$fair_value_change_loss1</td>
                <td align="right">$fair_value_change_loss2</td>
            </tr>
             <tr>
                <td>信用减值损失(损失以“-”号填列)</td>
                <td align="right">$credit_impairment_loss1</td>
                <td align="right">$credit_impairment_loss2</td>
            </tr>
             <tr>
                <td>资产减值损失(损失以“-”号填列)</td>
                <td align="right">$asset_impairment_loss1</td>
                <td align="right">$asset_impairment_loss2</td>
            </tr>
            <tr>
                <td>资产处置损失(损失以“-”号填列)</td>
                <td align="right">$asset_disposal_loss1</td>
                <td align="right">$asset_disposal_loss2</td>
            </tr>
             <tr>
                <td>减：其它营业外支出</td>
                <td align="right">$other_non_operating_expenses1</td>
                <td align="right">$other_non_operating_expenses2</td>
            </tr>
            <tr>
                <td></td>
                <td style="font-family:SimHei;font-weight:bold;">营业外支出</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$other_operating_expenses1</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$other_operating_expenses2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td rowspan="7">Other Operating Income 营业外收入</td>
                <td style="font-family:SimHei;font-weight:bold;">营业外收入</td>
                <td></td>
            </tr>
            <tr>
                <td>以摊余成本计量的金融资产终止确认收益</td>
                <td align="right">$financial_asset_income1</td>
                <td align="right">$financial_asset_income2</td>
            </tr>
             <tr>
                <td>净敞口套期收益</td>
                <td align="right">$net_exposure_hedging_gain1</td>
                <td align="right">$net_exposure_hedging_gain2</td>
            </tr>
             <tr>
                <td>公允价值变动收益</td>
                <td align="right">$fair_value_change_gain1</td>
                <td align="right">$fair_value_change_gain2</td>
            </tr>
             <tr>
                <td>资产处置收益</td>
                <td align="right">$asset_disposal_gain1</td>
                <td align="right">$asset_disposal_gain2</td>
            </tr>
             <tr>
                <td>加：其它营业外收入</td>
                <td align="right">$other_non_operating_income1</td>
                <td align="right">$other_non_operating_income2</td>
            </tr>
             <tr>
                <td style="font-family:SimHei;font-weight:bold;">营业外收入</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$other_operating_income1</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$other_operating_income2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>OPRATING PROFIT</td>
                <td style="font-family:SimHei;font-weight:bold;">营业利润</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$oprating_profit1</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$oprating_profit2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td rowspan="3">Finance Income</td>
                <td style="font-family:SimHei;font-weight:bold;">财务收入</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>利息收入</td>
                <td align="right">$interest_income1</td>
                <td align="right">$interest_income2</td>
            </tr>
              <tr>
                <td>投资收益</td>
                <td align="right">$investment_income1</td>
                <td align="right">$investment_income2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$finance_income1</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$finance_income2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td rowspan="3">Finance Expenses</td>
                <td style="font-family:SimHei;font-weight:bold;">财务费用</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>利息支出</td>
                <td align="right">$interest_expense1</td>
                <td align="right">$interest_expense2</td>
            </tr>
              <tr>
                <td>投资损失(损失以“-”号填列)</td>
                <td align="right">$investment_loss1</td>
                <td align="right">$investment_loss2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$finance_expenses1</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$finance_expenses2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>Share of Net Profit/(Loss) of Joint Ventures&Associates</td>
                <td>对联营企业和合营企业的投资收益</td>
                <td align="right">$share_of_net_profit1</td>
                <td align="right" >$share_of_net_profit2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>PROFIT BEFORE TAX</td>
                <td style="font-family:SimHei;font-weight:bold;">税前利润</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$profit_before_tax1</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$profit_before_tax2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>Taxation</td>
                <td >减：所得税费用</td>
                <td align="right" >$taxation1</td>
                <td align="right">$taxation2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>PROFIT FOR THE PERIOD</td>
                <td style="font-family:SimHei;font-weight:bold;">本期利润</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$profit_for_the_period1</td>
                <td align="right"style="font-family:SimHei;font-weight:bold;">$profit_for_the_period2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>Other Comprehensivc Income</td>
                <td style="font-family:SimHei;font-weight:bold;">其他综合收益</td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>Items that will not be Reclassified to Profit and Loss:</td>
                <td>不能重分类进损益的其他综合收益</td>
                <td align="right">$items_that_will_not_be_reclassified_to_profit_and_loss1</td>
                <td align="right">$items_that_will_not_be_reclassified_to_profit_and_loss2</td>
            </tr>
             <tr>
                <td>Other Comprehensive Income</td>
                <td >其他综合收益</td>
                <td align="right">$other_comprehensive_income1</td>
                <td align="right">$other_comprehensive_income2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$other_comprehensive_income_all1</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$other_comprehensive_income_all2</td>
            </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
             <tr>
                <td>Items that may be Reclassified Subsequently to Profit and Loss:</td>
                <td style="font-family:SimHei;font-weight:bold;">随后可重新分类至损益的项目：</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>Gains/(Losses) on Cash Flow Hedges</td>
                <td>现金流量套期保值收益/（损失）</td>
                <td align="right">$gains_losses_on_cash_flow_hedges1</td>
                <td align="right">$gains_losses_on_cash_flow_hedges2</td>
            </tr>
            <tr>
                <td>Currency Retranslation Gains/(Losses)</td>
                <td>货币重估收益 /（损失）</td>
                <td align="right">$currency_retranslation_gains1</td>
                <td align="right">$currency_retranslation_gains2</td>
            </tr>
            <tr>
                <td>Other Comprehensive (Expense)/Income for the Period, Net of Tax</td>
                <td>本期其他综合（费用）/收入，税后净额</td>
                <td align="right">$other_comprehensive_expense1</td>
                <td align="right">$other_comprehensive_expense2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>Total Comprehensive Income for the Period</td>
                <td style="font-family:SimHei;font-weight:bold;">综合收益总额</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$total_comprehensive_income_for_the_period1</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$total_comprehensive_income_for_the_period2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
        </table>
        EOD;

        $this->pdf->writeHTML($html, true, false, true, false, '');

        // 提交人
        $this->signature();

        // 输出 PDF
        $filename = time().Str::random(16).'.pdf';
        $directory = storage_path('app/public/gsp/report/');
        if (!is_dir($directory)) {
            mkdir($directory, 0777, true); // 递归创建目录
        }
        $file = $directory.$filename;
        $this->pdf->Output( $file, 'F');

        // 生成文件验签
        $token = DocService::generateToken('/gsp/report/'.$filename, 'gsp_form', 3600, $this->fileName);
        return $token;

    }

    /**
     * 现金流量表
     * @return void
     */
    public function cashFlow()
    {
        $title = $this->title;

        foreach ($this->content[0] as $key => $value) {
            $newKey = $key . '1';
            if ($value < 0) {
                $value = abs($value);
                $$newKey = '('.number_format($value, 2).')';
            } else {
                if ($key != 'year') {
                    $$newKey = number_format($value, 2);
                } else {
                    $$newKey = $value;
                }
            }

        }

        foreach ($this->content[1] as $key => $value) {
            $newKey = $key . '2';
            if ($value < 0) {
                $value = abs($value);
                $$newKey = '('.number_format($value, 2).')';
            } else {
                if ($key != 'year') {
                    $$newKey = number_format($value, 2);
                } else {
                    $$newKey = $value;
                }
            }
        }


        // 合并单元格
        $html = <<<EOD
        <div style="text-align:center; font-size:18px">$title</div>
        <div style="text-align:center;font-size:18px">Statement of Cash Flows </div><br>
        <table border="1" cellpadding="2">
           <tr>
                <td align="center"><strong>项目</strong></td>
                <td align="center">项目</td>
                <td align="center">$year1</td>
                <td align="center">$year2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td align="center"></td>
                <td align="center"></td>
           </tr>
            <tr>
                <td>CASH FLOW FROM OPERATING ACTIVITIES</td>
                <td  style="font-family:SimHei;font-weight:bold;">经营活动产生的现金流量</td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td>Cash Received from the Sale of Goods and the Provision of Services</td>
                <td >销售商品、提供劳务收到的现金</td>
                <td align="right">$cash_received_from_the_sale1</td>
                <td align="right">$cash_received_from_the_sale2</td>
           </tr>
            <tr>
                <td>Tax Refunds Received</td>
                <td>收到的税费返还</td>
                <td align="right">$tax_refunds_received1</td>
                <td align="right">$tax_refunds_received2</td>
           </tr>
            <tr>
                <td>Other Cash Received in connection with Operating Activities</td>
                <td>收到其他与经营活动有关的现金</td>
                <td align="right">$other_cash_received_in_connection_with_operating_activities1</td>
                <td align="right">$other_cash_received_in_connection_with_operating_activities2</td>
           </tr>
           <tr>
                <td>Subtotal Cash Inflows from Operating Activities</td>
                <td  style="font-family:SimHei;font-weight:bold;">经营活动现金流入小计</td>
                <td align="right"  style="font-family:SimHei;font-weight:bold;">$subtotal_cash_inflows_from_operating_activities1</td>
                <td align="right"  style="font-family:SimHei;font-weight:bold;">$subtotal_cash_inflows_from_operating_activities2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>Cash for the Purchase of Goods and Payment for Services</td>
                <td>购买商品、接受劳务支付的现金</td>
                <td align="right">$cash_for_the_purchase1</td>
                <td align="right">$cash_for_the_purchase2</td>
           </tr>
           <tr>
                <td>Cash Paid to and on Behalf of Employees</td>
                <td>支付给职工以及为职工支付的现金</td>
                <td align="right">$cash_paid_to_and_on_behalf1</td>
                <td align="right">$cash_paid_to_and_on_behalf2</td>
           </tr>
           <tr>
                <td>Taxes and Fees Paid</td>
                <td>支付的各项税费</td>
                <td align="right">$taxes_and_fees_paid1</td>
                <td align="right">$taxes_and_fees_paid2</td>
           </tr>
           <tr>
                <td>Payment of other Cash Related to Operating Activities</td>
                <td>支付其他与经营活动有关的现金</td>
                <td align="right">$payment_of_other_cash_related_to_operating_activities1</td>
                <td align="right">$payment_of_other_cash_related_to_operating_activities2</td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td><b>Subtotal Cash Otflows from Operating Activities</b></td>
                <td  style="font-family:SimHei;font-weight:bold;"><b>经营活动现金流出小计</b></td>
                <td align="right"  style="font-family:SimHei;font-weight:bold;">$subtotal_cash_otflows_from_operating_activities1</td>
                <td align="right"  style="font-family:SimHei;font-weight:bold;">$subtotal_cash_otflows_from_operating_activities2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td><b>CASH FLOW FROM INVESTING ACTIVITIES</b></td>
                <td  style="font-family:SimHei;font-weight:bold;"><b>投资活动产生的现金流量</b></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td><b>Proceeds from Disposal of Fixed Assets:</b></td>
                <td  style="font-family:SimHei;font-weight:bold;"><b>处置固定资产的收益：</b></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>Proceeds from Sale of Plant, property and equipment and proceeds from long term investment</td>
                <td>处置固定资产、无形资产和其他长期资产收回的现金净额</td>
                <td align="right">$proceeds_from_sale_of_plant1</td>
                <td align="right">$proceeds_from_sale_of_plant2</td>
           </tr>
            <tr>
                <td>Proceeds from Sale of Investments/Financial assets</td>
                <td>收回投资收到的现金</td>
                <td align="right">$proceeds_from_sale_of_investments1</td>
                <td align="right">$proceeds_from_sale_of_investments2</td>
           </tr>
           <tr>
                <td>Proceeds from Sale of Goodwill, Patent Rights, Trade Marks, etc.</td>
                <td>出售商誉、专利权、商标等的收益。</td>
                <td align="right">$proceeds_from_sale_of_goodwill1</td>
                <td align="right">$proceeds_from_sale_of_goodwill2</td>
           </tr>
           <tr>
                <td>Net cash received from sales of subsidiaries and other business units</td>
                <td>处置子公司及其他营业单位收到的现金净额</td>
                <td align="right">$net_cash_received_from_sales_of_subsidiaries1</td>
                <td align="right">$net_cash_received_from_sales_of_subsidiaries2</td>
           </tr>
           <tr>
                <td>Other Proceeds from Investments</td>
                <td>收到其他与投资活动有关的现金</td>
                <td align="right">$other_proceeds_from_investments1</td>
                <td align="right">$other_proceeds_from_investments2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td>Add: Non-Operating Incomes from Investments:</td>
                <td>加：取得投资收益收到的现金</td>
                <td align="right">$operating_incomes_from_investments1</td>
                <td align="right">$operating_incomes_from_investments2</td>
           </tr>
            <tr>
                <td>Dividends Received</td>
                <td>收到的股息</td>
                <td align="right">$dividends_received1</td>
                <td align="right">$dividends_received2</td>
           </tr>
           <tr>
                <td>Interest Received</td>
                <td>收到的利息</td>
                <td align="right">$interest_received1</td>
                <td align="right">$interest_received2</td>
           </tr>
           <tr>
                <td>Rent on Property Received</td>
                <td>收到的物业租金</td>
                <td align="right">$rent_on_property_received1</td>
                <td align="right">$rent_on_property_received2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>Less: Purchase of Non-Current Assets:</td>
                <td  style="font-family:SimHei;font-weight:bold;">非流动资产采购：</td>
                <td align="right"></td>
                <td align="right"></td>
           </tr>
            <tr>
                <td>Purchase of Plant, Property and Equipment</td>
                <td>购建固定资产, 和其他长期资产支付的现金</td>
                <td align="right">$purchase_of_plant1</td>
                <td align="right">$purchase_of_plant2</td>
           </tr>
           <tr>
                <td>Purchase of Investments/Financial Assets </td>
                <td>购买投资/金融资产</td>
                <td align="right">$purchase_of_investments1</td>
                <td align="right">$purchase_of_investments2</td>
           </tr>
           <tr>
                <td>Purchase of Joint Ventures and Associates</td>
                <td>购买合资企业和联营企业</td>
                <td align="right">$purchase_of_joint_ventures_and_associates1</td>
                <td align="right">$purchase_of_joint_ventures_and_associates2</td>
           </tr>
           <tr>
                <td>Purchase of intangible assets - Goodwill, Patent Rights, Trade Marks, etc. </td>
                <td>无形资产的购买—商誉、专利权、商标等。</td>
                <td align="right">$purchase_of_intangible_assets1</td>
                <td align="right">$purchase_of_intangible_assets2</td>
           </tr>
            <tr>
                <td>Other Payments for Investing Activities</td>
                <td>支付其他与投资活动有关的现金</td>
                <td align="right">$other_payments_for_investing_activities1</td>
                <td align="right">$other_payments_for_investing_activities2</td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>Net Cash (used in)/from Investing Activities</td>
                <td style="font-family:SimHei;font-weight:bold;">净现金流入/(流出）（用于/来自投资活动）</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$net_cash_from_investing_activities1</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$net_cash_from_investing_activities2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td><b>CASH FLOW FROM FINANCING ACTIVITIES</b></td>
                <td  style="font-family:SimHei;font-weight:bold;"><b>融资活动产生的现金流量</b></td>
                <td align="right"></td>
                <td align="right"></td>
           </tr>
           <tr>
                <td><b>CASH FLOW FROM FINANCING ACTIVITIES</b></td>
                <td style="font-family:SimHei;font-weight:bold;"><b>发行股本和借款的现金流。</b></td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$proceeds_from_issue_of_share_capital_and_borrowings1</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$proceeds_from_issue_of_share_capital_and_borrowings2</td>
           </tr>
            <tr>
                <td>Proceeds from Issue of Ordinary Shares Capital</td>
                <td >发行普通股所得款项资本</td>
                <td align="right">$proceeds_from_issue_of_ordinary_shares_capital1</td>
                <td align="right">$proceeds_from_issue_of_ordinary_shares_capital2</td>
           </tr>
            <tr>
                <td>Proceeds from Issue of Preference Shares Capital</td>
                <td>发行优先股所得款项资本</td>
                <td align="right">$proceeds_from_issue_of_preference_shares_capital1</td>
                <td align="right">$proceeds_from_issue_of_preference_shares_capital2</td>
           </tr>
           <tr>
                <td>Proceeds from Loans and Borrowings etc. </td>
                <td>贷款和借款等所得款项</td>
                <td align="right">$proceeds_from_loans_and_borrowings_etc1</td>
                <td align="right">$proceeds_from_loans_and_borrowings_etc2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td>(-) Buy-back of Equity Shares</td>
                <td>（-） 回购普通股</td>
                <td align="right">$buy_back_of_equity_shares1</td>
                <td align="right">$buy_back_of_equity_shares2</td>
           </tr>
            <tr>
                <td>(-) Redemption of Preference Shares</td>
                <td>（-） 优先股的赎回</td>
                <td align="right">$redemption_of_preference_shares1</td>
                <td align="right">$redemption_of_preference_shares2</td>
           </tr>
           <tr>
                <td>(-) Repayment of Loans and Borrowings</td>
                <td>（-） 偿还贷款和借款</td>
                <td align="right">$repayment_of_loans_and_borrowings1</td>
                <td align="right">$repayment_of_loans_and_borrowings2</td>
           </tr>
            <tr>
                <td>(-) Redemption of Debentures</td>
                <td>（-） 债券赎回</td>
                <td align="right">$redemption_of_debentures1</td>
                <td align="right">$redemption_of_debentures2</td>
           </tr>
           <tr>
                <td>(-) Dividends Paid on Equity Shares</td>
                <td>（-） 支付普通股股息</td>
                <td align="right">$dividends_paid_on_equity_shares1</td>
                <td align="right">$dividends_paid_on_equity_shares2</td>
           </tr>
           <tr>
                <td>(-) Dividend on Preference Shares</td>
                <td>（-） 优先股股息</td>
                <td align="right">$dividend_on_preference_shares1</td>
                <td align="right">$dividend_on_preference_shares2</td>
           </tr>
           <tr>
                <td>(-) Repayment of obligations under leases</td>
                <td>（-） 租赁债务的偿还</td>
                <td align="right">$repayment_of_obligations_under_leases1</td>
                <td align="right">$repayment_of_obligations_under_leases2</td>
           </tr>
            <tr>
                <td>Other payments for financing activities</td>
                <td>其他融资活动付款</td>
                <td align="right">$other_payments_for_financing_activities1</td>
                <td align="right">$other_payments_for_financing_activities2</td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td><b>Net Cash (used in)/From Financing Activities</b></td>
                <td  style="font-family:SimHei;font-weight:bold;"><b>净现金（用于）/来自融资活动</b></td>
                <td align="right"  style="font-family:SimHei;font-weight:bold;">$from_financing_activities1</td>
                <td align="right"  style="font-family:SimHei;font-weight:bold;">$from_financing_activities2</td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td><b>Net Increase/(Decrease) in Cash and Cash Equivalents</b></td>
                <td>现金及现金等价物净增加额</td>
                <td align="right">$net_increase1</td>
                <td align="right">$net_increase2</td>
           </tr>
           <tr>
                <td><b>Cash and Cash Equivalents at the beginning of the Period</b></td>
                <td>加：期初现金及现金等价物余额</td>
                <td align="right">$cash_equivalents_at_the_beginning_of_the_period1</td>
                <td align="right">$cash_equivalents_at_the_beginning_of_the_period2</td>
           </tr>
            <tr>
                <td><b>Effect of Foreign Exchange Rate Changes</b></td>
                <td>汇率变动对现金及现金等价物的影响</td>
                <td align="right">$effect_of_foreign_exchange_rate_changes1</td>
                <td align="right">$effect_of_foreign_exchange_rate_changes2</td>
           </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
            <tr>
                <td><b>CASH AND CASH EQUIVALENTS AT THE END OF THE YEAR</b></td>
                <td>期末现金及现金等价物余额</td>
                <td align="right">$cash_equivalents_at_the_end_of_the_year1</td>
                <td align="right">$cash_equivalents_at_the_end_of_the_year2</td>
           </tr>
        </table>
        EOD;

        $this->pdf->writeHTML($html, true, false, true, false, '');

        // 签名
        $this->signature();

        // 输出 PDF
        $filename = time().Str::random(16).'.pdf';
        $directory = storage_path('app/public/gsp/report/');
        if (!is_dir($directory)) {
            mkdir($directory, 0777, true); // 递归创建目录
        }
        $file = $directory.$filename;
        $this->pdf->Output( $file, 'F');

        // 生成文件验签
        $token = DocService::generateToken('/gsp/report/'.$filename, 'gsp_form', 3600, $this->fileName);
        return $token;

    }

    /**
     * 资产负债表
     * @return void
     */
    public function balanceSheet()
    {
        $title = $this->title;

        foreach ($this->content[0] as $key => $value) {
            $newKey = $key . '1';
            if ($value < 0) {
                $value = abs($value);
                $$newKey = '('.number_format($value, 2).')';
            } else {
                if ($key != 'year') {
                    $$newKey = number_format($value, 2);
                } else {
                    $$newKey = $value;
                }
            }

        }

        foreach ($this->content[1] as $key => $value) {
            $newKey = $key . '2';
            if ($value < 0) {
                $value = abs($value);
                $$newKey = '('.number_format($value, 2).')';
            } else {
                if ($key != 'year') {
                    $$newKey = number_format($value, 2);
                } else {
                    $$newKey = $value;
                }
            }
        }


        // 合并单元格
        $html = <<<EOD
        <div style="text-align:center;font-size:18px">$title</div>
        <div style="text-align:center;font-size:18px">Statement of Financial Position</div><br>
        <table border="1" cellpadding="2">
           <tr>
                <td align="center"><strong>项目</strong></td>
                <td align="center">项目</td>
                <td align="center">$year1</td>
                <td align="center">$year2</td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td align="center"></td>
                <td align="center"></td>
           </tr>
           <tr>
                <td>ASSETS</td>
                <td  style="font-family:SimHei;font-weight:bold;">资产合计</td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td>NON-CURRENT ASSETS</td>
                <td  style="font-family:SimHei;font-weight:bold;">非流动资产：</td>
                <td></td>
                <td></td>
           </tr>
           <tr>
                <td rowspan="2">Property, Plant and Equipment</td>
                <td>固定资产</td>
                <td align="right">$fixed_assets1</td>
                <td align="right">$fixed_assets2</td>
           </tr>
            <tr>
                <td>在建工程</td>
                <td align="right">$construction_in_progress1</td>
                <td align="right">$construction_in_progress2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$property_plant_and_equipment1</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$property_plant_and_equipment2</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Goodwill</td>
                <td>商誉</td>
                <td  align="right">$goodwill1</td>
                <td  align="right">$goodwill2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Intangible Assets</td>
                <td>无形资产</td>
                <td  align="right">$intangible_assets1</td>
                <td  align="right">$intangible_assets2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Right-of-Use Assets</td>
                <td>使用权资产</td>
                <td  align="right">$right_of_use_assets1</td>
                <td  align="right">$right_of_use_assets2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Investments Properties</td>
                <td>投资性房地产</td>
                <td  align="right">$investments_properties1</td>
                <td  align="right">$investments_properties2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td rowspan="7">Other Investments</td>
                <td>债权投资</td>
                <td  align="right">$debt_investments1</td>
                <td  align="right">$debt_investments2</td>
             </tr>
              <tr>
                <td>可供出售金融资产</td>
                <td  align="right">$available_for_sale_financial_assets1</td>
                <td  align="right">$available_for_sale_financial_assets2</td>
             </tr>
             <tr>
                <td>其他债权投资</td>
                <td  align="right">$other_debt_investments1</td>
                <td  align="right">$other_debt_investments2</td>
             </tr>
              <tr>
                <td>持有至到期投资</td>
                <td  align="right">$held_to_maturity_investments1</td>
                <td  align="right">$held_to_maturity_investments2</td>
             </tr>
             <tr>
                <td>长期股权投资</td>
                <td  align="right">$long_term_equity_investments1</td>
                <td  align="right">$long_term_equity_investments2</td>
             </tr>
              <tr>
                <td>其他权益工具投资</td>
                <td  align="right">$other_equity_instrument_investments1</td>
                <td  align="right">$other_equity_instrument_investments2</td>
             </tr>
              <tr>
                <td>其他非流动金融资产</td>
                <td  align="right">$other_non_current_financial_assets1</td>
                <td  align="right">$other_non_current_financial_assets2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td  align="right"  style="font-family:SimHei;font-weight:bold;">$other_investments_all1</td>
                <td  align="right"  style="font-family:SimHei;font-weight:bold;">$other_investments_all2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Other Receivables</td>
                <td>长期应收款</td>
                <td align="right">$long_term_receivables1</td>
                <td align="right">$long_term_receivables2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Deferred Tax Assets</td>
                <td>递延所得税资产</td>
                <td align="right">$deferred_tax_assets1</td>
                <td align="right">$deferred_tax_assets2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td rowspan="5">Other Non-Current Assets</td>
                <td>生产性生物资产</td>
                <td align="right">$productive_biological_assets1</td>
                <td align="right">$productive_biological_assets2</td>
             </tr>
             <tr>
                <td>油气资产</td>
                <td align="right">$oil_and_gas_assets1</td>
                <td align="right">$oil_and_gas_assets2</td>
             </tr>
             <tr>
                <td>开发支出</td>
                <td align="right">$development_expenditure1</td>
                <td align="right">$development_expenditure2</td>
             </tr>
             <tr>
                <td>长期待摊费用</td>
                <td align="right">$long_term_prepaid_expenses1</td>
                <td align="right">$long_term_prepaid_expenses2</td>
             </tr>
              <tr>
                <td>其他非流动资产</td>
                <td align="right">$other_non_current_assets1</td>
                <td align="right">$other_non_current_assets2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$other_non_current_assets_all1</td>
                <td align="right" style="font-family:SimHei;font-weight:bold;">$other_non_current_assets_all2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>TOTAL NON-CURRENT ASSETS</td>
                <td style="font-family:SimHei;font-weight:bold;">非流动资产 （共）</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_non_current_assets1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_non_current_assets2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>CURRENT ASSETS</td>
                <td style="font-family:SimHei;font-weight:bold;">流动资产</td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Inventories</td>
                <td>存货</td>
                <td  align="right">$inventories1</td>
                <td  align="right">$inventories2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td rowspan="4">Trade and Other Receivables</td>
                <td>应收票据</td>
                <td  align="right">$notes_receivable1</td>
                <td  align="right">$notes_receivable2</td>
             </tr>
              <tr>
                <td>应收账款</td>
                <td  align="right">$accounts_receivable1</td>
                <td  align="right">$accounts_receivable2</td>
             </tr>
             <tr>
                <td>预收款项</td>
                <td  align="right">$advance_receipts1</td>
                <td  align="right">$advance_receipts2</td>
             </tr>
              <tr>
                <td>其他应收款</td>
                <td  align="right">$other_receivables1</td>
                <td  align="right">$other_receivables2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$trade_and_other_receivables1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$trade_and_other_receivables2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Other Investments</td>
                <td>交易性金融资产</td>
                <td  align="right">$trading_financial_assets1</td>
                <td  align="right">$trading_financial_assets2</td>
             </tr>
             <tr>
                <td></td>
                <td>以公允价值计量且其变动计 入当期损益的金融资产</td>
                <td  align="right">$financial_assets_measured1</td>
                <td  align="right">$financial_assets_measured2</td>
             </tr>
              <tr>
                <td></td>
                <td>其他金融资产</td>
                <td  align="right">$derivative_financial_assets1</td>
                <td  align="right">$derivative_financial_assets2</td>
             </tr>
              <tr>
                <td></td>
                <td style="font-family:SimHei;font-weight:bold;">其它投资 （共）</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$other_investments1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$other_investments2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td rowspan="5">Other Current Assets</td>
                <td>合同资产</td>
                <td  align="right">$contract_assets1</td>
                <td  align="right">$contract_assets2</td>
             </tr>
              <tr>
                <td>持有待售资产</td>
                <td  align="right">$held_for_sale_assets1</td>
                <td  align="right">$held_for_sale_assets2</td>
             </tr>
             <tr>
                <td>一年内到期的非流动资产</td>
                <td  align="right">$non_current_assets_due_within_one_year1</td>
                <td  align="right">$non_current_assets_due_within_one_year2</td>
             </tr>
             <tr>
                <td>其他流动资产</td>
                <td  align="right">$other_current_assets1</td>
                <td  align="right">$other_current_assets2</td>
             </tr>
              <tr>
                <td>应收款项融资</td>
                <td  align="right">$receivables_financing1</td>
                <td  align="right">$receivables_financing2</td>
             </tr>
             <tr>
                <td></td>
                <td style="font-family:SimHei;font-weight:bold;">其它流动资产 （共）</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$other_current_assets_all1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$other_current_assets_all2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Income Tax Receivables</td>
                <td>应回收所得税费</td>
                <td  align="right">$income_tax_receivables1</td>
                <td  align="right">$income_tax_receivables2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Cash and Cash Equivalents</td>
                <td>现金加同等价值</td>
                <td  align="right">$cash_and_cash_equivalents1</td>
                <td  align="right">$cash_and_cash_equivalents2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>TOTAL CURRENT ASSETS</td>
                <td style="font-family:SimHei;font-weight:bold;">流动资产 (共）</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_current_assets1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_current_assets2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>TOTAL ASSETS</td>
                <td style="font-family:SimHei;font-weight:bold;">资产（共）</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_assets1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_assets2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>LIABILITIES</td>
                <td style="font-family:SimHei;font-weight:bold;">负债</td>
                <td  align="right"></td>
                <td  align="right"></td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>CURRENT LIABILITIES</td>
                <td style="font-family:SimHei;font-weight:bold;">流动负债</td>
                <td  align="right"></td>
                <td  align="right"></td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td rowspan="3">Interest-bearing loans and borrowings</td>
                <td>短期借款</td>
                <td  align="right">$short_term_borrowings1</td>
                <td  align="right">$short_term_borrowings2</td>
             </tr>
             <tr>
                <td>交易性金融负债</td>
                <td  align="right">$trading_financial_liabilities1</td>
                <td  align="right">$trading_financial_liabilities2</td>
             </tr>
             <tr>
                <td>其他金融负债</td>
                <td  align="right">$derivative_financial_liabilities1</td>
                <td  align="right">$derivative_financial_liabilities2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$interest_bearing_loans_and_borrowings_short1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$interest_bearing_loans_and_borrowings_short2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Lease Liabilities</td>
                <td>租赁负债</td>
                <td  align="right">$lease_liabilities_short1</td>
                <td  align="right">$lease_liabilities_short2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td rowspan="6">Trade and other payables</td>
                <td>应付账款</td>
                <td  align="right">$accounts_payable1</td>
                <td  align="right">$accounts_payable2</td>
             </tr>
             <tr>
                <td>应付票据</td>
                <td  align="right">$notes_payable1</td>
                <td  align="right">$notes_payable2</td>
             </tr>
            <tr>
                <td>预付款项</td>
                <td  align="right">$prepayments1</td>
                <td  align="right">$prepayments2</td>
             </tr>
             <tr>
                <td>合同负债</td>
                <td  align="right">$contract_liabilities1</td>
                <td  align="right">$contract_liabilities2</td>
             </tr>
              <tr>
                <td>应付职工薪酬</td>
                <td  align="right">$employee_benefits_payable1</td>
                <td  align="right">$employee_benefits_payable2</td>
             </tr>
             <tr>
                <td>其他应付款</td>
                <td  align="right">$other_payables1</td>
                <td  align="right">$other_payables2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$trade_and_other_payables1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$trade_and_other_payables2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Provisions</td>
                <td>会计拨备或应计</td>
                <td  align="right">$provisions_short1</td>
                <td  align="right">$provisions_short2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>Income Tax Payables</td>
                <td>应付所得税/应交税费</td>
                <td  align="right">$income_tax_payables_short1</td>
                <td  align="right">$income_tax_payables_short2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td rowspan="3">Other Current Liabilities</td>
                <td>持有待售负债</td>
                <td  align="right">$held_for_sale_liabilities1</td>
                <td  align="right">$held_for_sale_liabilities2</td>
             </tr>
             <tr>
                <td>一年内到期的非流动负债</td>
                <td  align="right">$non_current_liabilities_due_within_one_year1</td>
                <td  align="right">$non_current_liabilities_due_within_one_year2</td>
             </tr>
              <tr>
                <td>其他流动负债</td>
                <td  align="right">$other_current_liabilities1</td>
                <td  align="right">$other_current_liabilities2</td>
             </tr>
             <tr>
                <td></td>
                <td style="font-family:SimHei;font-weight:bold;">其他流动负债 (共）</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$other_current_liabilities_all1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$other_current_liabilities_all2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
               <tr>
                <td>TOTAL CURRENT LIABILITIES </td>
                <td style="font-family:SimHei;font-weight:bold;">流动负债 （共）</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_current_liabilities1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_current_liabilities2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>NON CURRENT LIABILITIES </td>
                <td style="font-family:SimHei;font-weight:bold;">非流动负债</td>
                <td  align="right"></td>
                <td  align="right"></td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td rowspan="2">Interest-bearing loans and borrowings</td>
                <td>长期借款</td>
                <td  align="right">$long_term_borrowings1</td>
                <td  align="right">$long_term_borrowings2</td>
             </tr>
              <tr>
                <td>应付债券</td>
                <td  align="right">$bonds_payable1</td>
                <td  align="right">$bonds_payable2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$interest_bearing_loans_and_borrowings_long1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$interest_bearing_loans_and_borrowings_long2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Lease Liabilities</td>
                <td>租赁负债</td>
                <td  align="right">$lease_liabilities_long1</td>
                <td  align="right">$lease_liabilities_long2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Deferred Tax Liabilities</td>
                <td>递延所得税负债</td>
                <td  align="right">$deferred_tax_liabilities1</td>
                <td  align="right">$deferred_tax_liabilities2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Provisions</td>
                <td>会计拨备或应计</td>
                <td  align="right">$provisions_long1</td>
                <td  align="right">$provisions_long2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Income Tax Payables</td>
                <td>应付所得税/应交税费</td>
                <td  align="right">$income_tax_payables_long1</td>
                <td  align="right">$income_tax_payables_long2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td rowspan="4">Other Paybales</td>
                <td>长期应付款</td>
                <td  align="right">$long_term_payables1</td>
                <td  align="right">$long_term_payables2</td>
             </tr>
              <tr>
                <td>长期应付职工薪酬</td>
                <td  align="right">$long_term_employee_benefits_payable1</td>
                <td  align="right">$long_term_employee_benefits_payable2</td>
             </tr>
             <tr>
                <td>预计负债</td>
                <td  align="right">$provision_liabilities1</td>
                <td  align="right">$provision_liabilities2</td>
             </tr>
              <tr>
                <td style="font-family:SimHei;font-weight:bold;">其它应付账款 (共）</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$other_paybales1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$other_paybales2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td rowspan="4">Other Non-Current Liabilities</td>
                <td>其中：优先股 (债务部分）</td>
                <td  align="right">$preferred_stock1</td>
                <td  align="right">$preferred_stock2</td>
             </tr>
             <tr>
                <td>永续债</td>
                <td  align="right">$perpetual_bonds1</td>
                <td  align="right">$perpetual_bonds2</td>
             </tr>
              <tr>
                <td>递延收益</td>
                <td  align="right">$deferred_revenue1</td>
                <td  align="right">$deferred_revenue2</td>
             </tr>
              <tr>
                <td>其他非流动负债</td>
                <td  align="right">$other_non_current_liabilities1</td>
                <td  align="right">$other_non_current_liabilities2</td>
             </tr>
             <tr>
                <td></td>
                <td style="font-family:SimHei;font-weight:bold;">其他非流动负债 （共）</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$other_non_current_liabilities_all1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$other_non_current_liabilities_all2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>TOTAL NON CURRENT LIABILITIES</td>
                <td style="font-family:SimHei;font-weight:bold;">非流动负债 （共）</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_non_current_liabilities1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_non_current_liabilities2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>TOTAL LIABILITIES </td>
                <td style="font-family:SimHei;font-weight:bold;">负债 （共）</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_liabilities1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_liabilities2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>TOTAL EQUITY</td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Capital and reserves attributable to equity holders of the Company</td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Ordinary Share Capital</td>
                <td>实收资本（或股本）</td>
                <td  align="right">$ordinary_share_capital1</td>
                <td  align="right">$ordinary_share_capital2</td>
             </tr>
              <tr>
                <td>Preference Shareholders</td>
                <td>优先股 （资本部分）</td>
                <td  align="right">$preferred_share_holders1</td>
                <td  align="right">$preferred_share_holders2</td>
             </tr>
              <tr>
                <td>Share Premium</td>
                <td>资本公积</td>
                <td  align="right">$share_premium1</td>
                <td  align="right">$share_premium2</td>
             </tr>
             <tr>
                <td>Share Capital</td>
                <td>股本</td>
                <td  align="right">$share_capital1</td>
                <td  align="right">$share_capital2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td rowspan="4">Other Reserves</td>
                <td>其他综合收益</td>
                <td  align="right">$other_comprehensive_income1</td>
                <td  align="right">$other_comprehensive_income2</td>
             </tr>
             <tr>
                <td>专项储备</td>
                <td  align="right">$special_reserves1</td>
                <td  align="right">$special_reserves2</td>
             </tr>
             <tr>
                <td  >盈余公积</td>
                <td  align="right" >$surplus_reserves1</td>
                <td  align="right" >$surplus_reserves2</td>
             </tr>
              <tr>
                <td></td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$other_reserves1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$other_reserves2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Retained Earnings</td>
                <td>未分配利润</td>
                <td  align="right">$retained_eranings1</td>
                <td  align="right">$retained_eranings2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>TOTAL EQUITY</td>
                <td style="font-family:SimHei;font-weight:bold;">股本加利润（年利润加累计利润）</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_equity1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_equity2</td>
             </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td>TOTAL EQUITY AND LIABILITIES</td>
                <td style="font-family:SimHei;font-weight:bold;">股本加利润（年利润加累计利润）加负债（共）</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_equity_and_liabilities1</td>
                <td  align="right" style="font-family:SimHei;font-weight:bold;">$total_equity_and_liabilities2</td>
             </tr>
             <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
             <tr>
                <td></td>
                <td>CHECK</td>
                <td align="right">$check1</td>
                <td align="right">$check2</td>
             </tr>
             <tr>
                <td>for consolidated balance sheet only</td>
                <td></td>
                <td></td>
                <td></td>
             </tr>
              <tr>
                <td>Non-Controlling Interest</td>
                <td>少数股东权益</td>
                <td align="right">$non_controlling_interest1</td>
                <td align="right">$non_controlling_interest2</td>
             </tr>
        </table>
        EOD;

        $this->pdf->writeHTML($html, true, false, true, false, '');

        // 提交人
        $this->signature();


        // 输出 PDF
        $filename = time().Str::random(16).'.pdf';
        $directory = storage_path('app/public/gsp/report/');
        if (!is_dir($directory)) {
            mkdir($directory, 0777, true); // 递归创建目录
        }
        $file = $directory.$filename;
        $this->pdf->Output( $file, 'F');

        // 生成文件验签
        $token = DocService::generateToken('/gsp/report/'.$filename, 'gsp_form', 3600, $this->fileName);
        return $token;

    }


    /**
     * 表格单位
     * @return void
     */
    public function setTableUnit()
    {
        $html = '';
        if ($this->desc) {
            // 描述
            $html .='<div style="text-align: left">'.$this->desc.'</div>';

        }

        if ($this->unit) {
            $html .='<div style="text-align: right">'.$this->unit.'</div>';
        }

        return $html;

    }

    /**
     * 表格尾部声明
     * @return void
     */
    public function setTableFooter()
    {

        $html = '';
        if ($this->statement) {

            if ($this->isChkStatement == 1) {
                $chk_img = storage_path('pdf/gsp_template/checkbox.png');
                if (!file_exists(public_path('storage/checkbox.png'))) {
                    copy($chk_img, public_path('storage/checkbox.png'));
                }
               
                $image = public_path('storage/checkbox.png');
                $html .= '<img src="'.$image.'" width="10" height="10">';
            } else {
                $html .= '<input type="checkbox"  name="option1" value="1">';
            }
            $html.= $this->statement;

        }

        return $html;

    }

    /**
     * 底部描述
     * @param $tableWidth
     * @return void
     */
    public function setBottom()
    {
        $html = '';
        if ($this->bottom) {

            // 描述
            $html = "<div style='text-align: left'>".$this->bottom."</div>";

        }

        return $html;
    }

    // 底部签名盖章
    public function signature()
    {
        // 提交人
        $this->pdf->setXY(100, $this->pdf->GetY()+8);
        $this->pdf->Cell(0, 10, '提交人:', 0, false, 'C', 0, '', 0, false, 'T', 'M');
        $this->pdf->setXY(130, $this->pdf->GetY()+8);
        $this->pdf->Cell(0, 10, '(公司盖章)', 0, false, 'C', 0, '', 0, false, 'T', 'M');
        $year = date('Y' , strtotime($this->datetime));
        $month = date('m' , strtotime($this->datetime));
        $day = date('d' , strtotime($this->datetime));
        $this->pdf->setXY(130, $this->pdf->GetY()+8);
        $this->pdf->Cell(0, 10, '填写日期：'.$year.' 年'.$month.'月'.$day.'日', 0, false, 'C', 0, '', 0, false, 'T', 'M');

        // 设置页脚位置
        $this->pdf->SetAutoPageBreak(false);
        $this->pdf->SetTextColor(128, 128, 128);
        $this->pdf->SetY(-10);
        $this->pdf->Cell(0, 10, $this->lastModifyDate, 0, false, 'C', 0, '', 0, false, 'T', 'M');


    }

    /**
     * 页脚内容
     * @return void
     */
    public function footer()
    {
        // 设置页脚字体
        $this->pdf->SetFont('GB', '', 8);
        // 设置页脚文本颜色为灰色
        $this->pdf->SetTextColor(128, 128, 128);
        // 禁用自动分页
        $this->pdf->SetAutoPageBreak(false);

        // 设置页脚内容
        $footerText = $this->lastModifyDate;

        // 设置页脚位置
        $this->pdf->SetY(-10); // 距离页面底部的距离
        $this->pdf->Cell(0, 10, $this->mbConvertEncoding($footerText), 0, 0, 'C'); // 居中对齐
    }


    /**
     * Method mbConvertEncoding
     * 中文转编码
     *
     * @param $text $text [explicite description]
     */
    private function mbConvertEncoding($text)
    {
        return mb_convert_encoding($text, 'GBK', 'UTF-8');
    }

    /**
     * 判断文本是否图片并返回url
     * @param $text
     * @return array|string[]
     */
    private function isImage($text)
    {
        $pattern = '/<img[^>]+src="([^"]+)"[^>]*>/i';
        preg_match_all($pattern, $text, $matches);

        return $matches[1] ?? [];
    }

    /**
     * 将png转成jpg
     * @param $pngFilePath
     * @return array|string|string[]
     */
    private function pngToJpg($pngFilePath)
    {
        $jpgFilePath = str_replace('.png', '.jpg', $pngFilePath);

        // 检查 PNG 文件是否存在
        if (!file_exists($pngFilePath)) {
            echo "PNG file not found: $pngFilePath";
            exit;
        }

        $imageData = file_get_contents($pngFilePath);
        $image = imagecreatefromstring($imageData);
        if (!$image) {
            echo "Failed to load PNG image: $pngFilePath";
            exit;
        }

        // 将图片保存为 JPEG 格式
        if (!imagejpeg($image, $jpgFilePath, 90)) {
            echo "Failed to save JPEG image: $jpgFilePath";
            imagedestroy($image);
            exit;
        }

        // 释放图片资源
        imagedestroy($image);

        return $jpgFilePath;
    }


    /**
     * 设置pdf权限
     * @param $source
     * @return void
     * @throws \setasign\Fpdi\PdfParser\CrossReference\CrossReferenceException
     * @throws \setasign\Fpdi\PdfParser\Filter\FilterException
     * @throws \setasign\Fpdi\PdfParser\PdfParserException
     * @throws \setasign\Fpdi\PdfParser\Type\PdfTypeException
     * @throws \setasign\Fpdi\PdfReader\PdfReaderException
     */
    private function setAuth($source)
    {
        $pdf = new TCPDF1();

        // 加载现有的 PDF 文件
        $pageCount = $pdf->setSourceFile($source);

        for ($pageNo = 1; $pageNo <= $pageCount; $pageNo++) {
            $templateId = $pdf->importPage($pageNo);
            $pdf->AddPage();
            $pdf->useTemplate($templateId);

        }

        // 设置保护功能
        $pdf->setProtection(['print', 'copy'],'', '', false);

        // 输出 PDF 文件
        $pdf->Output($source, 'F');
    }

}