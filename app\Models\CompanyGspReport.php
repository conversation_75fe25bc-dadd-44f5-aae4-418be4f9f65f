<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CompanyGspReport extends Model
{
    protected $table = 'cna_gsp_report';
    protected $guarded = [];

    // 文件审核状态
    const STATUS_FILE_NO = 0; // 未审核
    const STATUS_FILE_SUCCESS = 1; // 审核通过
    const STATUS_FILE_FAIL = 2; // 审核不通过
    const STATUS_FILE_DISCARD = 3; // 废弃

    // 企业审核状态
    const COMPANY_STATUS_NO = 0; // 未审核
    const COMPANY_STATUS_SUCCESS = 1; // 审核通过
    const COMPANY_STATUS_FAIL = 2; // 审核不通过

    const COMPANY_STATUS_ARR = [
        self::COMPANY_STATUS_SUCCESS,
        self::COMPANY_STATUS_FAIL,
    ];

    public function profile()
    {
        return $this->belongsTo(ProfileInfoModel::class, 'profile_id', 'profileID');
    }
}
