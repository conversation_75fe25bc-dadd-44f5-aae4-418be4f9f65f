<?php

namespace App\Http\Controllers\Api\v1\Interview;

use App\Http\Controllers\Controller;
use App\Jobs\SetPartnerInterviewQuestions;
use App\Models\AiCheck;
use App\Models\PartnerInterviewModel;
use App\Models\PartnerInterviewQuestionsModel;
use Carbon\Carbon;
use GatewayWorker\Lib\Gateway;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
class InterviewController extends Controller
{
    public  function show()
    {

    }




    // 预约面试
    public function appointment(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'interview_start_at' => 'required|date|date_format:Y-m-d H:i:s|after:now',
            'interview_end_at' => 'required|date|date_format:Y-m-d H:i:s|after:interview_start_at',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => $validator->errors(),
                'status' => false,
            ]);
        }

        $user = $request->attributes->get('user');
        $profileID = $user['profileID'];
//        $profileID = $request->input('profileID');
        $interview_start_at = $request->input('interview_start_at');
        $interview_end_at = $request->input('interview_end_at');

        $interviewInfo = PartnerInterviewModel::checkInterview($profileID);

        if (!is_null($interviewInfo['latestInterview'])) {
            $interviewStatus = $interviewInfo['latestInterview']->status;
            $latestInterview_id = $interviewInfo['latestInterview']->id;

            if ($interviewStatus == 0) {
                if ($interviewInfo['latestInterview']->interview_start_at->greaterThanOrEqualTo(Carbon::now())) {
                    return response()->json([
                        'code' => 403,
                        'message' => '您已预约面试，请勿重复预约',
                        'status' => true,
                        'data' => $interviewInfo['latestInterview']
                    ]);
                } else {
                    // 修改面试时间
                    PartnerInterviewModel::where('id', $latestInterview_id)
                        ->update([
                            'interview_start_at' => $interview_start_at,
                            'interview_end_at' => $interview_end_at,
                        ]);
                    return response()->json([
                        'code' => 200,
                        'message' => '面试信息修改成功',
                        'status' => true
                    ]);
                }

            } elseif ($interviewStatus == 1) {
                return response()->json([
                    'code' => 403,
                    'message' => '您已经通过面试，请勿重复预约面试',
                    'status' => true,
                    'data' => $interviewInfo['latestInterview']
                ]);
            } elseif ($interviewStatus == 2) {
                return response()->json([
                    'code' => 403,
                    'message' => '面试审核中，请耐心等待',
                    'status' => true,
                    'data' => $interviewInfo['latestInterview']
                ]);
            }
        }

        if ($interviewInfo['absent_count'] >= 3) {
            return response()->json([
                'code' => 403,
                'message' => '您已缺席超过3次，无法再预约面试',
                'status' => true
            ]);
        }

        if ($interviewInfo['fail_count'] >= 3) {
            return response()->json([
                'code' => 403,
                'message' => '您已面试失败超过3次，无法再预约面试',
                'status' => true
            ]);
        }
        // 保存面试者信息到数据库
        $token = Str::uuid();
        $interview = PartnerInterviewModel::create([
            'profileID' => $profileID,
            'token' => $token,
            'interview_start_at' => $interview_start_at,
            'interview_end_at' => $interview_end_at,
        ]);

        // 触发异步任务生成面试题
        SetPartnerInterviewQuestions::dispatch($interview);

        return response()->json([
            'code' => 200,
            'message' => '面试信息提交成功',
            'status' => true,
            'data' => $interview->token
        ]);

    }



    // 面试状态
    public function interviewStatus(Request $request)
    {
        $user = $request->attributes->get('user');
        $profileID = $user['profileID'];
//        $profileID = $request->input('profileID');

        if (!$profileID) {
            return response()->json(['message' => '未登录'], 401);
        }

        $interviewStatus = PartnerInterviewModel::checkInterview($profileID);

        if (!$interviewStatus['latestInterview']) {
            return response()->json([
                'code' => 402,
                'message' => '您还未预约过面试',
                'status' => true
            ]);
        }


        // 状态返回描述
//        $statusMap = [
//            '-2'   =>  '缺席',
//            '-1'   =>  '面试未通过',
//            '0'    =>  '已预约',
//            '1'    =>  '面试通过'
//            '2'    =>  '等待审核'
//        ];


        $interview_status = [
            'status' => $interviewStatus['latestInterview']->status,
            'token' => $interviewStatus['latestInterview']->token,
            'absent_count' => $interviewStatus['absent_count'],
            'fail_count' => $interviewStatus['fail_count'],
            'interview_start_at' => $interviewStatus['latestInterview']->interview_start_at,
            'interview_end_at' => $interviewStatus['latestInterview']->interview_end_at,
        ];
        return response()->json([
            'code' => 200,
            'status' => true,
            'data' => $interview_status
        ]);
    }






    public function attendInterview(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required|exists:cna_interviews_info,token',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => $validator->errors(),
                'status' => false,
            ]);
        }


        $token = $request->input('token');
        $interview = PartnerInterviewModel::where('token', $token)
            ->where('status', 0)
            ->first();

        if (!$interview) {
            return response()->json([
                'code' => 400,
                'message' => '没有面试记录',
                'status' => false
            ]);
        }

        // 获取面试问题
        $result = PartnerInterviewQuestionsModel::where('interview_id', $interview->id)
            ->get();

        if (!$result) {
            return response()->json([
                'code' => 404,
                'message' => '没有找到面试问题',
                'status' => false
            ]);
        }

        $statusKey = "interview_{$interview->id}_status";
        $status = Redis::get($statusKey);

        if ($status == 'finish') {
            return response()->json([
                'code' => 404,
                'message' => '面试已结束',
                'status' => false
            ]);
        } elseif ($status == 'interviewing') {
            return response()->json([
                'code' => 200,
                'message' => '实时问答环节进行中',
                'status' => true
            ]);
        }

        foreach ($result as $val) {
            // 转成音频
            $res = AiCheck::textToSpeech($val['question']);
            if (!$res['status']) {
                return response()->json([
                    'code' => 404,
                    'status' => false,
                    'message' => '音频生成失败'
                ]);
            }

            // 存储问题的 ID 和音频链接到 Redis
            $questionData = [
                'question_id' => $val['id'],  // 问题 ID
                'question_text' => $val['question'],  // 问题文本
                'question_url' => $res['data']['url'],  // 问题音频链接
            ];
            // 将问题队列保存到 Redis
            Redis::lpush("interview_{$interview->id}_questions", json_encode($questionData));
            // 设置当前面试状态
            Redis::set("interview_{$interview->id}_status", 'attend');
        }

        // 准备面试结束语
//        $peroration_text = PartnerInterviewModel::getPeroration();
//        $peroration_audio = AiCheck::textToSpeech($peroration_text);
//        $perorationData = [
//            'peroration_text' => $peroration_text,  // 文本
//            'peroration_url' => $peroration_audio['data']['url'],  // 音频链接
//        ];
//        Redis::set("interview_{$interview->id}_peroration", json_encode($perorationData));

        return response()->json([
            'code' => 200,
            'message' => '面试开始',
            'status' => true,
            'data' => $interview['token']
        ]);
    }


    // 开始面试
    public function start(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required|exists:cna_interviews_info,token',
            'clientId' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => $validator->errors(),
                'status' => false,
            ]);
        }


        $token = $request->input('token');
        $clientId = $request->input('clientId');

        $interview = PartnerInterviewModel::where('token', $token)
            ->where('status', 0)
            ->first();
        if (!$interview) {
            Gateway::sendToClient($clientId, json_encode([
                'type' => 'error',
                'questionData' => '',
                'message' => '找不到面试记录。'
            ]));
            return response()->json([
                'code' => 404,
                'message' => '找不到面试记录。',
                'status' => false,
            ]);
        }

        $questionData = Redis::rpop("interview_{$interview->id}_questions");

        if ($questionData) {
            Redis::set("interview_{$interview->id}_status", 'interviewing');
            $type = 'interviewing';
            $code = 200;
            $status = true;
            $message = '进入问答环节，已给出第一道面试题。';
        } else {
            Redis::set("interview_{$interview->id}_status", 'finish');
            $type = 'finish';
            $code = 200;
            $status = true;
            $message = '没有更多问题，面试结束。';
        }

        Gateway::sendToClient($clientId, json_encode([
            'type' => $type,
            'questionData' => $questionData,
            'message' => $message
        ]));
        return response()->json([
            'code' => $code,
            'message' => $message,
            'status' => $status
        ]);
    }


    // 接收语音回答，并转成文本
    public function submitAnswer(Request $request)
    {
        $startTotal = microtime(true); // 总体开始时间

        // Step 1：验证请求数据
        $validator = Validator::make($request->all(), [
            'token' => 'required|exists:cna_interviews_info,token',
            'question_id' => 'required|exists:cna_interviews_questions,id',
            'audio' => 'required|file|mimes:wav,webm,mp3|max:10240',
        ]);


        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => $validator->errors(),
                'status' => false,
            ]);
        }

        $clientId = $request->input('clientId');
        $token = $request->input('token');
        $question = $request->input('question');
        $question_id = $request->input('question_id');

        $interview = PartnerInterviewModel::where('token', $token)
            ->where('status', 0)
            ->first();

        if (!$interview) {
            Gateway::sendToClient($clientId, json_encode([
                'type' => 'error',
                'questionData' => '',
                'message' => '找不到面试记录。'
            ]));
            return response()->json([
                'code' => 404,
                'message' => '找不到面试记录。',
                'status' => false,
            ]);
        }

        // Step 2：上传音频
        // 2.1 获取上传的音频文件
        $audioFile = $request->file('audio');
        // 2.2 存储文件到 public/audios 目录下
        $audioFilePath = $audioFile->storeAs('public/audios', uniqid('audio_', true) . '.' . $audioFile->getClientOriginalExtension());
        $audioUrl = asset('storage/audios/' . basename($audioFilePath));
        // Step 3：开始转写耗时统计
        $startTranscribe = microtime(true);
        // 调用阿里模型获取文本
//        $answer = AiCheck::speechToText(url($audioUrl));

        // 3.1 调用转写 API 获取文本
        $answer = AiCheck::testToText($audioFile);
        $endTranscribe = microtime(true);

        if (!$answer['status']) {
            Log::warning('音频转文本失败：' . ($answer['message'] ?? '未知错误'));

            return response()->json([
                'code' => $answer['code'],
                'message' => '音频转文本失败：' . ($answer['message'] ?? '请稍后再试'),
                'status' => false,
            ]);
        }

        // Step 4：评分 + 下一题获取耗时统计
        $startScoring = microtime(true);

        // 4.1 获取回答评分
        $res = PartnerInterviewModel::getScore($question, $answer['text']);
        // 传出下一题并将回答存入 Redis
        $response = PartnerInterviewModel::getNextQuestion($res['question'],$res['answer'], $res['score'],$audioUrl, $question_id, $interview->id);

        $endScoring = microtime(true);

        // Step 5：处理完毕时间
        $endTotal = microtime(true);

        // Step 6：输出日志
        $timing = [
            'transcribe_time' => round($endTranscribe - $startTranscribe, 3) . ' s',
            'scoring_time'    => round($endScoring - $startScoring, 3) . ' s',
            'total_time'      => round($endTotal - $startTotal, 3) . ' s',
        ];
        Log::info('面试答题耗时记录', $timing);

        if (is_null($response['nextQuestion'])) {
            Redis::set("interview_{$interview->id}_status", 'finish');
            $type = 'finish';
            $message = '没有更多问题，面试结束。';
            $code = 200;

            // 如果没有更多问题，传出结束语
//            $perorationData = Redis::get("interview_{$interview->id}_peroration");
//            $peroration =  json_decode($perorationData, true);
//            $response['nextQuestion'] = [
//                'peroration_text' => $peroration['peroration_text'],
//                'peroration_url' => $peroration['peroration_url']
//            ];

            $response['nextQuestion'] = [
                'question_text' => "非常感谢您参加今天的面试。本次面试结束后，请您在3个工作日内登录系统查看面试结果，我们也会同步通过邮件通知您。若有任何问题，欢迎随时与我们联系。",
                'question_url' => 'https://api.testai.corporate-advisory.cn/storage/tool/audio/xCIOGfc9.mp3'
            ];


        } else {
            $type = 'interviewing';
            $message = '实时问答环节进行中';
            $code = 200;

        }

        Gateway::sendToClient($clientId, json_encode([
            'type' => $type,
            'score' => $res['score'],
            'questionData' => $response['nextQuestion'],
            'message' => $message
        ]));
        return response()->json([
            'code' => $code,
            'score' => $res['score'],
            'message' => $message,
            'status' => true
        ]);

    }

    // 面试结束，记录面试结果
    public function finish(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required|exists:cna_interviews_info,token',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => $validator->errors(),
                'status' => false,
            ]);
        }


        $token = $request->input('token');
        $interview = PartnerInterviewModel::where('token', $token)
            ->where('status', 0)
            ->first();
        if (!$interview) {
            return response()->json([
                'code' => 404,
                'message' => '找不到面试记录。',
                'status' => false,
            ]);
        }

        $statusKey = "interview_{$interview->id}_status";
        $status = Redis::get($statusKey);

        if (empty($status)) {
            return false;
        }

        if ($status != 'finish') {
            return response()->json([
                'code' => 404,
                'message' => '面试还没结束',
                'status' => false
            ]);
        }


        PartnerInterviewQuestionsModel::saveAnswers($interview->id);

        return response()->json([
            'code' => 200,
            'message' => '面试结束,等待审核。',
            'status' => true,
        ]);
    }



    private function jsonResponse($code, $message, $status, $data = null)
    {
        return response()->json([
            'code' => $code,
            'message' => $message,
            'status' => $status,
            'data' => $data,
        ]);
    }



    public function cleanRedis(Request $request,$token)
    {
        $validator = Validator::make(
            ['token' => $token],
            [
                'token' => 'required|exists:cna_interviews_info,token',
            ]
        );


        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => $validator->errors(),
                'status' => false,
            ]);
        }

        $interview = PartnerInterviewModel::where('token', $token)->first();


        $status = Redis::get("interview_{$interview->id}_status");
        $question = Redis::rpop("interview_{$interview->id}_questions");
        $answer = Redis::rpop("interview_{$interview->id}_answers");
        $peroration = Redis::get("interview_{$interview->id}_peroration");
        echo $status . PHP_EOL;
        print_r($question);
        print_r($answer);
        print_r($peroration);
        Redis::del("interview_{$interview->id}_peroration");
        return Redis::del("interview_{$interview->id}_status");
    }

    public function checkSpeechToText(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'audio' => 'required|file|mimes:wav,webm,mp3|max:10240',
        ]);


        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => $validator->errors(),
                'status' => false,
            ]);
        }

        $audioFile = $request->file('audio');
        $audioFilePath = $audioFile->storeAs('public/audios', uniqid('audio_', true) . '.' . $audioFile->getClientOriginalExtension());
        $audioUrl = asset('storage/audios/' . basename($audioFilePath));
//        $audioUrl = "https://tts.corporate-advisory.cn/sound/368963e0-a505-4f80-a6fd-056b58b9a640.mp3";
        $answer = AiCheck::speechToText(url($audioUrl));
        return $answer;
    }


}
